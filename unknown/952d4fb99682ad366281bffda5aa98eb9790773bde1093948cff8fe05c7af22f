//
//  WheelConfigTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 大转盘配置功能测试视图
 * 用于测试大转盘配置的保存、加载、更新功能
 */
struct WheelConfigTestView: View {
    
    @EnvironmentObject var dataManager: DataManager
    
    @State private var showWheelConfig = false
    @State private var selectedMember: Member?
    @State private var testResults: [String] = []
    @State private var isRunningTests = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("大转盘配置功能测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 成员选择
                memberSelectionSection
                
                // 测试按钮组
                testButtonsSection
                
                // 测试结果显示
                testResultsSection
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationBarHidden(true)
        }
        .overlay(
            // 大转盘配置弹窗
            WheelConfigPopupView(
                isPresented: $showWheelConfig,
                selectedMember: selectedMember,
                onSave: { configData in
                    handleConfigSave(configData)
                },
                onCancel: {
                    showWheelConfig = false
                    addTestResult("❌ 用户取消了配置")
                }
            )
        )
    }
    
    // MARK: - 子视图组件
    
    /**
     * 成员选择区域
     */
    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("选择测试成员")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            if dataManager.members.isEmpty {
                Text("请先在首页添加成员")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding()
                    .background(Color(hex: "#f8ffe5"))
                    .cornerRadius(8)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(dataManager.members, id: \.objectID) { member in
                            memberCard(member: member)
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
        }
    }
    
    /**
     * 成员卡片
     */
    private func memberCard(member: Member) -> some View {
        Button(action: {
            selectedMember = member
            addTestResult("✅ 选择成员: \(member.displayName)")
        }) {
            VStack(spacing: 8) {
                Image(member.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                
                Text(member.displayName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                
                Text("\(member.currentPoints)分")
                    .font(.system(size: 10))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                selectedMember?.objectID == member.objectID ? 
                Color(hex: "#a9d051").opacity(0.2) : Color(hex: "#f8ffe5")
            )
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        selectedMember?.objectID == member.objectID ? 
                        Color(hex: "#a9d051") : Color.clear, 
                        lineWidth: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /**
     * 测试按钮区域
     */
    private var testButtonsSection: some View {
        VStack(spacing: 12) {
            Text("功能测试")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(spacing: 8) {
                testButton(
                    title: "打开配置界面",
                    icon: "gear",
                    action: openConfigPopup
                )
                
                testButton(
                    title: "检查现有配置",
                    icon: "doc.text.magnifyingglass",
                    action: checkExistingConfig
                )
                
                testButton(
                    title: "删除配置",
                    icon: "trash",
                    action: deleteConfig
                )
                
                testButton(
                    title: "清空测试结果",
                    icon: "clear",
                    action: clearTestResults
                )
            }
        }
        .disabled(selectedMember == nil || isRunningTests)
    }
    
    /**
     * 测试按钮
     */
    private func testButton(title: String, icon: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(.white)
                
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(Color(hex: "#a9d051"))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /**
     * 测试结果区域
     */
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        testResultRow(index: index, result: result)
                    }
                }
            }
            .frame(maxHeight: 200)
            .background(Color(hex: "#f5f5f5"))
            .cornerRadius(8)
        }
    }

    /**
     * 测试结果行
     */
    private func testResultRow(index: Int, result: String) -> some View {
        let textColor = getResultColor(for: result)

        return Text("\(index + 1). \(result)")
            .font(.system(size: 12, design: .monospaced))
            .foregroundColor(textColor)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
    }

    /**
     * 获取结果文本颜色
     */
    private func getResultColor(for result: String) -> Color {
        if result.hasPrefix("✅") {
            return .green
        } else if result.hasPrefix("❌") {
            return .red
        } else if result.hasPrefix("📥") {
            return .blue
        } else {
            return DesignSystem.Colors.textSecondary
        }
    }
    
    // MARK: - 测试方法
    
    /**
     * 打开配置弹窗
     */
    private func openConfigPopup() {
        guard selectedMember != nil else {
            addTestResult("❌ 请先选择成员")
            return
        }
        
        showWheelConfig = true
        addTestResult("📱 打开大转盘配置界面")
    }
    
    /**
     * 检查现有配置
     */
    private func checkExistingConfig() {
        guard let member = selectedMember else {
            addTestResult("❌ 请先选择成员")
            return
        }
        
        if let config = DataManager.shared.getWheelConfig(for: member) {
            addTestResult("📥 找到现有配置: 分区数=\(config.itemCount), 积分=\(config.costPerPlay)")
            
            let items = config.allItems
            for item in items {
                addTestResult("   - 分区\(item.itemIndex + 1): \(item.formattedPrizeName)")
            }
        } else {
            addTestResult("📝 未找到现有配置")
        }
    }
    
    /**
     * 删除配置
     */
    private func deleteConfig() {
        guard let member = selectedMember else {
            addTestResult("❌ 请先选择成员")
            return
        }
        
        let success = DataManager.shared.deleteWheelConfig(for: member)
        if success {
            addTestResult("✅ 配置删除成功")
        } else {
            addTestResult("❌ 配置删除失败")
        }
    }
    
    /**
     * 清空测试结果
     */
    private func clearTestResults() {
        testResults.removeAll()
    }
    
    /**
     * 处理配置保存
     */
    private func handleConfigSave(_ configData: WheelConfigData) {
        guard let member = selectedMember else { return }
        
        showWheelConfig = false
        
        let savedConfig = DataManager.shared.saveWheelConfig(
            for: member,
            sectorCount: configData.sectorCount,
            costPerPlay: configData.costPerPlay,
            sectorPrizes: configData.sectorPrizes
        )
        
        if savedConfig != nil {
            addTestResult("✅ 配置保存成功")
            addTestResult("   - 分区数: \(configData.sectorCount)")
            addTestResult("   - 消耗积分: \(configData.costPerPlay)")
            addTestResult("   - 奖品: \(configData.sectorPrizes.joined(separator: ", "))")
        } else {
            addTestResult("❌ 配置保存失败")
        }
    }
    
    /**
     * 添加测试结果
     */
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter().apply {
            $0.dateFormat = "HH:mm:ss"
        }.string(from: Date())
        
        testResults.append("[\(timestamp)] \(result)")
    }
}

extension DateFormatter {
    func apply(_ closure: (DateFormatter) -> Void) -> DateFormatter {
        closure(self)
        return self
    }
}

#Preview {
    WheelConfigTestView()
        .environmentObject(DataManager.shared)
}
