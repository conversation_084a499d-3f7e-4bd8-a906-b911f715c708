# 成长日记功能使用说明

## 功能概述

成长日记功能已完全集成到ztt2项目中，支持完整的日记管理功能，包括创建、查看、编辑、删除等操作。

## 主要功能

### 1. 日记记录
- **大输入框**：提供宽敞的文本输入区域，占屏幕高度的50%
- **时间选择**：支持自定义记录时间，默认为当前时间
- **对象选择**：只显示角色为"儿子"和"女儿"的家庭成员
- **表单验证**：确保内容不为空且选择了记录对象

### 2. 历史查看
- **按成员分类**：选择特定成员查看其历史日记
- **双标签页**：
  - 分析报告：显示日记列表，支持编辑删除
  - 成长报告：显示统计信息和日记列表
- **统计信息**：显示日记总数和记录天数

### 3. 编辑删除
- **编辑功能**：点击日记条目的菜单按钮，选择"编辑"
- **删除功能**：点击日记条目的菜单按钮，选择"删除"
- **编辑表单**：支持修改日记内容和记录时间

## 使用流程

### 记录新日记
1. 进入成长日记页面
2. 在大输入框中输入日记内容
3. 点击"记录时间"按钮选择日期（可选）
4. 点击"选择对象"按钮选择孩子
5. 点击"保存日记"按钮完成保存

### 查看历史日记
1. 点击"查看历史日记"按钮
2. 选择要查看的孩子
3. 在"分析报告"或"成长报告"标签页中查看日记
4. 可以对日记进行编辑或删除操作

### 编辑日记
1. 在历史日记列表中找到要编辑的日记
2. 点击日记条目右侧的"..."菜单按钮
3. 选择"编辑"选项
4. 在弹出的编辑表单中修改内容
5. 点击"保存"按钮完成修改

### 删除日记
1. 在历史日记列表中找到要删除的日记
2. 点击日记条目右侧的"..."菜单按钮
3. 选择"删除"选项
4. 日记将被立即删除

## 技术特性

### 数据持久化
- 使用CoreData进行数据存储
- 支持数据的增删改查操作
- 自动同步数据变化到UI

### 响应式设计
- 数据变化自动更新界面
- 流畅的动画效果
- 适配不同屏幕尺寸

### 用户体验
- 友好的错误提示
- 加载状态指示
- 操作成功反馈
- 表单验证提示

## 注意事项

### 成员要求
- 只有角色为"儿子"或"女儿"的成员才会在选择列表中显示
- 如果没有符合条件的成员，会显示"还没有添加孩子"的提示
- 需要先在首页添加儿子或女儿角色的成员

### 数据安全
- 所有日记数据都存储在本地CoreData数据库中
- 删除操作不可撤销，请谨慎操作
- 编辑操作会立即保存到数据库

### 性能优化
- 日记列表按时间倒序排列，最新的在前
- 支持大量日记数据的高效显示
- 统计信息实时计算

## 开发信息

### 相关文件
- `Views/GrowthDiaryView.swift` - 主界面
- `ViewModels/GrowthDiaryViewModel.swift` - 数据管理
- `Models/DataManager.swift` - 数据操作
- `Models/CoreDataExtensions.swift` - 数据模型扩展

### 测试文件
- `Tests/GrowthDiaryViewModelTests.swift` - 单元测试

### 数据模型
- `DiaryEntry` - 日记条目实体
- `Member` - 家庭成员实体
- 支持完整的关系映射和数据验证

## 故障排除

### 常见问题
1. **选择对象为空**：确保已添加角色为"儿子"或"女儿"的成员
2. **保存失败**：检查日记内容是否为空，是否选择了记录对象
3. **历史记录不显示**：确保选择的成员有日记记录

### 调试信息
- 所有数据操作都有控制台日志输出
- 错误信息会通过弹窗提示用户
- 可以通过Xcode调试器查看详细信息

---

**版本**: 1.0
**更新时间**: 2025年7月30日
**开发者**: Claude Sonnet 4
