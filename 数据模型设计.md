

## 数据模型设计

### 核心架构
```
User (用户)
├── subscription: Subscription (一对一)
├── globalRules: [GlobalRule] (一对多)
└── members: [Member] (一对多)

Member (家庭成员)
├── pointRecords: [PointRecord] (一对多)
├── diaryEntries: [DiaryEntry] (一对多)
├── redemptionRecords: [RedemptionRecord] (一对多)
├── lotteryRecords: [LotteryRecord] (一对多)
├── aiReports: [AIReport] (一对多)
├── memberRules: [MemberRule] (一对多)
├── memberPrizes: [MemberPrize] (一对多)
└── lotteryConfigs: [LotteryConfig] (一对多)
    └── items: [LotteryItem] (一对多)
```

### 数据模型特性
- **CloudKit 同步**: 支持多设备数据同步
- **关系完整性**: 正确的删除规则和约束
- **类型安全**: 使用适当的数据类型
- **扩展性**: 易于添加新功能和字段

### 实体详细设计

#### User (用户)
- id: UUID
- nickname: String
- email: String
- appleUserID: String
- createdAt: Date
- subscription: Subscription (一对一)
- globalRules: [GlobalRule] (一对多)
- members: [Member] (一对多)

#### Subscription (订阅信息)
- id: UUID
- subscriptionType: String (free/basic/premium)
- isActive: Bool
- startDate: Date?
- endDate: Date?
- productIdentifier: String?
- createdAt: Date
- updatedAt: Date
- user: User (多对一)

#### GlobalRule (全局规则)
- id: UUID
- name: String
- value: Int32
- type: String (add/deduct)
- isFrequent: Bool
- createdAt: Date
- user: User (多对一)

#### Member (家庭成员)
- id: UUID
- name: String
- role: String (father/mother/son/daughter/other)
- birthDate: Date? (用于计算年龄)
- memberNumber: Int32 (自动编号)
- currentPoints: Int32
- avatar: String?
- createdAt: Date
- updatedAt: Date
- user: User (多对一)

#### PointRecord (积分记录)
- id: UUID
- reason: String
- value: Int32
- timestamp: Date
- recordType: String (behavior/redemption/lottery)
- isReversed: Bool
- member: Member (多对一)

#### DiaryEntry (成长日记)
- id: UUID
- content: String
- timestamp: Date
- createdAt: Date
- updatedAt: Date
- member: Member (多对一)

#### AIReport (AI报告)
- id: UUID
- title: String
- content: String
- reportType: String (analysis/growth)
- createdAt: Date
- inputDataSummary: String
- totalRecords: Int32
- positiveRecords: Int32
- negativeRecords: Int32
- member: Member (多对一)

#### MemberRule (成员规则)
- id: UUID
- name: String
- value: Int32
- type: String (add/deduct)
- isFrequent: Bool
- createdAt: Date
- member: Member (多对一)

#### MemberPrize (成员奖品)
- id: UUID
- name: String
- cost: Int32
- type: String
- createdAt: Date
- member: Member (多对一)

#### RedemptionRecord (兑换记录)
- id: UUID
- prizeName: String
- cost: Int32
- timestamp: Date
- source: String (redemption/lottery)
- member: Member (多对一)

#### LotteryRecord (抽奖记录)
- id: UUID
- toolType: String
- prizeResult: String
- cost: Int32
- timestamp: Date
- member: Member (多对一)

#### LotteryConfig (抽奖配置)
- id: UUID
- toolType: String
- itemCount: Int32
- costPerPlay: Int32
- createdAt: Date
- updatedAt: Date
- member: Member (多对一)
- items: [LotteryItem] (一对多)

#### LotteryItem (抽奖项目)
- id: UUID
- itemIndex: Int32
- prizeName: String
- createdAt: Date
- lotteryConfig: LotteryConfig (多对一)

## 实现说明

### 数据管理器 (DataManager)
提供统一的数据操作接口，包括：
- 用户和成员管理
- 积分系统操作
- 规则管理
- 奖品和抽奖系统
- 日记和AI报告管理
- 订阅状态管理
- 统计分析功能

### Core Data 扩展 (CoreDataExtensions)
为所有实体提供便利属性和方法：
- 计算属性（如年龄、积分统计）
- 关系数据的便捷访问
- 格式化显示方法
- 业务逻辑判断

### 使用示例
详见 `DataModelUsageExamples.swift` 文件，包含完整的使用示例代码。

### 测试覆盖
`DataModelTests.swift` 提供了完整的单元测试，确保数据模型的正确性和稳定性。

