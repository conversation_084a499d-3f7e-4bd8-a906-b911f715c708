<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- 应用基本信息 -->
	<key>CFBundleDevelopmentRegion</key>
	<string>zh-Hans</string>
	<key>CFBundleDisplayName</key>
	<string>转团团</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	
	<!-- 本地化支持 -->
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh-Hans</string>
		<string>en</string>
	</array>
	
	<!-- iOS版本要求 -->
	<key>LSMinimumSystemVersion</key>
	<string>15.6</string>
	
	<!-- 设备支持 -->
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- SwiftUI配置 -->
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	
	<!-- 启动屏幕 -->
	<key>UILaunchScreen</key>
	<dict>
		<key>UIImageName</key>
		<string>AppIcon</string>
		<key>UIColorName</key>
		<string>AccentColor</string>
	</dict>
	
	<!-- 应用图标 -->
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundlePrimaryIcon</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon</string>
			</array>
			<key>CFBundleIconName</key>
			<string>AppIcon</string>
		</dict>
	</dict>
	
	<!-- 网络安全配置 -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<!-- DeepSeek API -->
			<key>api.deepseek.com</key>
			<dict>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<!-- RevenueCat API -->
			<key>api.revenuecat.com</key>
			<dict>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	
	<!-- 隐私权限说明 -->
	<key>NSUserTrackingUsageDescription</key>
	<string>转团团需要此权限来提供个性化的家庭教育体验和改进应用功能。您的隐私对我们很重要，我们不会收集任何个人身份信息。</string>
	
	<!-- CloudKit配置 -->
	<key>CKSharingSupported</key>
	<true/>
	
	<!-- URL Schemes配置 -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.rainkygong.ztt1.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ztt1</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.rainkygong.ztt1.cloudkit</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>icloud-com-rainkygong-ztt1</string>
			</array>
		</dict>
	</array>
	
	<!-- 后台模式 -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>remote-notification</string>
	</array>
	
	<!-- 应用类别 -->
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.education</string>
	
	<!-- 文档类型支持 -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>CSV File</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.comma-separated-values-text</string>
			</array>
		</dict>
	</array>
	
	<!-- 导出的UTI类型 -->
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>com.rainkygong.ztt1.family-data</string>
			<key>UTTypeDescription</key>
			<string>转团团家庭数据</string>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>ztt</string>
				</array>
			</dict>
		</dict>
	</array>
	
	<!-- 应用特性 -->
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	
	<!-- 状态栏样式 -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	
	<!-- 支持的设备方向 -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	
	<!-- 应用支持的功能 -->
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- 防止应用在后台被终止 -->
	<key>UIApplicationExitsOnSuspend</key>
	<false/>
	
	<!-- 支持多任务 -->
	<key>UIApplicationSupportsMultitasking</key>
	<true/>
	
	<!-- 文件共享支持 -->
	<key>UIFileSharingEnabled</key>
	<false/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<false/>
</dict>
</plist>
