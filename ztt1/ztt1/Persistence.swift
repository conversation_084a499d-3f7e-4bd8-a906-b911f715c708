//
//  Persistence.swift
//  ztt1
//
//  Created by rainkygong on 2025/6/23.
//

import CoreData
import CloudKit

/**
 * 持久化控制器
 * 根据用户订阅等级决定是否启用CloudKit同步
 */
struct PersistenceController {
    static let shared = PersistenceController()

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        // 预览环境不创建任何模拟数据，保持干净状态
        return result
    }()

    let container: NSPersistentContainer
    private(set) var isCloudKitEnabled: Bool = true

    // MARK: - Initialization
    
    init(inMemory: Bool = false) {
        // 统一使用CloudKit容器，支持所有用户多设备同步
        container = NSPersistentCloudKitContainer(name: "ztt1")
        
        print("☁️ 配置CloudKit同步容器 - 统一多设备同步模式")
        
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        } else {
            Self.configureStoreDescription(container.persistentStoreDescriptions.first!)
        }
        
        setupContainer()
    }
    
    // MARK: - Store Configuration
    
    /**
     * 配置CloudKit存储描述
     */
    private static func configureStoreDescription(_ description: NSPersistentStoreDescription) {
        // 启用CloudKit同步相关配置
        description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        // CloudKit 容器配置
        description.setOption("iCloud.com.rainkygong.ztt1" as NSString, 
                            forKey: "containerIdentifier")
        
        // 使用默认存储位置
        description.url = defaultStoreURL()
    }
    
    /**
     * 默认存储URL（CloudKit同步）
     */
    private static func defaultStoreURL() -> URL {
        return NSPersistentContainer.defaultDirectoryURL()
            .appendingPathComponent("ztt1.sqlite")
    }
    
    // MARK: - Container Setup
    
    private func setupContainer() {
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                print("❌ CoreData加载失败: \(error)")
                print("Error info: \(error.userInfo)")
                
                #if DEBUG
                fatalError("Unresolved error \(error), \(error.userInfo)")
                #else
                // 生产环境中的错误处理
                self.handleStoreLoadError(error)
                #endif
            } else {
                print("✅ CoreData加载成功 - 模式: CloudKit多设备同步")
                
                // 配置视图上下文
                self.configureViewContext()
            }
        })
    }
    
    /**
     * 配置视图上下文
     */
    private func configureViewContext() {
        // 自动合并来自父上下文的更改
        container.viewContext.automaticallyMergesChangesFromParent = true
        
        // 配置合并策略
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        // CloudKit同步配置
        container.viewContext.automaticallyMergesChangesFromParent = true
    }
    
    /**
     * 处理存储加载错误
     */
    private func handleStoreLoadError(_ error: NSError) {
        // 在生产环境中，可以尝试重置存储或提供用户选项
        print("🔄 尝试恢复存储...")
        // 这里可以添加更复杂的错误恢复逻辑
    }
}

// MARK: - Subscription Level Extension

extension Subscription.Level {
    /**
     * 所有用户都支持多设备同步
     */
    var supportsMultiDeviceSync: Bool {
        return true // 统一支持CloudKit同步
    }
}
