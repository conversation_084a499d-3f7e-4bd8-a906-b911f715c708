//
//  SubscriptionService.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/20.
//  Updated to use RevenueCat for real subscription management
//

import Foundation
import RevenueCat
import Combine

/**
 * 订阅服务
 * 提供订阅相关的核心业务逻辑和数据管理
 * 与RevenueCat SDK集成，处理订阅状态同步
 */
@MainActor
class SubscriptionService: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = SubscriptionService()
    
    // MARK: - Published Properties
    @Published var currentSubscriptionLevel: Subscription.Level = .free
    @Published var expirationDate: Date?
    @Published var customerInfo: CustomerInfo?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Product IDs (与RevenueCatManager保持一致)
    enum ProductIds {
        static let monthlyBasic = "com.ztt1.subscription.monthly.basic"
        static let yearlyBasic = "com.ztt1.subscription.yearly.basic"
        static let monthlyPremium = "com.ztt1.subscription.monthly.premium"
        static let yearlyPremium = "com.ztt1.subscription.yearly.premium"
    }
    
    // MARK: - Private Properties
    private let revenueCatManager = RevenueCatManager.shared
    private let coreDataManager = CoreDataManager.shared
    private let classManagementService = ClassManagementService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // 记录订阅状态变更事件
    private var previousLevel: String?
    
    // MARK: - Initialization
    private init() {
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /**
     * 配置订阅服务
     */
    func configure(userId: String? = nil) {
        // RevenueCat已在App启动时配置，这里主要是同步状态
        if let userId = userId {
            identify(userId: userId)
        }
        
        // 注册订阅状态变更观察者
        setupSubscriptionObservers()
        
        // 从RevenueCat获取订阅数据
        loadSubscriptionFromRevenueCat()
        
        // isInitialized = true // This line was removed as per the new_code
    }
    
    /**
     * 设置用户标识
     */
    func identify(userId: String) {
        isLoading = true
        
        Task {
            do {
                let (customerInfo, created) = try await Purchases.shared.logIn(userId)
                
                await MainActor.run {
                    self.customerInfo = customerInfo
                    self.isLoading = false
                    print("✅ 用户标识设置成功: \(userId), 新用户: \(created)")
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    print("❌ 用户标识设置失败: \(error)")
                }
            }
        }
    }
    
    /**
     * 购买订阅产品
     */
    func purchaseProduct(productId: String) async -> Bool {
        isLoading = true
        let success = await revenueCatManager.purchaseProduct(productId: productId)
        isLoading = false
        return success
    }
    
    /**
     * 恢复购买
     */
    func restorePurchases() async -> Bool {
        isLoading = true
        let success = await revenueCatManager.restorePurchases()
        isLoading = false
        return success
    }
    
    /**
     * 赠送试用
     */
    func grantTrialDays(_ days: Int) async -> Bool {
        return await revenueCatManager.grantPremiumTrialDays(days)
    }
    
    /**
     * 处理订阅级别变更
     */
    func handleSubscriptionLevelChange(oldLevel: String, newLevel: String) {
        previousLevel = oldLevel
        
        print("🔄 处理订阅级别变更: \(oldLevel) → \(newLevel)")
        
        let oldSubscriptionLevel = Subscription.Level(rawValue: oldLevel) ?? .free
        let newSubscriptionLevel = Subscription.Level(rawValue: newLevel) ?? .free
        
        // 检查是否为降级
        if oldSubscriptionLevel.maxClasses > newSubscriptionLevel.maxClasses {
            handleSubscriptionDowngrade(oldLevel: oldLevel, newLevel: newLevel, 
                                       oldMaxClasses: oldSubscriptionLevel.maxClasses,
                                       newMaxClasses: newSubscriptionLevel.maxClasses)
        }
        // 检查是否为升级
        else if oldSubscriptionLevel.maxClasses < newSubscriptionLevel.maxClasses {
            handleSubscriptionUpgrade(oldLevel: oldLevel, newLevel: newLevel)
        }
        
        updateSubscriptionStatus()
    }
    
    /**
     * 退出登录
     */
    func logout() {
        Task {
            do {
                _ = try await Purchases.shared.logOut()
                print("✅ 已成功退出RevenueCat登录")
            } catch {
                print("❌ RevenueCat退出登录失败: \(error)")
            }
        }
    }
    
    /**
     * 获取当前用户订阅信息
     */
    func getCustomerInfo() {
        isLoading = true
        
        Task {
            do {
                let customerInfo = try await Purchases.shared.customerInfo()
                
                await MainActor.run {
                    self.customerInfo = customerInfo
                    self.isLoading = false
                    print("✅ 获取用户信息成功")
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    print("❌ 获取用户信息失败: \(error)")
                }
            }
        }
    }
    
    /**
     * 检查特定功能权限
     */
    func checkPermission(for feature: Subscription.Feature) -> Bool {
        let revenueCatFeature: RevenueCatManager.SubscriptionFeature
        
        switch feature {
        case .basicScoring:
            revenueCatFeature = .basicScoring
        case .prizeExchange:
            revenueCatFeature = .prizeExchange
        case .lottery:
            revenueCatFeature = .lottery
        case .advancedGames:
            revenueCatFeature = .advancedGames
        case .aiAnalysis:
            revenueCatFeature = .aiAnalysis
        case .multiDeviceSync:
            revenueCatFeature = .multiDeviceSync
        }
        
        return revenueCatManager.hasFeature(revenueCatFeature)
    }
    
    /**
     * 获取当前订阅级别
     */
    func getCurrentSubscriptionLevel() -> String {
        return revenueCatManager.currentSubscriptionLevel.rawValue
    }
    
    /**
     * 获取当前订阅到期日期
     */
    func getCurrentExpirationDate() -> Date? {
        return revenueCatManager.expirationDate
    }
    
    /**
     * 设置订阅观察者
     */
    func setupSubscriptionObservers() {
        print("✅ 订阅观察者已通过RevenueCatManager设置")
    }
    
    // MARK: - Private Methods
    
    private func setupObservers() {
        revenueCatManager.$currentSubscriptionLevel
            .sink { [weak self] level in
                self?.syncSubscriptionFromRevenueCat(level: level)
            }
            .store(in: &cancellables)
        
        revenueCatManager.$customerInfo
            .sink { [weak self] customerInfo in
                self?.customerInfo = customerInfo
            }
            .store(in: &cancellables)
    }
    
    private func loadSubscriptionFromRevenueCat() {
        guard coreDataManager.getCurrentUser() != nil else { return }
        // currentSubscription = user.subscription // This line was removed as per the new_code
        syncSubscriptionFromRevenueCat(level: revenueCatManager.currentSubscriptionLevel)
    }
    
    private func syncSubscriptionFromRevenueCat(level: RevenueCatManager.SubscriptionLevel) {
        guard let user = coreDataManager.getCurrentUser() else { return }
        
        let oldLevel = user.subscription?.level ?? "free"
        let newLevel = level.rawValue
        
        if user.subscription == nil {
            user.subscription = Subscription.createFreeSubscription(for: user, in: coreDataManager.viewContext)
        }
        
        user.subscription?.level = newLevel
        user.subscription?.maxClasses = Int32(level.maxClasses)
        user.subscription?.updatedAt = revenueCatManager.expirationDate ?? Date()
        
        // currentSubscription = user.subscription // This line was removed as per the new_code
        coreDataManager.save()
        
        if oldLevel != newLevel {
            handleSubscriptionLevelChange(oldLevel: oldLevel, newLevel: newLevel)
        }
    }
    
    private func handleSubscriptionDowngrade(oldLevel: String, newLevel: String, 
                                           oldMaxClasses: Int, newMaxClasses: Int) {
        print("⬇️ 检测到会员降级: 最大班级数从 \(oldMaxClasses) 减少到 \(newMaxClasses)")
        
        guard let user = coreDataManager.getCurrentUser() else { return }
        
        let needsFreeze = classManagementService.needsClassFreezeHandling(for: user)
        if needsFreeze {
            NotificationCenter.default.post(
                name: .subscriptionDowngradeNeedsAction,
                object: nil,
                userInfo: [
                    NotificationUserInfoKey.userId: user.id?.uuidString ?? "",
                    NotificationUserInfoKey.oldLevel: oldLevel,
                    NotificationUserInfoKey.newLevel: newLevel
                ]
            )
        }
    }
    
    private func handleSubscriptionUpgrade(oldLevel: String, newLevel: String) {
        print("⬆️ 检测到会员升级")
        
        guard let user = coreDataManager.getCurrentUser() else { return }
        
        let availableUnfreezeCount = classManagementService.getAvailableUnfreezeCount(for: user)
        let frozenClasses = classManagementService.getFrozenClasses(for: user)
        
        if !frozenClasses.isEmpty && availableUnfreezeCount > 0 {
            NotificationCenter.default.post(
                name: .subscriptionUpgradeClassUnfreeze,
                object: nil,
                userInfo: [
                    NotificationUserInfoKey.userId: user.id?.uuidString ?? "",
                    NotificationUserInfoKey.oldLevel: oldLevel,
                    NotificationUserInfoKey.newLevel: newLevel,
                    NotificationUserInfoKey.availableUnfreezeCount: availableUnfreezeCount
                ]
            )
        }
    }
    
    private func updateSubscriptionStatus() {
        let currentLevel = getCurrentSubscriptionLevel()
        
        if currentLevel != previousLevel {
            NotificationCenter.default.post(name: Notification.Name.subscriptionStatusChanged, object: nil)
            print("📢 发送订阅状态变更通知: \(currentLevel)")
        }
        
        previousLevel = currentLevel
    }
}

// MARK: - Product Price Helper

extension SubscriptionService {
    
    func getProductPrice(for productId: String) -> String {
        let prices = revenueCatManager.getProductPrices()
        return prices[productId] ?? "¥0.00"
    }
    
    func getProductInfo(for productId: String) -> (title: String, description: String, price: String)? {
        return revenueCatManager.getLocalizedProductInfo(for: productId)
    }
    
    func isProductAvailable(_ productId: String) -> Bool {
        return revenueCatManager.offerings != nil
    }
}

// MARK: - Legacy Compatibility

extension SubscriptionService {
    
    func getBasicMonthlyPrice() -> String {
        return getProductPrice(for: ProductIds.monthlyBasic)
    }
    
    func getBasicYearlyPrice() -> String {
        return getProductPrice(for: ProductIds.yearlyBasic)
    }
    
    func getPremiumMonthlyPrice() -> String {
        return getProductPrice(for: ProductIds.monthlyPremium)
    }
    
    func getPremiumYearlyPrice() -> String {
        return getProductPrice(for: ProductIds.yearlyPremium)
    }
}