//
//  ContentView.swift
//  ztt1
//
//  Created by rainkygong on 2025/6/23.
//

import SwiftUI
import CoreData

/**
 * 应用主入口视图
 * 根据登录状态显示登录页面或主界面
 */
struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var authManager: AuthenticationManager
    @StateObject private var accountDeletionManager = AccountDeletionManager.shared

    @State private var showRemoteDeletionAlert = false

    var body: some View {
        ZStack {
            if authManager.isLoggedIn {
                // 已登录，显示主界面
                MainTabView()
                    .preferredColorScheme(.light) // 强制使用浅色模式
                    .environmentObject(authManager)
                    .keyboardToolbar() // 应用键盘工具栏
                    .onAppear {
                        // 检查远程删除标记
                        checkForRemoteDeletion()
                    }
            } else {
                // 未登录，显示登录页面
                LoginView(authManager: authManager)
                    .preferredColorScheme(.light) // 强制使用浅色模式
                    .keyboardToolbar() // 应用键盘工具栏
            }
        }
        .alert("账号已在其他设备删除", isPresented: $showRemoteDeletionAlert) {
            Button("确定", role: .cancel) {
                // 退出到登录页面
                authManager.logout()
            }
        } message: {
            Text("检测到您的账号已在其他设备删除，本地数据将自动清理。")
        }
        .onReceive(NotificationCenter.default.publisher(for: .accountDeleteCompleted)) { _ in
            // 监听删除完成通知
            print("📢 收到账号删除完成通知")
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 检查远程删除标记
     */
    private func checkForRemoteDeletion() {
        // 延迟检查，避免影响启动速度
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // 检查用户是否刚刚登录（避免在重新注册后立即弹出删除提示）
            guard let currentUser = authManager.currentUser,
                  let lastLoginAt = currentUser.lastLoginAt,
                  Date().timeIntervalSince(lastLoginAt) > 10.0 else {
                print("ℹ️ 用户刚刚登录，跳过删除标记检查")
                return
            }
            
            accountDeletionManager.checkForRemoteDeletion { hasRemoteDeletion in
                if hasRemoteDeletion {
                    DispatchQueue.main.async {
                        showRemoteDeletionAlert = true
                    }
                }
            }
        }
    }
}



#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
