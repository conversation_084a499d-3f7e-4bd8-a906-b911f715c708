# 试用期订阅提醒功能实现文档

## 功能概述

在用户点击个人中心页面的"查看订阅方案"按钮时，如果用户处于试用期内，会先弹出"温馨提醒"弹窗，提醒用户现在订阅会提前结束试用，建议试用期结束后再订阅。

## 功能流程

1. 用户在个人中心页面点击"查看订阅方案"按钮
2. 系统检查用户是否处于试用期内（`trialManager.isTrialActive`）
3. 如果用户处于试用期：
   - 显示"温馨提醒"弹窗
   - 弹窗内容："现在订阅会提前结束试用，建议试用期结束后再订阅"
   - 提供"谢谢提醒"按钮
   - 用户点击按钮后，关闭弹窗并跳转到订阅页面
4. 如果用户不在试用期：
   - 直接跳转到订阅页面

## 实现细节

### 1. 新增组件

#### `TrialSubscriptionReminderModal`
- 位置：`ztt1/Views/Components/TrialClaimModal.swift`
- 功能：显示试用期订阅提醒弹窗
- 特点：
  - 与现有弹窗样式保持一致
  - 使用橙色灯泡图标作为提醒标识
  - 简洁的提醒文案
  - 单一确认按钮"谢谢提醒"

### 2. 修改的文件

#### `ProfileView.swift`
- 新增状态变量：`@State private var showTrialReminderModal = false`
- 修改 `handleViewPlansPressed()` 方法：
  - 添加试用期检查逻辑
  - 根据试用状态决定显示弹窗或直接跳转
- 新增弹窗显示逻辑：
  - 使用 `.overlay()` 修饰符显示试用期提醒弹窗（避免与现有 sheet 冲突）
  - 用户确认后跳转到订阅页面

### 3. 核心逻辑

```swift
private func handleViewPlansPressed() {
    print("查看会员方案功能")
    
    // 检查用户是否处于试用期内
    if trialManager.isTrialActive {
        print("🔔 用户处于试用期内，显示温馨提醒")
        // 显示试用期提醒弹窗
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            showTrialReminderModal = true
        }
    } else {
        // 直接跳转到订阅页面
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            showSubscriptionView = true
        }
    }
    
    // 触觉反馈
    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
    impactFeedback.impactOccurred()
}
```

## 测试

### 测试视图
创建了 `TrialReminderTestView.swift` 用于测试功能：
- 位置：`ztt1/Views/Testing/TrialReminderTestView.swift`
- 功能：
  - 显示当前试用状态
  - 模拟点击"查看订阅方案"按钮
  - 直接显示试用期提醒弹窗
  - 切换测试试用状态

### 测试场景
1. **用户处于试用期**：
   - 点击"查看订阅方案"按钮
   - 应显示试用期提醒弹窗
   - 点击"谢谢提醒"后跳转到订阅页面

2. **用户不在试用期**：
   - 点击"查看订阅方案"按钮
   - 应直接跳转到订阅页面

## 用户体验

### 设计原则
- **非阻断性**：提醒用户但不阻止其订阅
- **信息明确**：清楚说明订阅对试用的影响
- **操作简单**：单一确认按钮，操作流程简洁
- **样式一致**：与应用现有弹窗样式保持一致

### 视觉设计
- 使用橙色灯泡图标表示提醒
- 标题："温馨提醒"
- 内容：简洁明了的提醒文案
- 按钮：主色调的"谢谢提醒"按钮

## 技术特点

1. **状态管理**：使用 `TrialManager.shared.isTrialActive` 检查试用状态
2. **动画效果**：与现有组件保持一致的弹簧动画
3. **触觉反馈**：提供适当的用户交互反馈
4. **代码复用**：复用现有的弹窗样式和设计系统
5. **测试友好**：提供专门的测试视图便于功能验证

## 构建状态

✅ 代码编译通过
✅ 无语法错误
✅ 与现有代码集成良好
✅ 测试视图可用
✅ 修复了 SwiftUI sheet 冲突问题（使用 overlay 替代 sheet）

## 后续优化建议

1. 可以考虑添加试用剩余天数显示
2. 可以添加更详细的订阅影响说明
3. 可以考虑添加"继续试用"选项
4. 可以添加用户行为统计（如提醒显示次数、用户选择等）
