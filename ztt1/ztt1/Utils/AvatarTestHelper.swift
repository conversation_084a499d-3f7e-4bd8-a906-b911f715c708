//
//  AvatarTestHelper.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/28.
//

import UIKit
import SwiftUI

/**
 * 头像测试辅助工具
 * 用于调试和验证头像资源是否正确加载
 */
struct AvatarTestHelper {
    
    /**
     * 测试所有头像资源是否存在
     */
    static func testAllAvatarResources() {
        print("🧪 开始测试头像资源...")
        
        let avatarNames = [
            "爸爸头像",
            "妈妈头像", 
            "男生头像",
            "女生头像",
            "其他头像"
        ]
        
        for avatarName in avatarNames {
            if let image = UIImage(named: avatarName) {
                print("✅ 头像资源存在: \(avatarName) - 尺寸: \(image.size)")
            } else {
                print("❌ 头像资源缺失: \(avatarName)")
            }
        }

        // 额外测试本地化字符串
        print("🔍 测试本地化字符串映射...")
        let maleAvatar = "gender.avatar.male".localized
        let femaleAvatar = "gender.avatar.female".localized
        print("   - gender.avatar.male -> \(maleAvatar)")
        print("   - gender.avatar.female -> \(femaleAvatar)")

        if let maleImage = UIImage(named: maleAvatar) {
            print("   ✅ 男性头像本地化映射成功 - 尺寸: \(maleImage.size)")
        } else {
            print("   ❌ 男性头像本地化映射失败")
        }

        if let femaleImage = UIImage(named: femaleAvatar) {
            print("   ✅ 女性头像本地化映射成功 - 尺寸: \(femaleImage.size)")
        } else {
            print("   ❌ 女性头像本地化映射失败")
        }

        print("🧪 头像资源测试完成")
    }
    
    /**
     * 测试FamilyRole的头像映射
     */
    static func testFamilyRoleAvatarMapping() {
        print("🧪 开始测试FamilyRole头像映射...")
        
        let roles: [FamilyRole] = [.father, .mother, .son, .daughter, .other]
        
        for role in roles {
            let avatarName = FamilyAvatarSystem.getDefaultAvatarName(for: role)
            if let image = UIImage(named: avatarName) {
                print("✅ \(role.rawValue) -> \(avatarName) - 尺寸: \(image.size)")
            } else {
                print("❌ \(role.rawValue) -> \(avatarName) - 资源缺失")
            }
        }
        
        print("🧪 FamilyRole头像映射测试完成")
    }
    
    /**
     * 测试Student的avatarImageName属性
     */
    static func testStudentAvatarImageName(student: Student) {
        print("🧪 测试Student头像: \(student.name ?? "未知")")
        
        let avatarName = student.avatarImageName
        print("   - 返回的头像名称: \(avatarName)")
        
        if let image = UIImage(named: avatarName) {
            print("   ✅ 头像资源存在 - 尺寸: \(image.size)")
        } else {
            print("   ❌ 头像资源缺失")
        }
        
        // 如果是FamilyMember适配对象，额外测试
        if student.isFamilyMemberAdapter {
            print("   - 这是FamilyMember适配对象")
            if let studentId = student.id,
               let familyMember = CoreDataManager.shared.getFamilyMember(by: studentId) {
                print("   - FamilyMember角色: \(familyMember.role ?? "未知")")
                print("   - FamilyMember avatar字段: \(familyMember.avatar ?? "nil")")
                print("   - FamilyMember性别: \(familyMember.gender ?? "未知")")

                let role = FamilyRole(rawValue: familyMember.role ?? "other") ?? .other
                print("   - 解析的角色枚举: \(role)")
                print("   - 角色对应的头像名称: \(role.defaultAvatarName)")

                let systemAvatarName = FamilyAvatarSystem.getDefaultAvatarName(for: role)
                print("   - FamilyAvatarSystem返回的头像: \(systemAvatarName)")

                let memberAvatarName = familyMember.avatarImageName
                print("   - FamilyMember.avatarImageName: \(memberAvatarName)")
            } else {
                print("   - ❌ 无法找到对应的FamilyMember")
            }
        }
    }
}

/**
 * 头像测试视图
 * 用于在界面中显示头像测试结果
 */
struct AvatarTestView: View {
    
    var body: some View {
        VStack(spacing: 20) {
            Text("头像测试")
                .font(.title)
                .padding()
            
            Button("测试所有头像资源") {
                AvatarTestHelper.testAllAvatarResources()
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
            
            Button("测试FamilyRole映射") {
                AvatarTestHelper.testFamilyRoleAvatarMapping()
            }
            .padding()
            .background(Color.green)
            .foregroundColor(.white)
            .cornerRadius(8)
            
            // 显示所有头像
            VStack(spacing: 10) {
                Text("头像预览")
                    .font(.headline)
                
                HStack(spacing: 10) {
                    ForEach(["爸爸头像", "妈妈头像", "男生头像", "女生头像", "其他头像"], id: \.self) { avatarName in
                        VStack {
                            if let uiImage = UIImage(named: avatarName) {
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 50, height: 50)
                                    .clipShape(Circle())
                            } else {
                                Circle()
                                    .fill(Color.red)
                                    .frame(width: 50, height: 50)
                                    .overlay(
                                        Text("❌")
                                            .foregroundColor(.white)
                                    )
                            }
                            
                            Text(avatarName)
                                .font(.caption)
                                .multilineTextAlignment(.center)
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding()
        .onAppear {
            AvatarTestHelper.testAllAvatarResources()
            AvatarTestHelper.testFamilyRoleAvatarMapping()
        }
    }
}

// MARK: - Preview
#Preview {
    AvatarTestView()
}
