//
//  TrialDebugHelper.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/20.
//

import Foundation

/**
 * 试用功能调试助手
 * 用于调试和验证试用状态的持久化
 */
@MainActor
class TrialDebugHelper {
    
    static let shared = TrialDebugHelper()
    
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private let coreDataManager = CoreDataManager.shared
    
    private init() {}
    
    /**
     * 打印所有存储位置的试用状态
     */
    func printAllTrialStates() {
        print("🔍 === 试用状态调试信息 ===")
        
        // NSUbiquitousKeyValueStore
        let cloudHasReceived = ubiquitousStore.bool(forKey: "hasReceivedTrial")
        let cloudExpirationDate = ubiquitousStore.object(forKey: "trialExpirationDate") as? Date
        print("☁️ NSUbiquitousKeyValueStore:")
        print("   hasReceivedTrial: \(cloudHasReceived)")
        print("   trialExpirationDate: \(cloudExpirationDate?.description ?? "nil")")
        
        // UserDefaults
        let localHasReceived = UserDefaults.standard.bool(forKey: "local_hasReceivedTrial")
        let localExpirationDate = UserDefaults.standard.object(forKey: "local_trialExpirationDate") as? Date
        print("💾 UserDefaults:")
        print("   local_hasReceivedTrial: \(localHasReceived)")
        print("   local_trialExpirationDate: \(localExpirationDate?.description ?? "nil")")
        
        // CoreData
        let subscription = coreDataManager.getCurrentUser()?.subscription
        print("🗄️ CoreData:")
        print("   hasReceivedTrial: \(subscription?.hasReceivedTrial ?? false)")
        print("   expirationDate: \(subscription?.expirationDate?.description ?? "nil")")
        
        // TrialManager状态
        let trialManager = TrialManager.shared
        print("🎯 TrialManager:")
        print("   hasReceivedTrial: \(trialManager.hasReceivedTrial)")
        print("   isTrialActive: \(trialManager.isTrialActive)")
        print("   trialExpirationDate: \(trialManager.trialExpirationDate?.description ?? "nil")")
        
        print("🔍 === 调试信息结束 ===")
    }
    
    /**
     * 清除所有试用状态（仅用于测试）
     */
    func clearAllTrialStates() {
        print("🧹 清除所有试用状态...")
        
        // 清除NSUbiquitousKeyValueStore
        ubiquitousStore.removeObject(forKey: "hasReceivedTrial")
        ubiquitousStore.removeObject(forKey: "trialExpirationDate")
        ubiquitousStore.synchronize()
        
        // 清除UserDefaults
        UserDefaults.standard.removeObject(forKey: "local_hasReceivedTrial")
        UserDefaults.standard.removeObject(forKey: "local_trialExpirationDate")
        UserDefaults.standard.synchronize()
        
        // 清除CoreData
        if let subscription = coreDataManager.getCurrentUser()?.subscription {
            subscription.hasReceivedTrial = false
            subscription.expirationDate = nil
            coreDataManager.saveWithSync()
        }
        
        print("✅ 所有试用状态已清除")
    }
    
    /**
     * 验证数据一致性
     */
    func validateDataConsistency() -> Bool {
        let cloudHasReceived = ubiquitousStore.bool(forKey: "hasReceivedTrial")
        let localHasReceived = UserDefaults.standard.bool(forKey: "local_hasReceivedTrial")
        let coreDataHasReceived = coreDataManager.getCurrentUser()?.subscription?.hasReceivedTrial ?? false
        
        let cloudExpirationDate = ubiquitousStore.object(forKey: "trialExpirationDate") as? Date
        let localExpirationDate = UserDefaults.standard.object(forKey: "local_trialExpirationDate") as? Date
        let coreDataExpirationDate = coreDataManager.getCurrentUser()?.subscription?.expirationDate
        
        let hasReceivedConsistent = (cloudHasReceived == localHasReceived) && (localHasReceived == coreDataHasReceived)
        
        // 检查日期一致性（允许小的时间差异）
        var dateConsistent = true
        if let cloudDate = cloudExpirationDate, let localDate = localExpirationDate, let coreDataDate = coreDataExpirationDate {
            let cloudLocalDiff = abs(cloudDate.timeIntervalSince(localDate))
            let localCoreDataDiff = abs(localDate.timeIntervalSince(coreDataDate))
            dateConsistent = cloudLocalDiff < 60 && localCoreDataDiff < 60 // 允许1分钟误差
        } else {
            dateConsistent = (cloudExpirationDate == nil) && (localExpirationDate == nil) && (coreDataExpirationDate == nil)
        }
        
        let isConsistent = hasReceivedConsistent && dateConsistent
        
        print("🔍 数据一致性检查:")
        print("   hasReceivedTrial一致性: \(hasReceivedConsistent)")
        print("   expirationDate一致性: \(dateConsistent)")
        print("   总体一致性: \(isConsistent)")
        
        return isConsistent
    }
    
    /**
     * 强制同步所有存储
     */
    func forceSyncAllStores() {
        print("🔄 强制同步所有存储...")
        
        let trialManager = TrialManager.shared
        let hasReceived = trialManager.hasReceivedTrial
        let expirationDate = trialManager.trialExpirationDate
        
        // 同步到所有存储位置
        ubiquitousStore.set(hasReceived, forKey: "hasReceivedTrial")
        if let date = expirationDate {
            ubiquitousStore.set(date, forKey: "trialExpirationDate")
        } else {
            ubiquitousStore.removeObject(forKey: "trialExpirationDate")
        }
        ubiquitousStore.synchronize()
        
        UserDefaults.standard.set(hasReceived, forKey: "local_hasReceivedTrial")
        if let date = expirationDate {
            UserDefaults.standard.set(date, forKey: "local_trialExpirationDate")
        } else {
            UserDefaults.standard.removeObject(forKey: "local_trialExpirationDate")
        }
        UserDefaults.standard.synchronize()
        
        if let subscription = coreDataManager.getCurrentUser()?.subscription {
            subscription.hasReceivedTrial = hasReceived
            subscription.expirationDate = expirationDate
            coreDataManager.saveWithSync()
        }
        
        print("✅ 所有存储同步完成")
    }
}

#if DEBUG
extension TrialDebugHelper {
    /**
     * 模拟试用领取（仅用于调试）
     */
    func simulateTrialClaim() {
        print("🧪 模拟试用领取...")
        
        let expirationDate = Calendar.current.date(byAdding: .day, value: 30, to: Date())!
        
        // 设置到所有存储位置
        ubiquitousStore.set(true, forKey: "hasReceivedTrial")
        ubiquitousStore.set(expirationDate, forKey: "trialExpirationDate")
        ubiquitousStore.synchronize()
        
        UserDefaults.standard.set(true, forKey: "local_hasReceivedTrial")
        UserDefaults.standard.set(expirationDate, forKey: "local_trialExpirationDate")
        UserDefaults.standard.synchronize()
        
        if let subscription = coreDataManager.getCurrentUser()?.subscription {
            subscription.hasReceivedTrial = true
            subscription.expirationDate = expirationDate
            subscription.upgrade(to: .premium, expirationDate: expirationDate)
            coreDataManager.saveWithSync()
        }
        
        // 刷新TrialManager状态
        TrialManager.shared.refreshTrialStatus()
        
        print("✅ 模拟试用领取完成")
    }
}
#endif
