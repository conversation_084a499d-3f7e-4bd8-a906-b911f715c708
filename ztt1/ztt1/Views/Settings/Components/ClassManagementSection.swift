//
//  ClassManagementSection.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//  Updated: 2025/1/17 - 修复滚动手势冲突问题
//

import SwiftUI

/**
 * 班级管理组件 - 支持左滑删除和垂直滚动共存
 * 
 * 功能特性：
 * 1. 使用simultaneousGesture解决手势冲突问题
 * 2. 严格的水平拖拽检测，只有明确的左滑才激活删除功能
 * 3. 长按备用删除功能 + 完整的触觉反馈系统
 * 4. 保持ScrollView垂直滚动功能完全正常
 */

/**
 * 班级管理组件
 * 包含标题栏、创建按钮和班级卡片网格
 */
struct ClassManagementSection: View {
    
    // MARK: - Properties
    let classes: [ClassInfo]
    let height: CGFloat
    let onCreateClassTapped: () -> Void
    let onClassOptionsTapped: (String) -> Void
    let onClassDeleteTapped: (String) -> Void
    
    // MARK: - State
    @State private var sectionAppeared = false
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // 标题栏
            HStack {
                Text("settings.class_management.title".localized)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingMedium.fontSize,
                        weight: DesignSystem.Typography.HeadingMedium.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 创建班级按钮
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        onCreateClassTapped()
                    }
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "plus")
                            .font(.system(size: 12, weight: .semibold))
                        Text("settings.class_management.create_button".localized)
                            .font(.system(size: 14, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, DesignSystem.SettingsPage.CreateButton.paddingHorizontal)
                    .frame(height: DesignSystem.SettingsPage.CreateButton.height)
                    .background(DesignSystem.Colors.createButtonBackground)
                    .cornerRadius(DesignSystem.SettingsPage.CreateButton.cornerRadius)
                    .shadow(color: DesignSystem.Colors.createButtonBackground.opacity(0.3), radius: 4, x: 0, y: 2)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .opacity(sectionAppeared ? 1.0 : 0.0)
            .offset(y: sectionAppeared ? 0 : -20)
            .animation(.easeInOut(duration: 0.6).delay(0.1), value: sectionAppeared)
            
            // 班级卡片列表
            if classes.isEmpty {
                // 空状态
                EmptyClassesView()
                    .opacity(sectionAppeared ? 1.0 : 0.0)
                    .offset(y: sectionAppeared ? 0 : 20)
                    .animation(.easeInOut(duration: 0.6).delay(0.3), value: sectionAppeared)
            } else {
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(spacing: DesignSystem.Spacing.md) {
                        ForEach(classes.indices, id: \.self) { index in
                            ClassCardView(
                                classInfo: classes[index],
                                onOptionsTapped: {
                                    onClassOptionsTapped(classes[index].id)
                                },
                                onDeleteTapped: {
                                    onClassDeleteTapped(classes[index].id)
                                }
                            )
                            .opacity(sectionAppeared ? 1.0 : 0.0)
                            .offset(y: sectionAppeared ? 0 : 30)
                            .animation(.easeInOut(duration: 0.6).delay(0.2 + Double(index) * 0.1), value: sectionAppeared)
                        }
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .frame(height: height)
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
}

/**
 * 班级卡片视图（支持智能滑动删除，不影响垂直滚动）
 */
private struct ClassCardView: View {
    
    let classInfo: ClassInfo
    let onOptionsTapped: () -> Void
    let onDeleteTapped: () -> Void
    
    // MARK: - UI状态
    @State private var isPressed = false
    @State private var dragOffset: CGFloat = 0
    @State private var isShowingDeleteButton = false
    
    // MARK: - 手势状态
    @State private var isDraggingHorizontally: Bool = false
    
    // MARK: - 配置常量
    private let deleteButtonWidth: CGFloat = 80
    private let swipeThreshold: CGFloat = 60
    
    // MARK: - 手势检测配置
    private struct GestureConfig {
        static let minimumDistance: CGFloat = 5       // 最小拖拽距离
        static let longPressDelay: Double = 0.8       // 长按删除延迟
    }
    
    // MARK: - 动画配置
    private struct AnimationConfig {
        static let defaultSpring = Animation.spring(response: 0.3, dampingFraction: 0.8)
        static let deleteButtonSpring = Animation.spring(response: 0.4, dampingFraction: 0.7)
        static let resetAnimation = Animation.easeInOut(duration: 0.2)
        static let pressedScale: CGFloat = 0.98
        static let pressedFeedbackDelay: Double = 0.1
    }
    
    // MARK: - 触觉反馈配置
    private struct HapticConfig {
        static let lightImpact = UIImpactFeedbackGenerator.FeedbackStyle.light
        static let mediumImpact = UIImpactFeedbackGenerator.FeedbackStyle.medium
        static let heavyImpact = UIImpactFeedbackGenerator.FeedbackStyle.heavy
    }
    
    // 检查班级是否已冻结
    private var isFrozen: Bool {
        return classInfo.status == "frozen"
    }

    
    /**
     * 重置手势状态
     */
    private func resetGestureState() {
        dragOffset = 0
        isShowingDeleteButton = false
        isDraggingHorizontally = false
    }
    
    /**
     * 长按删除功能（备用删除方式）
     */
    private func showDeleteButtonWithHaptic() {
        guard !isShowingDeleteButton else { return }
        
        withAnimation(AnimationConfig.defaultSpring) {
            dragOffset = -deleteButtonWidth
            isShowingDeleteButton = true
        }
        
        // 长按删除触觉反馈 - 强烈反馈表示重要操作
        let impactFeedback = UIImpactFeedbackGenerator(style: HapticConfig.heavyImpact)
        impactFeedback.impactOccurred()
    }
    

    
    /**
     * 安全重置删除状态（防止状态卡住）
     */
    private func safeHideDeleteButton() {
        withAnimation(AnimationConfig.deleteButtonSpring) {
            resetGestureState()
        }
    }

    var body: some View {
        ZStack {
            // 删除按钮背景层
            HStack {
                Spacer()
                
                // 删除按钮
                Button(action: {
                    // 删除确认触觉反馈 - 最强烈反馈表示不可逆操作
                    let impactFeedback = UIImpactFeedbackGenerator(style: HapticConfig.heavyImpact)
                    impactFeedback.impactOccurred()
                    
                    withAnimation(AnimationConfig.defaultSpring) {
                        onDeleteTapped()
                    }
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "trash.fill")
                            .font(.system(size: 18, weight: .medium))
                        Text("delete_class.button.title".localized)
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .frame(width: deleteButtonWidth)
                    .frame(maxHeight: .infinity)
                    .background(Color.red)
                    .cornerRadius(DesignSystem.SettingsPage.ClassCard.cornerRadius, corners: [.topRight, .bottomRight])
                }
                .buttonStyle(PlainButtonStyle())
                .opacity(isShowingDeleteButton ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.2), value: isShowingDeleteButton)
            }
            
            // 主卡片容器
            ZStack {
                // 背景容器
                RoundedRectangle(cornerRadius: DesignSystem.SettingsPage.ClassCard.cornerRadius)
                    .fill(isFrozen ? DesignSystem.Colors.classCardBackground.opacity(0.6) : DesignSystem.Colors.classCardBackground)
                    .frame(height: DesignSystem.SettingsPage.ClassCard.height)
                    .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.SettingsPage.ClassCard.cornerRadius)
                            .stroke(Color.white.opacity(0.5), lineWidth: 1)
                    )
                
                // 内容布局
                HStack(spacing: DesignSystem.SettingsPage.ClassCard.spacing) {
                    // 班级图标
                    Image("banjixinxi")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(
                            width: 32,
                            height: 32
                        )
                        .offset(x: 15)
                        .opacity(isFrozen ? 0.6 : 1.0)
                    
                    // 班级信息
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(classInfo.name)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(isFrozen ? DesignSystem.Colors.textPrimary.opacity(0.6) : DesignSystem.Colors.textPrimary)
                                .lineLimit(1)
                            
                            if isFrozen {
                                Text("settings.class_management.frozen_tag".localized)
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.gray.opacity(0.7))
                                    .cornerRadius(4)
                            }
                        }
                        
                        Text("settings.class_management.student_count".localized(with: classInfo.studentCount))
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(isFrozen ? DesignSystem.Colors.textSecondary.opacity(0.6) : DesignSystem.Colors.textSecondary)
                    }
                    .offset(x: 30)
                    
                    Spacer()
                    
                    // 操作按钮
                    Button(action: {
                        if !isFrozen {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                onOptionsTapped()
                            }
                        }
                    }) {
                        Text("settings.class_management.options_button".localized)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(isFrozen ? DesignSystem.Colors.textPrimary.opacity(0.4) : DesignSystem.Colors.textPrimary)
                            .padding(.horizontal, DesignSystem.SettingsPage.ConfigButton.paddingHorizontal)
                            .frame(height: DesignSystem.SettingsPage.ConfigButton.height)
                            .background(isFrozen ? DesignSystem.Colors.configButtonBackground.opacity(0.4) : DesignSystem.Colors.configButtonBackground)
                            .cornerRadius(DesignSystem.SettingsPage.ConfigButton.cornerRadius)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(isFrozen)
                }
                .padding(DesignSystem.SettingsPage.ClassCard.padding)
            }
            .offset(x: dragOffset)
            .scaleEffect(isPressed ? AnimationConfig.pressedScale : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            .simultaneousGesture(
                DragGesture(minimumDistance: GestureConfig.minimumDistance, coordinateSpace: .local)
                    .onChanged { value in
                        // 严格的水平拖拽检测：只有明确的左滑才激活删除功能
                        let translation = value.translation
                        let horizontalDistance = abs(translation.width)
                        let verticalDistance = abs(translation.height)
                        
                        // 只处理明确的水平左滑动作
                        if translation.width < -10 && // 必须向左滑动至少10点
                           horizontalDistance > verticalDistance * 2 && // 水平距离大于垂直距离的2倍
                           verticalDistance < 15 { // 垂直偏移小于15点
                            
                            // 首次激活水平删除模式
                            if !isDraggingHorizontally {
                                isDraggingHorizontally = true
                                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                impactFeedback.impactOccurred()
                            }
                            
                            // 更新删除按钮偏移
                            dragOffset = max(translation.width, -deleteButtonWidth)
                            isShowingDeleteButton = dragOffset < -swipeThreshold
                        }
                    }
                    .onEnded { value in
                        // 只有在水平拖拽模式下才处理结束事件
                        guard isDraggingHorizontally else { return }
                        
                        let translation = value.translation.width
                        
                        withAnimation(AnimationConfig.deleteButtonSpring) {
                            if translation < -swipeThreshold {
                                // 显示删除按钮
                                dragOffset = -deleteButtonWidth
                                isShowingDeleteButton = true
                                
                                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                impactFeedback.impactOccurred()
                            } else {
                                // 重置状态
                                resetGestureState()
                            }
                        }
                        
                        isDraggingHorizontally = false
                    }
            )
            .onLongPressGesture(minimumDuration: GestureConfig.longPressDelay) {
                // 长按备用删除功能
                showDeleteButtonWithHaptic()
            }
            .onTapGesture {
                if isShowingDeleteButton {
                    // 如果删除按钮已显示，点击卡片隐藏删除按钮
                    safeHideDeleteButton()
                } else {
                    // 正常的卡片点击反馈
                    withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                        isPressed = true
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConfig.pressedFeedbackDelay) {
                        isPressed = false
                    }
                }
            }
        }
        .clipped()
        .onAppear {
            // 视图出现时重置状态，防止状态残留
            resetGestureState()
        }
    }
}

// MARK: - RoundedRectangle Extension for Custom Corners
extension RoundedRectangle {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

/**
 * 空状态视图
 */
private struct EmptyClassesView: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            Image("banjixinxi")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 60, height: 60)
                .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.6))
            
            VStack(spacing: 4) {
                Text("settings.class_management.empty.title".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("settings.class_management.empty.description".localized)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(hex: "#edf5d9"), lineWidth: 2)
                        .opacity(0.8)
                )
        )
    }
}

/**
 * 班级信息数据模型
 */
struct ClassInfo {
    let id: String
    let name: String
    let studentCount: Int
    let status: String
}

// MARK: - Preview
#Preview {
    ClassManagementSection(
        classes: [],
        height: 240,
        onCreateClassTapped: {
            print("创建班级")
        },
        onClassOptionsTapped: { classId in
            print("班级操作: \(classId)")
        },
        onClassDeleteTapped: { classId in
            print("删除班级: \(classId)")
        }
    )
    .padding()
    .background(DesignSystem.Colors.background)
} 