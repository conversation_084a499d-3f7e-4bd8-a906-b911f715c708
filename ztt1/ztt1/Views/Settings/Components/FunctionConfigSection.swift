//
//  FunctionConfigSection.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 功能配置组件
 * 包含规则库配置、奖品库配置、抽奖道具配置三个功能卡片
 */
struct FunctionConfigSection: View {
    
    // MARK: - Properties
    let onFunctionTapped: (FunctionType) -> Void
    
    // MARK: - State
    @State private var sectionAppeared = false
    
    // MARK: - Function Data
    private var functionItems: [FunctionItem] {
        return [
            FunctionItem(
                type: .ruleConfig,
                title: "settings.function_config.rule.title".localized,
                description: "settings.function_config.rule.description".localized,
                iconName: "guizepeizhi"
            ),
            FunctionItem(
                type: .rewardConfig,
                title: "settings.function_config.reward.title".localized,
                description: "settings.function_config.reward.description".localized,
                iconName: "lingqujilu"
            ),
            FunctionItem(
                type: .lotteryConfig,
                title: "settings.function_config.lottery.title".localized,
                description: "settings.function_config.lottery.description".localized,
                iconName: "choujiang"
            )
        ]
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            // 标题
            Text("settings.function_config.title".localized)
                .font(.system(
                    size: DesignSystem.Typography.HeadingMedium.fontSize,
                    weight: DesignSystem.Typography.HeadingMedium.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .opacity(sectionAppeared ? 1.0 : 0.0)
                .offset(y: sectionAppeared ? 0 : -20)
                .animation(.easeInOut(duration: 0.6).delay(0.1), value: sectionAppeared)
            
            // 功能卡片列表
            VStack(spacing: DesignSystem.SettingsPage.FunctionCard.spacing) {
                ForEach(functionItems.indices, id: \.self) { index in
                    FunctionCardView(
                        item: functionItems[index],
                        onTapped: {
                            onFunctionTapped(functionItems[index].type)
                        }
                    )
                    .opacity(sectionAppeared ? 1.0 : 0.0)
                    .offset(y: sectionAppeared ? 0 : 30)
                    .animation(.easeInOut(duration: 0.6).delay(0.2 + Double(index) * 0.1), value: sectionAppeared)
                }
            }
        }
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
}

/**
 * 功能卡片视图
 */
private struct FunctionCardView: View {
    
    let item: FunctionItem
    let onTapped: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                onTapped()
            }
        }) {
            ZStack {
                // 背景容器
                RoundedRectangle(cornerRadius: DesignSystem.SettingsPage.FunctionCard.cornerRadius)
                    .fill(DesignSystem.Colors.functionCardBackground)
                    .frame(height: DesignSystem.SettingsPage.FunctionCard.height)
                    .shadow(color: Color.black.opacity(0.08), radius: 6, x: 0, y: 3)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.SettingsPage.FunctionCard.cornerRadius)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.6),
                                        Color.white.opacity(0.2)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                
                // 装饰性背景元素
                HStack {
                    Spacer()
                    VStack {
                        Circle()
                            .fill(Color.white.opacity(0.1))
                            .frame(width: 60, height: 60)
                            .offset(x: 20, y: -20)
                        Spacer()
                    }
                }
                
                // 内容布局
                HStack(spacing: DesignSystem.SettingsPage.FunctionCard.spacing) {
                    // 功能图标
                    Image(item.iconName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(
                            width: 32,
                            height: 32
                        )
                        .offset(x: 15)
                    
                    // 功能信息
                    VStack(alignment: .leading, spacing: 4) {
                        Text(item.title)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .lineLimit(1)
                        
                        Text(item.description)
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(2)
                    }
                    .offset(x: 30)
                    
                    Spacer()
                    
                    // 右箭头指示器
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.6))
                }
                .padding(DesignSystem.SettingsPage.FunctionCard.padding)
                
                // 按压闪烁效果
                if isPressed {
                    RoundedRectangle(cornerRadius: DesignSystem.SettingsPage.FunctionCard.cornerRadius)
                        .fill(Color.white.opacity(0.3))
                        .frame(height: DesignSystem.SettingsPage.FunctionCard.height)
                        .transition(.opacity)
                }
            }
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onTapGesture {
            // 卡片点击反馈
            withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isPressed = false
            }
        }
    }
}

/**
 * 功能类型枚举
 */
enum FunctionType: CaseIterable {
    case ruleConfig
    case rewardConfig
    case lotteryConfig
    
    var displayName: String {
        switch self {
        case .ruleConfig:
            return "settings.function_config.rule.title".localized
        case .rewardConfig:
            return "settings.function_config.reward.title".localized
        case .lotteryConfig:
            return "settings.function_config.lottery.title".localized
        }
    }
}

/**
 * 功能项目数据模型
 */
private struct FunctionItem {
    let type: FunctionType
    let title: String
    let description: String
    let iconName: String
}

// MARK: - Preview
#Preview {
    FunctionConfigSection(
        onFunctionTapped: { type in
            print("点击了功能: \(type.displayName)")
        }
    )
    .padding()
    .background(DesignSystem.Colors.background)
} 