//
//  SettingsView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 抽奖配置流程阶段枚举
 */
enum LotteryConfigFlowStage {
    case idle
    case classSelection
    case toolTypeSelection
    case configForm
}

/**
 * 设置页面主视图
 * 包含班级管理和功能配置两个主要部分
 */
struct SettingsView: View {
    
    // MARK: - External Binding
    @Binding var initialShowLotteryConfig: Bool
    
    // MARK: - State
    @State private var pageAppeared = false
    @State private var classes: [SchoolClass] = []
    @State private var syncTimer: Timer?
    
    // MARK: - Create Class State
    @State private var showCreateClassDialog = false
    @State private var showPermissionDeniedAlert = false
    @State private var showSubscriptionView = false
    @State private var className = ""
    @State private var isCreatingClass = false
    @State private var createClassErrorMessage: String?
    @State private var showCreateClassSuccessAlert = false
    
    // MARK: - Delete Class State
    @State private var showDeleteClassDialog = false
    @State private var classToDelete: SchoolClass?
    @State private var isDeletingClass = false
    
    // MARK: - Rule Config State
    @State private var showRuleConfigOptions = false
    @State private var showRuleConfigForm = false
    @State private var ruleConfigType: RuleTemplate.RuleType = .add
    @State private var isSubmittingRules = false
    @State private var ruleConfigError: String? = nil
    @State private var showRuleConfigSuccessAlert = false
    
    // MARK: - Prize Config State
    @State private var showPrizeConfigForm = false
    @State private var isSubmittingPrizes = false
    @State private var prizeConfigError: String? = nil
    @State private var showPrizeConfigSuccessAlert = false
    
    // MARK: - Class Config State
    @State private var showClassConfigForm = false
    @State private var selectedClassForConfig: SchoolClass? = nil
    @State private var isSubmittingClassConfig = false
    @State private var classConfigError: String? = nil
    @State private var showClassConfigSuccessAlert = false
    
    // MARK: - Class Reset State
    @State private var showClassResetOptions = false
    @State private var showResetPointsForm = false
    @State private var showResetHistoryConfirm = false
    @State private var selectedClassForReset: SchoolClass? = nil
    @State private var isResettingPoints = false
    @State private var isResettingHistory = false
    @State private var resetError: String? = nil
    @State private var showResetSuccessAlert = false
    
    // MARK: - Lottery Tool Config State
    @State private var showingClassSelection = false {
        didSet {
            logStateChange("showingClassSelection", oldValue: oldValue, newValue: showingClassSelection)
        }
    }
    @State private var showingLotteryToolOptions = false {
        didSet {
            logStateChange("showingLotteryToolOptions", oldValue: oldValue, newValue: showingLotteryToolOptions)
        }
    }
    @State private var selectedClassForLottery: SchoolClass? {
        didSet {
            logStateChange("selectedClassForLottery", oldValue: oldValue?.name ?? "nil", newValue: selectedClassForLottery?.name ?? "nil")
        }
    }
    @State private var selectedLotteryToolType: LotteryToolConfig.ToolType? {
        didSet {
            logStateChange("selectedLotteryToolType", oldValue: oldValue?.displayName ?? "nil", newValue: selectedLotteryToolType?.displayName ?? "nil")
        }
    }
    @State private var showingLotteryToolConfig = false {
        didSet {
            logStateChange("showingLotteryToolConfig", oldValue: oldValue, newValue: showingLotteryToolConfig)
        }
    }
    @State private var editingLotteryConfig: LotteryToolConfig? {
        didSet {
            logStateChange("editingLotteryConfig", oldValue: oldValue != nil ? "存在" : "nil", newValue: editingLotteryConfig != nil ? "存在" : "nil")
        }
    }
    
    // MARK: - Flow Control State
    @State private var hasActiveSheetFlow = false {
        didSet {
            logStateChange("hasActiveSheetFlow", oldValue: oldValue, newValue: hasActiveSheetFlow)
        }
    }
    @State private var flowStageTracker: LotteryConfigFlowStage = .idle {
        didSet {
            logStateChange("flowStageTracker", oldValue: oldValue, newValue: flowStageTracker)
        }
    }
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - Computed Properties
    
    /**
     * 当前用户
     */
    private var currentUser: User {
        return coreDataManager.getOrCreateDefaultUser()
    }
    
    /**
     * 用户订阅级别显示名称
     */
    private var userLevelDisplayName: String {
        switch currentUser.subscriptionLevel {
        case "free":
            return "create_class.permission_denied.free_user".localized
        case "basic":
            return "create_class.permission_denied.basic_user".localized
        case "premium":
            return "create_class.permission_denied.premium_user".localized
        default:
            return "create_class.permission_denied.free_user".localized
        }
    }

    // MARK: - Computed Properties for Overlays

    /**
     * 抽奖道具选项弹窗
     */
    private var lotteryToolOptionsOverlay: some View {
        Group {
            if let selectedClass = selectedClassForLottery,
               hasActiveSheetFlow && flowStageTracker == .toolTypeSelection {
                LotteryToolOptionsView(
                    selectedClass: selectedClass,
                    onToolTypeSelected: handleLotteryToolTypeSelection,
                    onDismiss: {
                        showingLotteryToolOptions = false
                        safeResetLotteryConfigStates()
                    }
                )
                .environmentObject(coreDataManager)
            } else {
                // Fallback视图，防止空白显示
                VStack {
                    ProgressView("加载中...")
                        .padding()
                    Button("返回") {
                        showingLotteryToolOptions = false
                        handleFlowStateError()
                    }
                    .padding()
                }
            }
        }
        .allowsHitTesting(showingLotteryToolOptions)
        .opacity(showingLotteryToolOptions ? 1 : 0)
        .animation(.easeInOut(duration: 0.25), value: showingLotteryToolOptions)
    }

    /**
     * 抽奖道具配置表单弹窗
     */
    private var lotteryToolConfigOverlay: some View {
        Group {
            if let selectedClass = selectedClassForLottery,
               let toolType = selectedLotteryToolType,
               hasActiveSheetFlow && flowStageTracker == .configForm {
                LotteryToolConfigFormView(
                    selectedClass: selectedClass,
                    toolType: toolType,
                    existingConfig: editingLotteryConfig,
                    onConfigurationComplete: handleLotteryConfigurationComplete,
                    onDismiss: {
                        showingLotteryToolConfig = false
                        safeResetLotteryConfigStates()
                    }
                )
                .environmentObject(coreDataManager)
            } else {
                // Fallback视图，防止空白显示
                VStack {
                    ProgressView("加载配置中...")
                        .padding()
                    Button("返回") {
                        showingLotteryToolConfig = false
                        handleFlowStateError()
                    }
                    .padding()
                }
            }
        }
        .allowsHitTesting(showingLotteryToolConfig)
        .opacity(showingLotteryToolConfig ? 1 : 0)
        .animation(.easeInOut(duration: 0.25), value: showingLotteryToolConfig)
    }

    var body: some View {
        let _ = logSwiftUIUpdate("body重新计算开始")
        let _ = logCurrentState("body计算时")
        
        ZStack {
            // 美化背景渐变 - 与首页保持一致的风格
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)
            
            // 装饰性背景元素
            VStack {
                HStack {
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 100, height: 100)
                        .offset(x: -30, y: 20)
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -10)
                }
                Spacer()
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.02))
                        .frame(width: 80, height: 80)
                        .offset(x: 20, y: 30)
                }
            }
            
            // 主要内容区域 - 空白页面
            GeometryReader { geometry in
                VStack {
                    // 空白内容区域
                    Spacer()
                }
                .padding(.top, DesignSystem.Spacing.lg)
                .padding(.bottom, DesignSystem.Spacing.md)
            }
        }
        .onAppear {
            logViewLifecycle("onAppear", details: "SettingsView首次出现")
            logCurrentState("onAppear时")
            
            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
            // 加载班级数据
            loadClassesData()
            
            // 检查CloudKit可用性（保留功能，但不显示UI）
            CoreDataManager.shared.checkCloudKitAvailability()
            
            // 设置定时器自动同步数据（每5分钟同步一次）
            syncTimer = Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { _ in
                CoreDataManager.shared.triggerCloudKitSync()
            }
            
            // 如果需要自动启动抽奖道具配置流程
            if initialShowLotteryConfig {
                // 延迟一下，确保页面已完全加载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    startLotteryToolConfiguration()
                    // 重置标志，避免重复触发
                    initialShowLotteryConfig = false
                }
            }
        }
        .onDisappear {
            logViewLifecycle("onDisappear", details: "SettingsView消失")
            
            // 清除定时器
            syncTimer?.invalidate()
            syncTimer = nil
        }




    }
    
    // MARK: - Action Handlers
    
    /**
     * 处理创建班级按钮点击
     */
    private func handleCreateClass() {
        print("创建班级功能")
        
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            // 添加视觉反馈
        }
        
        // 1. 检查会员权限
        if !currentUser.canCreateMoreClasses() {
            showPermissionDeniedAlert = true
            return
        }
        
        // 2. 显示创建班级弹窗
        className = ""
        showCreateClassDialog = true
    }
    
    /**
     * 执行创建班级
     */
    private func createClass() {
        // 验证班级名称
        let trimmedName = className.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedName.isEmpty {
            createClassErrorMessage = "create_class.validation.name_empty".localized
            return
        }
        
        if trimmedName.count > 30 {
            createClassErrorMessage = "create_class.validation.name_too_long".localized
            return
        }
        
        // 检查班级名称是否已存在
        if classes.contains(where: { $0.name == trimmedName }) {
            createClassErrorMessage = "create_class.validation.name_exists".localized
            return
        }
        
        // 开始创建班级
        isCreatingClass = true
        
        // 使用CoreData创建班级
        let newClass = coreDataManager.createClass(name: trimmedName, for: currentUser)
        
        // 更新本地班级列表
        classes.append(newClass)
        
        // 重置状态
        isCreatingClass = false
        showCreateClassDialog = false
        resetCreateClassState()
        
        // 显示成功消息
        showCreateClassSuccessAlert = true
        
        print("班级创建成功: \(trimmedName)")
    }
    
    /**
     * 重置创建班级状态
     */
    private func resetCreateClassState() {
        className = ""
        isCreatingClass = false
        createClassErrorMessage = nil
    }
    
    /**
     * 处理班级操作按钮点击
     */
    private func handleClassOptions(classId: String) {
        print("班级操作: \(classId)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }
        
        // 根据ID查找班级
        if let schoolClass = classes.first(where: { $0.id?.uuidString == classId }) {
            selectedClassForReset = schoolClass
            showClassResetOptions = true
            print("显示班级操作选项: \(schoolClass.name ?? "")")
        } else {
            print("未找到班级ID: \(classId)")
        }
    }
    
    /**
     * 处理班级配置选项
     */
    private func handleClassConfig() {
        showClassResetOptions = false
        
        if let schoolClass = selectedClassForReset {
            selectedClassForConfig = schoolClass
            showClassConfigForm = true
            print("显示班级配置界面: \(schoolClass.name ?? "")")
        }
    }
    
    /**
     * 显示重置积分选项
     */
    private func showResetPointsOption() {
        showClassResetOptions = false
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.showResetPointsForm = true
        }
    }
    
    /**
     * 显示重置历史记录选项
     */
    private func showResetHistoryOption() {
        showClassResetOptions = false
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.showResetHistoryConfirm = true
        }
    }
    
    /**
     * 重置班级积分
     */
    private func resetClassPoints(to points: Int) {
        guard let schoolClass = selectedClassForReset else {
            resetError = "class_reset.error.no_students".localized
            return
        }
        
        if schoolClass.studentCount == 0 {
            resetError = "class_reset.error.no_students".localized
            return
        }
        
        isResettingPoints = true
        resetError = nil
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let success = schoolClass.resetAllStudentsPoints(to: points, in: self.coreDataManager.viewContext)
            
            DispatchQueue.main.async {
                self.isResettingPoints = false
                
                if success {
                    // 重新加载班级数据
                    self.loadClassesData()
                    
                    // 显示成功提示
                    self.showResetSuccessAlert = true
                    
                    print("✅ 班级积分重置成功")
                } else {
                    self.resetError = "class_reset.error.points_failed".localized(with: "数据保存失败")
                    print("❌ 班级积分重置失败")
                }
            }
        }
    }
    
    /**
     * 重置班级历史记录
     */
    private func resetClassHistory() {
        guard let schoolClass = selectedClassForReset else {
            resetError = "class_reset.error.no_students".localized
            return
        }
        
        if schoolClass.studentCount == 0 {
            resetError = "class_reset.error.no_students".localized
            return
        }
        
        isResettingHistory = true
        resetError = nil
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let result = schoolClass.clearAllStudentsHistory(in: self.coreDataManager.viewContext)
            
            DispatchQueue.main.async {
                self.isResettingHistory = false
                
                if result.success {
                    // 重新加载班级数据
                    self.loadClassesData()
                    
                    // 显示成功提示
                    self.showResetSuccessAlert = true
                    
                    print("✅ 班级历史记录清除成功，删除了 \(result.deletedCount) 条记录")
                } else {
                    self.resetError = "class_reset.error.history_failed".localized(with: "数据删除失败")
                    print("❌ 班级历史记录清除失败")
                }
            }
        }
    }
    
    /**
     * 关闭重置选项菜单
     */
    private func closeResetOptionsMenu() {
        showClassResetOptions = false
        selectedClassForReset = nil
    }
    
    /**
     * 处理功能配置卡片点击
     */
    private func handleFunctionConfig(type: FunctionType) {
        logLotteryConfig("🎯 用户点击功能配置: \(type.displayName)", type: "USER")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }
        
        switch type {
        case .ruleConfig:
            // 显示规则配置选项菜单
            logLotteryConfig("📋 显示规则库配置选项", type: "UI")
            showRuleConfigOptions = true
        case .rewardConfig:
            // 显示奖品配置表单
            logLotteryConfig("🎁 显示奖品库配置表单", type: "UI")
            showPrizeConfigForm = true
        case .lotteryConfig:
            // 启动抽奖道具配置流程
            logLotteryConfig("🎲 用户选择抽奖道具配置，即将启动流程", type: "USER")
            startLotteryToolConfiguration()
        }
    }
    
    /**
     * 显示加分规则配置表单
     */
    private func showAddRulesConfig() {
        print("🎯 [规则配置] 用户选择：配置加分规则")
        print("🔄 [规则配置] 设置ruleConfigType = .add")
        ruleConfigType = .add
        print("✅ [规则配置] ruleConfigType已设置为: \(ruleConfigType)")
        showRuleConfigOptions = false
        
        // 延迟显示表单，确保选项菜单先关闭且状态同步
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            print("📋 [规则配置] 准备显示加分规则配置表单")
            print("🔍 [规则配置] 最终检查ruleConfigType: \(self.ruleConfigType)")
            self.showRuleConfigForm = true
            print("✅ [规则配置] 已显示加分规则配置表单")
        }
    }
    
    /**
     * 显示扣分规则配置表单
     */
    private func showDeductRulesConfig() {
        print("🎯 [规则配置] 用户选择：配置扣分规则")
        print("🔄 [规则配置] 设置ruleConfigType = .deduct")
        ruleConfigType = .deduct
        print("✅ [规则配置] ruleConfigType已设置为: \(ruleConfigType)")
        showRuleConfigOptions = false
        
        // 延迟显示表单，确保选项菜单先关闭且状态同步
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            print("📋 [规则配置] 准备显示扣分规则配置表单")
            print("🔍 [规则配置] 最终检查ruleConfigType: \(self.ruleConfigType)")
            self.showRuleConfigForm = true
            print("✅ [规则配置] 已显示扣分规则配置表单")
        }
    }
    
    /**
     * 批量添加规则模板
     */
    private func addRuleTemplates(_ ruleForms: [RuleFormData]) {
        print("💾 [规则配置] 开始保存规则模板")
        print("📊 [规则配置] 当前ruleConfigType: \(ruleConfigType)")
        print("📝 [规则配置] 收到的规则表单数量: \(ruleForms.count)")
        
        // 设置处理状态
        isSubmittingRules = true
        ruleConfigError = nil
        
        var successCount = 0
        let errorCount = 0
        
        // 延迟处理，模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            // 批量创建规则模板
            for (index, ruleForm) in ruleForms.enumerated() {
                print("🔍 [规则配置] 处理第\(index+1)个规则:")
                print("  - 规则名称: \(ruleForm.formattedName)")
                print("  - 规则分值: \(ruleForm.valueInt)")
                print("  - 规则类型: \(ruleForm.type)")
                print("  - 期望类型: \(self.ruleConfigType.rawValue)")
                
                if ruleForm.type != self.ruleConfigType.rawValue {
                    print("⚠️ [规则配置] 警告：规则类型不匹配！")
                    print("   表单类型: \(ruleForm.type), 配置类型: \(self.ruleConfigType.rawValue)")
                }
                
                let _ = self.coreDataManager.createRuleTemplate(
                    name: ruleForm.formattedName,
                    value: ruleForm.valueInt,
                    type: ruleForm.type
                )
                successCount += 1
                print("✅ [规则配置] 规则模板创建成功: \(ruleForm.formattedName) (\(ruleForm.valueInt)分) [类型:\(ruleForm.type)]")
            }
            
            DispatchQueue.main.async {
                self.isSubmittingRules = false
                
                if errorCount > 0 {
                    self.ruleConfigError = "Some rules failed to add: %@ succeeded, %@ failed".localized(with: "\(successCount)", "\(errorCount)")
                }
                
                if successCount > 0 {
                    // 显示成功提示
                    self.showRuleConfigSuccessAlert = true
                    
                    // 如果全部成功，关闭表单
                    if errorCount == 0 {
                        self.showRuleConfigForm = false
                    }
                }
                
                print("批量添加规则模板完成: 成功 \(successCount) 个，失败 \(errorCount) 个")
            }
        }
    }
    
    /**
     * 关闭规则配置选项菜单
     */
    private func closeRuleConfigOptions() {
        showRuleConfigOptions = false
        print("关闭规则配置选项菜单")
    }
    
    /**
     * 关闭规则配置表单
     */
    private func closeRuleConfigForm() {
        showRuleConfigForm = false
        ruleConfigError = nil
        print("关闭规则配置表单")
    }
    
    /**
     * 清除规则配置错误信息
     */
    private func clearRuleConfigError() {
        ruleConfigError = nil
    }
    
    /**
     * 关闭规则配置成功提示
     */
    private func closeRuleConfigSuccessAlert() {
        showRuleConfigSuccessAlert = false
    }
    
    // MARK: - Prize Config Methods
    
    /**
     * 批量添加奖品模板
     */
    private func addPrizeTemplates(_ prizeForms: [PrizeFormData]) {
        // 设置处理状态
        isSubmittingPrizes = true
        prizeConfigError = nil
        
        var successCount = 0
        let errorCount = 0
        
        // 延迟处理，模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            // 批量创建奖品模板
            for prizeForm in prizeForms {
                let _ = self.coreDataManager.createPrizeTemplate(
                    name: prizeForm.formattedName,
                    cost: prizeForm.costInt,
                    type: prizeForm.type
                )
                successCount += 1
                print("奖品模板创建成功: \(prizeForm.formattedName) (\(prizeForm.costInt)积分)")
            }
            
            DispatchQueue.main.async {
                self.isSubmittingPrizes = false
                
                if errorCount > 0 {
                    self.prizeConfigError = "prize_config.error.partial_failure".localized(with: "\(successCount)", "\(errorCount)")
                }
                
                if successCount > 0 {
                    // 显示成功提示
                    self.showPrizeConfigSuccessAlert = true
                    
                    // 如果全部成功，关闭表单
                    if errorCount == 0 {
                        self.showPrizeConfigForm = false
                    }
                }
                
                print("批量添加奖品模板完成: 成功 \(successCount) 个，失败 \(errorCount) 个")
            }
        }
    }
    
    /**
     * 关闭奖品配置表单
     */
    private func closePrizeConfigForm() {
        showPrizeConfigForm = false
        prizeConfigError = nil
        print("关闭奖品配置表单")
    }
    
    /**
     * 清除奖品配置错误信息
     */
    private func clearPrizeConfigError() {
        prizeConfigError = nil
    }
    
    /**
     * 关闭奖品配置成功提示
     */
    private func closePrizeConfigSuccessAlert() {
        showPrizeConfigSuccessAlert = false
    }
    
    // MARK: - Class Config Methods
    
    /**
     * 提交班级配置
     */
    private func submitClassConfiguration(_ formData: ClassConfigFormData) {
        guard let schoolClass = selectedClassForConfig else {
            classConfigError = "No class selected".localized
            isSubmittingClassConfig = false
            return
        }
        
        // 设置处理状态
        isSubmittingClassConfig = true
        classConfigError = nil
        
        // 延迟处理，模拟保存操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 清理现有的常用规则和奖品
            self.clearExistingClassConfiguration(for: schoolClass)
            
            var successCount = 0
            
            // 保存加分规则
            for ruleForm in formData.validAddRules {
                let _ = self.coreDataManager.addRule(
                    to: schoolClass,
                    name: ruleForm.formattedName,
                    value: ruleForm.valueInt,
                    type: "add",
                    isFrequent: true
                )
                successCount += 1
                print("班级加分规则创建成功: \(ruleForm.formattedName) (\(ruleForm.valueInt)分)")
            }
            
            // 保存扣分规则
            for ruleForm in formData.validDeductRules {
                let _ = self.coreDataManager.addRule(
                    to: schoolClass,
                    name: ruleForm.formattedName,
                    value: ruleForm.valueInt,
                    type: "deduct",
                    isFrequent: true
                )
                successCount += 1
                print("班级扣分规则创建成功: \(ruleForm.formattedName) (\(ruleForm.valueInt)分)")
            }
            
            // 保存奖品
            for prizeForm in formData.validPrizes {
                let _ = self.coreDataManager.addPrize(
                    to: schoolClass,
                    name: prizeForm.formattedName,
                    cost: prizeForm.costInt,
                    type: prizeForm.type
                )
                successCount += 1
                print("班级奖品创建成功: \(prizeForm.formattedName) (\(prizeForm.costInt)积分)")
            }
            
            DispatchQueue.main.async {
                self.isSubmittingClassConfig = false
                
                if successCount > 0 {
                    // 显示成功提示
                    self.showClassConfigSuccessAlert = true
                    
                    // 关闭表单
                    self.showClassConfigForm = false
                    
                    print("班级配置保存完成: 成功保存 \(successCount) 项配置")
                } else {
                    self.classConfigError = "There is valid configuration data.".localized
                }
            }
        }
    }
    
    /**
     * 清理班级现有的常用配置
     */
    private func clearExistingClassConfiguration(for schoolClass: SchoolClass) {
        // 删除现有的常用规则
        let existingRules = schoolClass.frequentRules
        for rule in existingRules {
            coreDataManager.viewContext.delete(rule)
            print("删除现有常用规则: \(rule.name ?? "")")
        }
        
        // 删除现有的奖品
        let existingPrizes = schoolClass.sortedPrizes
        for prize in existingPrizes {
            coreDataManager.viewContext.delete(prize)
            print("删除现有奖品: \(prize.name ?? "")")
        }
        
        // 保存删除操作
        coreDataManager.save()
    }
    
    /**
     * 关闭班级配置表单
     */
    private func closeClassConfigForm() {
        showClassConfigForm = false
        selectedClassForConfig = nil
        classConfigError = nil
        isSubmittingClassConfig = false
        print("关闭班级配置表单")
    }
    
    /**
     * 清除班级配置错误信息
     */
    private func clearClassConfigError() {
        classConfigError = nil
    }
    
    /**
     * 关闭班级配置成功提示
     */
    private func closeClassConfigSuccessAlert() {
        showClassConfigSuccessAlert = false
    }
    
    // MARK: - Lottery Tool Config Methods
    
    /**
     * 日志记录工具函数
     * 在Debug模式下启用详细日志，Release模式下只保留关键的状态同步逻辑
     */
    private func logLotteryConfig(_ message: String, type: String = "INFO") {
        #if DEBUG
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        let timestamp = formatter.string(from: Date())
        print("[\(timestamp)] [LotteryConfig-\(type)] \(message)")
        #endif
        // 注意：即使在Release模式下，这个函数的调用仍然会执行
        // 这保证了状态访问和同步逻辑的执行，这是修复问题的关键
    }
    
    /**
     * 状态变化追踪函数
     */
    private func logStateChange<T>(_ propertyName: String, oldValue: T, newValue: T) {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        let timestamp = formatter.string(from: Date())
        
        // 获取调用栈信息
        let callStack = Thread.callStackSymbols
        let caller = callStack.count > 2 ? callStack[2] : "Unknown"
        let simplifiedCaller = extractFunctionName(from: caller)
        
        print("[\(timestamp)] [LotteryConfig-STATE-CHANGE] 🔄 \(propertyName): \(oldValue) → \(newValue)")
        print("[\(timestamp)] [LotteryConfig-STATE-CHANGE] 📍 调用位置: \(simplifiedCaller)")
        
        // 记录完整的状态上下文
        DispatchQueue.main.async {
            self.logCurrentState("状态变化后(\(propertyName))")
        }
    }
    
    /**
     * 从调用栈中提取函数名
     */
    private func extractFunctionName(from callStackString: String) -> String {
        // 简化调用栈信息，提取有用的函数名
        let components = callStackString.components(separatedBy: " ")
        if let functionPart = components.last {
            return functionPart
        }
        return callStackString
    }
    
    /**
     * View生命周期追踪
     */
    private func logViewLifecycle(_ event: String, details: String = "") {
        logLotteryConfig("🔄 View生命周期: \(event) \(details)", type: "LIFECYCLE")
    }
    
    /**
     * SwiftUI更新周期追踪
     */
    private func logSwiftUIUpdate(_ context: String) {
        logLotteryConfig("🔄 SwiftUI更新: \(context)", type: "SWIFTUI")
    }
    
    /**
     * 记录当前状态快照
     * 这个函数的关键作用是强制访问所有状态变量，确保SwiftUI状态同步
     */
    private func logCurrentState(_ context: String) {
        // 这些状态访问是修复问题的关键 - 强制SwiftUI状态同步
        let _ = hasActiveSheetFlow
        let _ = flowStageTracker
        let _ = selectedClassForLottery?.name
        let _ = selectedLotteryToolType?.displayName
        let _ = editingLotteryConfig
        let _ = showingClassSelection
        let _ = showingLotteryToolOptions
        let _ = showingLotteryToolConfig
        
        #if DEBUG
        logLotteryConfig("=== 状态快照 (\(context)) ===", type: "STATE")
        logLotteryConfig("hasActiveSheetFlow: \(hasActiveSheetFlow)", type: "STATE")
        logLotteryConfig("flowStageTracker: \(flowStageTracker)", type: "STATE")
        logLotteryConfig("selectedClassForLottery: \(selectedClassForLottery?.name ?? "nil")", type: "STATE")
        logLotteryConfig("selectedLotteryToolType: \(selectedLotteryToolType?.displayName ?? "nil")", type: "STATE")
        logLotteryConfig("editingLotteryConfig: \(editingLotteryConfig != nil ? "存在" : "nil")", type: "STATE")
        logLotteryConfig("showingClassSelection: \(showingClassSelection)", type: "STATE")
        logLotteryConfig("showingLotteryToolOptions: \(showingLotteryToolOptions)", type: "STATE")
        logLotteryConfig("showingLotteryToolConfig: \(showingLotteryToolConfig)", type: "STATE")
        
        // 添加对象内存地址追踪
        if let selectedClass = selectedClassForLottery {
            logLotteryConfig("selectedClassForLottery内存地址: \(Unmanaged.passUnretained(selectedClass).toOpaque())", type: "STATE")
        }
        if let toolType = selectedLotteryToolType {
            logLotteryConfig("selectedLotteryToolType值: \(toolType)", type: "STATE")
        }
        if let config = editingLotteryConfig {
            logLotteryConfig("editingLotteryConfig内存地址: \(Unmanaged.passUnretained(config).toOpaque())", type: "STATE")
        }
        
        // 添加View实例追踪（使用withUnsafePointer）
        withUnsafePointer(to: self) { pointer in
            logLotteryConfig("SettingsView实例地址: \(pointer)", type: "STATE")
        }
        
        logLotteryConfig("=== 状态快照结束 ===", type: "STATE")
        #endif
    }
    
    /**
     * 启动抽奖道具配置流程
     */
    private func startLotteryToolConfiguration() {
        logLotteryConfig("🚀 用户点击抽奖道具配置按钮", type: "USER")
        logCurrentState("启动前")
        
        // 检查是否有活跃的流程，如果有则先安全重置
        if hasActiveSheetFlow {
            logLotteryConfig("⚠️ 检测到活跃流程，执行强制重置", type: "WARNING")
            logLotteryConfig("当前流程阶段: \(flowStageTracker)", type: "WARNING")
            safeResetLotteryConfigStates(forced: true)
        } else {
            logLotteryConfig("✅ 无活跃流程，可以安全启动", type: "SUCCESS")
        }
        
        // 设置流程控制状态
        logLotteryConfig("🔄 设置流程控制状态", type: "FLOW")
        hasActiveSheetFlow = true
        flowStageTracker = .classSelection
        
        logLotteryConfig("📱 准备显示班级选择界面", type: "UI")
        
        // 显示班级选择界面
        showingClassSelection = true
        
        logCurrentState("启动后")
        logLotteryConfig("✅ 抽奖道具配置流程启动完成", type: "SUCCESS")
    }
    
    /**
     * 处理班级选择
     */
    private func handleClassSelection(_ schoolClass: SchoolClass) {
        logLotteryConfig("🏫 用户选择班级: \(schoolClass.name ?? "未命名")", type: "USER")
        logCurrentState("班级选择前")
        
        // 验证流程状态
        let flowStateValid = hasActiveSheetFlow && flowStageTracker == .classSelection
        logLotteryConfig("🔍 流程状态验证: \(flowStateValid ? "有效" : "无效")", type: flowStateValid ? "SUCCESS" : "ERROR")
        
        if !flowStateValid {
            logLotteryConfig("❌ 班级选择处理时流程状态异常", type: "ERROR")
            logLotteryConfig("预期: hasActiveSheetFlow=true, flowStageTracker=classSelection", type: "ERROR")
            logLotteryConfig("实际: hasActiveSheetFlow=\(hasActiveSheetFlow), flowStageTracker=\(flowStageTracker)", type: "ERROR")
            handleFlowStateError()
            return
        }
        
        // 设置选择的班级
        logLotteryConfig("💾 保存选择的班级", type: "DATA")
        selectedClassForLottery = schoolClass
        
        logLotteryConfig("📱 关闭班级选择界面", type: "UI")
        showingClassSelection = false
        
        // 更新流程阶段
        logLotteryConfig("🔄 更新流程阶段: classSelection -> toolTypeSelection", type: "FLOW")
        flowStageTracker = .toolTypeSelection
        
        logCurrentState("班级选择后")
        
        // 显示道具类型选择界面
        logLotteryConfig("⏰ 延迟0.3秒后显示道具类型选择界面", type: "ASYNC")
        logLotteryConfig("🎯 延迟前状态快照:", type: "ASYNC")
        logCurrentState("延迟前")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.logLotteryConfig("📱 异步延迟执行开始", type: "ASYNC")
            self.logCurrentState("异步执行时")
            
            self.logLotteryConfig("🎯 准备设置showingLotteryToolOptions = true", type: "ASYNC")
            self.showingLotteryToolOptions = true
            
            self.logLotteryConfig("✅ 道具类型选择界面显示完成", type: "SUCCESS")
            self.logCurrentState("异步执行后")
        }
    }
    
    /**
     * 处理道具类型选择
     */
    private func handleLotteryToolTypeSelection(_ toolType: LotteryToolConfig.ToolType) {
        logLotteryConfig("🎯 用户选择道具类型: \(toolType.displayName)", type: "USER")
        logCurrentState("道具类型选择前")
        
        // 验证流程状态和必要条件
        let flowStateValid = hasActiveSheetFlow && flowStageTracker == .toolTypeSelection
        logLotteryConfig("🔍 流程状态验证: \(flowStateValid ? "有效" : "无效")", type: flowStateValid ? "SUCCESS" : "ERROR")
        
        if !flowStateValid {
            logLotteryConfig("❌ 道具类型选择处理时流程状态异常", type: "ERROR")
            logLotteryConfig("预期: hasActiveSheetFlow=true, flowStageTracker=toolTypeSelection", type: "ERROR")
            logLotteryConfig("实际: hasActiveSheetFlow=\(hasActiveSheetFlow), flowStageTracker=\(flowStageTracker)", type: "ERROR")
            handleFlowStateError()
            return
        }
        
        guard let schoolClass = selectedClassForLottery else {
            logLotteryConfig("❌ 道具类型选择时班级状态为空", type: "ERROR")
            logLotteryConfig("selectedClassForLottery为nil，无法继续", type: "ERROR")
            handleFlowStateError()
            return
        }
        
        logLotteryConfig("✅ 班级状态验证通过: \(schoolClass.name ?? "未命名")", type: "SUCCESS")
        
        // 设置选择的道具类型
        logLotteryConfig("💾 保存选择的道具类型", type: "DATA")
        selectedLotteryToolType = toolType
        
        logLotteryConfig("📱 关闭道具类型选择界面", type: "UI")
        showingLotteryToolOptions = false
        
        // 更新流程阶段
        logLotteryConfig("🔄 更新流程阶段: toolTypeSelection -> configForm", type: "FLOW")
        flowStageTracker = .configForm
        
        // 检查是否已有配置
        logLotteryConfig("🔍 检查现有配置", type: "DATA")
        editingLotteryConfig = schoolClass.getLotteryConfig(for: toolType)
        if let existingConfig = editingLotteryConfig {
            logLotteryConfig("📋 找到现有配置: \(existingConfig.lotteryToolType.displayName)", type: "DATA")
        } else {
            logLotteryConfig("🆕 未找到现有配置，将创建新配置", type: "DATA")
        }
        
        logCurrentState("道具类型选择后")
        
        // 显示配置表单
        logLotteryConfig("⏰ 延迟0.3秒后显示配置表单", type: "ASYNC")
        logLotteryConfig("🎯 配置表单延迟前状态快照:", type: "ASYNC")
        logCurrentState("配置表单延迟前")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.logLotteryConfig("📱 配置表单异步延迟执行开始", type: "ASYNC")
            self.logCurrentState("配置表单异步执行时")
            
            self.logLotteryConfig("🎯 准备设置showingLotteryToolConfig = true", type: "ASYNC")
            self.showingLotteryToolConfig = true
            
            self.logLotteryConfig("✅ 配置表单显示完成", type: "SUCCESS")
            self.logCurrentState("配置表单异步执行后")
        }
    }
    
    /**
     * 安全重置抽奖配置状态
     */
    private func safeResetLotteryConfigStates(forced: Bool = false) {
        logLotteryConfig("🔄 开始状态重置 (forced: \(forced))", type: "RESET")
        logCurrentState("重置前")
        
        // 检查是否有活跃的sheet，除非强制重置
        let hasActiveSheets = showingClassSelection || showingLotteryToolOptions || showingLotteryToolConfig
        if !forced && hasActiveSheets {
            logLotteryConfig("⚠️ 检测到活跃的sheet，跳过重置", type: "WARNING")
            logLotteryConfig("showingClassSelection: \(showingClassSelection)", type: "WARNING")
            logLotteryConfig("showingLotteryToolOptions: \(showingLotteryToolOptions)", type: "WARNING")
            logLotteryConfig("showingLotteryToolConfig: \(showingLotteryToolConfig)", type: "WARNING")
            return
        }
        
        if forced {
            logLotteryConfig("💪 执行强制重置", type: "RESET")
        } else {
            logLotteryConfig("✅ 安全条件满足，执行重置", type: "RESET")
        }
        
        // 重置所有相关状态
        logLotteryConfig("🗑️ 清空所有状态变量", type: "RESET")
        selectedClassForLottery = nil
        selectedLotteryToolType = nil
        editingLotteryConfig = nil
        hasActiveSheetFlow = false
        flowStageTracker = .idle
        
        // 确保所有sheet都关闭
        logLotteryConfig("📱 关闭所有sheet", type: "RESET")
        showingClassSelection = false
        showingLotteryToolOptions = false
        showingLotteryToolConfig = false
        
        logCurrentState("重置后")
        logLotteryConfig("✅ 抽奖配置状态重置完成", type: "SUCCESS")
    }
    
    /**
     * 处理配置完成
     */
    private func handleLotteryConfigurationComplete() {
        logLotteryConfig("🎉 配置完成回调触发", type: "SUCCESS")
        logCurrentState("配置完成前")
        
        logLotteryConfig("📱 关闭配置表单", type: "UI")
        showingLotteryToolConfig = false
        
        // 重新加载班级数据以更新显示
        logLotteryConfig("🔄 重新加载班级数据", type: "DATA")
        loadClassesData()
        
        // 使用安全重置机制
        logLotteryConfig("⏰ 延迟0.5秒后执行状态重置", type: "ASYNC")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.logLotteryConfig("🔄 执行配置完成后的状态重置", type: "CLEANUP")
            self.safeResetLotteryConfigStates()
        }
        
        logLotteryConfig("✅ 抽奖道具配置流程完成", type: "SUCCESS")
    }
    
    /**
     * 处理流程状态错误
     */
    private func handleFlowStateError() {
        logLotteryConfig("🚨 检测到流程状态错误，开始错误恢复", type: "ERROR")
        
        // 记录当前状态用于调试
        logCurrentState("错误发生时")
        
        logLotteryConfig("🔧 开始错误恢复流程", type: "RECOVERY")
        
        // 强制安全重置所有状态
        logLotteryConfig("💪 执行强制状态重置", type: "RECOVERY")
        safeResetLotteryConfigStates(forced: true)
        
        // 提供用户反馈
        logLotteryConfig("⏰ 延迟0.1秒后完成错误恢复", type: "ASYNC")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.logLotteryConfig("✅ 错误恢复完成，流程已重置", type: "RECOVERY")
            self.logLotteryConfig("💡 用户可以重新开始配置", type: "INFO")
        }
    }
    
    /**
     * 处理删除班级按钮点击
     */
    private func handleDeleteClass(classId: String) {
        print("删除班级: \(classId)")
        
        // 查找要删除的班级
        guard let classToDeleteObject = classes.first(where: { $0.id?.uuidString == classId }) else {
            print("未找到要删除的班级: \(classId)")
            return
        }
        
        // 设置要删除的班级并显示确认对话框
        classToDelete = classToDeleteObject
        showDeleteClassDialog = true
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 确认删除班级
     */
    private func confirmDeleteClass() {
        guard let classToDeleteObject = classToDelete else { return }
        
        isDeletingClass = true
        
        DispatchQueue.main.async {
            // 使用CoreData删除班级
            self.coreDataManager.deleteClass(classToDeleteObject)
            
            // 从本地数组中移除
            self.classes.removeAll { $0.id == classToDeleteObject.id }
            
            // 重置状态
            self.isDeletingClass = false
            self.showDeleteClassDialog = false
            self.classToDelete = nil
            
            print("班级删除成功: \(classToDeleteObject.name ?? "未知班级")")
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 取消删除班级
     */
    private func cancelDeleteClass() {
        showDeleteClassDialog = false
        classToDelete = nil
        print("取消删除班级")
    }
    
    // MARK: - Helper Methods
    
    /**
     * 计算班级管理组件的动态高度
     * 基于屏幕尺寸自适应计算
     */
    private func calculateClassManagementHeight(screenHeight: CGFloat) -> CGFloat {
        // 功能配置组件高度：标题24 + 卡片80×3 + 间距12×2 + 偏移-25 = 263pt
        let functionConfigHeight: CGFloat = 263
        // 底部边距：屏幕高度的15%
        let bottomMargin = screenHeight * DesignSystem.SettingsPage.FunctionCard.bottomMarginPercentage
        // 其他间距：顶部间距24 + 分隔线1 + VStack间距24×2 + 内边距16×2 = 109pt
        let otherSpacing: CGFloat = 109
        
        // 班级管理可用高度计算
        let availableHeight = screenHeight - functionConfigHeight - bottomMargin - otherSpacing
        
        // 设置合理的高度范围：最小180pt，最大400pt
        return max(180, min(400, availableHeight))
    }
    
    /**
     * 加载班级数据
     */
    private func loadClassesData() {
        classes = coreDataManager.getClasses(for: currentUser)
        print("加载班级数据完成，共 \(classes.count) 个班级")
    }
    
    /**
     * 将SchoolClass转换为ClassInfo
     */
    private func convertToClassInfo(_ schoolClasses: [SchoolClass]) -> [ClassInfo] {
        return schoolClasses.map { schoolClass in
            ClassInfo(
                id: schoolClass.id?.uuidString ?? UUID().uuidString,
                name: schoolClass.name ?? "未知班级",
                studentCount: schoolClass.studentCount,
                status: schoolClass.status ?? "active"
            )
        }
    }
}

// MARK: - Preview
#Preview {
    SettingsView(initialShowLotteryConfig: .constant(false))
} 