//
//  DeleteRecordConfirmationDialog.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 删除记录确认对话框
 * 显示记录详情和删除影响说明，提供确认和取消选项
 */
struct DeleteRecordConfirmationDialog: View {
    
    // MARK: - Properties
    let record: HistoryRecordProtocol
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 顶部标题区域
            HeaderSection()
            
            // 记录信息区域
            RecordInfoSection(record: record)
            
            // 影响说明区域
            ImpactSection(record: record)
            
            // 按钮区域
            ButtonsSection(
                onConfirm: onConfirm,
                onCancel: onCancel
            )
            .frame(height: 45) // 固定按钮区域高度
        }
        .background(Color.white)
        .cornerRadius(20)
        .shadow(color: Color.black.opacity(0.15), radius: 10, x: 0, y: 5)
        .padding(.horizontal, 40)
    }
}

/**
 * 对话框标题区域
 */
private struct HeaderSection: View {
    var body: some View {
        VStack(spacing: 8) {
            // 警告图标
            ZStack {
                Circle()
                    .fill(Color(hex: "#FF6B6B").opacity(0.1))
                    .frame(width: 60, height: 60)
                
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color(hex: "#FF6B6B"))
            }
            .padding(.top, 24)
            
            Text("确认删除记录")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("此操作无法撤销，请确认删除")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.bottom, 20)
    }
}

/**
 * 记录信息区域
 */
private struct RecordInfoSection: View {
    let record: HistoryRecordProtocol
    
    var body: some View {
        VStack(spacing: 16) {
            // 分割线
            Rectangle()
                .fill(Color(hex: "#F0F0F0"))
                .frame(height: 1)
            
            // 记录详情
            HStack(spacing: 16) {
                // 类型图标
                ZStack {
                    Circle()
                        .fill(record.recordType.typeColor.opacity(0.1))
                        .frame(width: 50, height: 50)
                    
                    Image(record.recordType.iconName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24)
                        .foregroundColor(record.recordType.typeColor)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    // 记录名称
                    Text(record.displayName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .lineLimit(2)
                    
                    // 记录时间
                    Text(record.formattedTime)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    // 记录类型
                    Text(record.recordType.displayName)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(record.recordType.typeColor)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(record.recordType.typeColor.opacity(0.1))
                        )
                }
                
                Spacer()
                
                // 积分显示
                if record.pointsValue != 0 {
                    Text(record.formattedPoints)
                        .font(.system(size: 15, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(
                                    record.pointsValue > 0 
                                    ? Color(hex: "#4CAF50") 
                                    : Color(hex: "#F44336")
                                )
                        )
                }
            }
            .padding(.horizontal, 20)
        }
    }
}

/**
 * 影响说明区域
 */
private struct ImpactSection: View {
    let record: HistoryRecordProtocol
    
    var body: some View {
        VStack(spacing: 12) {
            // 分割线
            Rectangle()
                .fill(Color(hex: "#F0F0F0"))
                .frame(height: 1)
            
            // 影响说明
            VStack(spacing: 8) {
                Text("删除影响")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(record.deleteImpactDescription)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.vertical, 16)
        }
    }
}

/**
 * 按钮区域
 */
private struct ButtonsSection: View {
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 分割线
            Rectangle()
                .fill(Color(hex: "#F0F0F0"))
                .frame(height: 1)
            
            HStack(spacing: 1) {
                // 取消按钮
                Button(action: onCancel) {
                    Text("取消")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(Color.white)
                }
                
                // 分割线
                Rectangle()
                    .fill(Color(hex: "#F0F0F0"))
                    .frame(width: 1)
                    .frame(height: 44)
                
                // 确认删除按钮
                Button(action: onConfirm) {
                    Text("确认删除")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color(hex: "#FF6B6B"))
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(Color.white)
                }
            }
            .frame(height: 44) // 确保整个按钮行高度固定
        }
    }
}

// MARK: - Preview
#Preview {
    let context = PersistenceController.preview.container.viewContext
    let student = Student(context: context)
    student.id = UUID()
    student.name = "张小明"
    student.point = 85
    
    let pointRecord = PointRecord(context: context)
    pointRecord.id = UUID()
    pointRecord.reason = "课堂表现优秀"
    pointRecord.value = 5
    pointRecord.timestamp = Date()
    pointRecord.isReversed = false
    pointRecord.student = student
    
    return ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        DeleteRecordConfirmationDialog(
            record: pointRecord,
            onConfirm: {
                print("确认删除")
            },
            onCancel: {
                print("取消删除")
            }
        )
    }
} 