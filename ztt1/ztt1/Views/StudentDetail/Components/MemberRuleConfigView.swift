//
//  MemberRuleConfigView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/28.
//  Copyright © 2025 ztt1. All rights reserved.
//

import SwiftUI

/**
 * 家庭成员规则配置弹窗组件
 *
 * ## 功能特性
 * - 支持批量添加成员专属规则
 * - 实时表单验证和错误提示
 * - 网络状态检查和错误处理
 * - 流畅的动画和触觉反馈
 * - 完整的本地化支持
 *
 * ## 使用场景
 * 在家庭成员详情页面，用户可以通过此组件为特定成员创建个性化的积分规则
 *
 * ## 兼容性
 * - iOS 15.6+
 * - 支持深色模式
 * - 支持动态字体大小
 */
struct MemberRuleConfigView: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    let operationType: StudentPointsOperation.OperationType
    let member: FamilyMember
    let ruleManager: FamilyMemberRuleManager
    let onSubmit: ([MemberRuleFormData]) -> Void
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var ruleForms: [MemberRuleFormData] = []
    @State private var isSubmitting: Bool = false
    @State private var validationErrors: [String] = []
    @State private var animationTrigger = false
    @State private var showNetworkError = false
    @State private var showUsageGuide = false
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        headerView
                        
                        // 分隔线
                        dividerView
                        
                        // 表单内容区域
                        ScrollView(.vertical, showsIndicators: true) {
                            VStack(spacing: 16) {
                                ForEach(ruleForms.indices, id: \.self) { index in
                                    MemberRuleFormRow(
                                        rule: $ruleForms[index],
                                        index: index,
                                        operationType: operationType,
                                        canDelete: ruleForms.count > 1,
                                        onDelete: {
                                            removeRuleForm(at: index)
                                        }
                                    )
                                    .disabled(isSubmitting)
                                    .transition(.asymmetric(
                                        insertion: .scale.combined(with: .opacity),
                                        removal: .scale.combined(with: .opacity)
                                    ))
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                        }
                        .frame(maxHeight: geometry.size.height * 0.5)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            dismissKeyboard()
                        }
                        
                        // 错误提示区域
                        if !validationErrors.isEmpty {
                            errorView
                        }
                        
                        // 底部操作按钮
                        bottomActionView
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 420))
                    .frame(maxHeight: geometry.size.height * 0.8)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: operationType.colorHex).opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                initializeForm()
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                initializeForm()
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
                resetForm()
            }
        }
        .alert("member_rule.error.network_title".localized, isPresented: $showNetworkError) {
            Button("member_rule.error.retry".localized) {
                showNetworkError = false
                submitRules()
            }
            Button("member_rule.config.cancel".localized, role: .cancel) {
                showNetworkError = false
            }
        } message: {
            Text("member_rule.error.network_message".localized)
        }
        .alert("使用说明", isPresented: $showUsageGuide) {
            Button("我知道了", role: .cancel) {
                showUsageGuide = false
            }
        } message: {
            Text("""
            • 规则名称：简洁明了，如"完成作业"、"迟到"
            • 分值设置：建议1-10分，避免过大差异
            • 常用规则：会显示在快速选择列表中
            • 可同时添加多条规则，提高配置效率
            """)
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 标题栏
     */
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(operationType == .add ?
                     "member_rule.config.add_title".localized :
                     "member_rule.config.deduct_title".localized)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("member_rule.config.subtitle".localized(with: member.name ?? "成员"))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.gray)
            }
            
            Spacer()

            HStack(spacing: 12) {
                // 帮助按钮
                Button(action: {
                    showUsageGuide = true
                }) {
                    Image(systemName: "questionmark.circle")
                        .font(.system(size: 22))
                        .foregroundColor(Color.gray)
                }
                .disabled(isSubmitting)

                // 添加规则按钮
                Button(action: {
                    // 触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    addNewRuleForm()
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 26))
                        .foregroundColor(Color(hex: operationType.colorHex))
                }
                .disabled(isSubmitting)
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 24)
        .padding(.bottom, 16)
        .onTapGesture {
            dismissKeyboard()
        }
    }
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 24)
    }
    
    /**
     * 错误提示区域
     */
    private var errorView: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(validationErrors, id: \.self) { error in
                Text(error)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(.red)
                    .multilineTextAlignment(.leading)
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .background(Color.red.opacity(0.05))
        .onTapGesture {
            dismissKeyboard()
        }
    }
    
    /**
     * 底部操作按钮
     */
    private var bottomActionView: some View {
        HStack(spacing: 16) {
            // 取消按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Text("member_rule.config.cancel".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
            }
            .disabled(isSubmitting)
            
            // 确认按钮
            Button(action: {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                submitRules()
            }) {
                HStack(spacing: 8) {
                    if isSubmitting {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }

                    Text(isSubmitting ?
                         "member_rule.config.submitting".localized :
                         "member_rule.config.confirm_add".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(
                    Color(hex: operationType.colorHex)
                        .opacity(isSubmitting || ruleForms.isEmpty ? 0.6 : 1.0)
                )
                .cornerRadius(12)
            }
            .disabled(isSubmitting || ruleForms.isEmpty)
            .animation(.easeInOut(duration: 0.2), value: isSubmitting)
        }
        .padding(.horizontal, 24)
        .padding(.top, 20)
        .padding(.bottom, 28)
    }
    
    // MARK: - Private Methods
    
    /**
     * 初始化表单
     */
    private func initializeForm() {
        print("🔧 [MemberRuleConfig] 初始化表单")
        print("📋 [MemberRuleConfig] operationType: \(operationType)")
        print("📋 [MemberRuleConfig] operationType.rawValue: \(operationType.rawValue)")

        let newForm = MemberRuleFormData(type: operationType.rawValue)
        print("✅ [MemberRuleConfig] 创建表单，类型: \(newForm.type)")

        ruleForms = [newForm]
        print("📊 [MemberRuleConfig] 初始化完成，表单数量: \(ruleForms.count)")
    }
    
    /**
     * 添加新的规则表单行
     */
    private func addNewRuleForm() {
        print("➕ [MemberRuleConfig] 添加新规则表单行")
        print("📋 [MemberRuleConfig] operationType.rawValue: \(operationType.rawValue)")

        let newForm = MemberRuleFormData(type: operationType.rawValue)
        print("✅ [MemberRuleConfig] 新表单创建，类型: \(newForm.type)")

        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            ruleForms.append(newForm)
        }
        print("📊 [MemberRuleConfig] 表单添加完成，总数量: \(ruleForms.count)")
    }
    
    /**
     * 移除指定索引的规则表单行
     */
    private func removeRuleForm(at index: Int) {
        if ruleForms.count > 1 {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                ruleForms.remove(at: index)
            }
        }
    }
    
    /**
     * 提交规则数据
     */
    private func submitRules() {
        print("📤 [MemberRuleConfig] 开始提交规则")
        print("📋 [MemberRuleConfig] operationType: \(operationType)")
        print("📊 [MemberRuleConfig] 表单数量: \(ruleForms.count)")

        // 打印每个表单的类型
        for (index, form) in ruleForms.enumerated() {
            print("📝 [MemberRuleConfig] 表单\(index + 1): 名称='\(form.name)', 分值='\(form.value)', 类型='\(form.type)'")
        }

        // 清除之前的错误
        validationErrors.removeAll()

        // 检查网络状态（简化处理）
        if !isNetworkAvailable() {
            showNetworkError = true
            return
        }

        // 验证表单数据
        let validationResult = validateForms()

        print("✅ [MemberRuleConfig] 验证结果: 有效=\(validationResult.isValid), 有效规则数=\(validationResult.validRules.count)")

        if validationResult.isValid {
            isSubmitting = true

            // 延迟提交，模拟处理时间和网络延迟
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // 模拟网络请求可能失败
                if shouldSimulateNetworkFailure() {
                    isSubmitting = false
                    showNetworkError = true
                } else {
                    print("📤 [MemberRuleConfig] 调用onSubmit回调")
                    onSubmit(validationResult.validRules)
                    isSubmitting = false
                }
            }
        } else {
            // 显示验证错误
            _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                validationErrors = validationResult.errorMessages
            }
        }
    }
    
    /**
     * 验证表单数据
     *
     * 执行以下验证：
     * 1. 规则名称非空且长度合理
     * 2. 分值为正整数且在合理范围内
     * 3. 检查是否与现有规则重复
     *
     * @return MemberRuleValidationResult 验证结果，包含错误信息和有效规则
     */
    private func validateForms() -> MemberRuleValidationResult {
        var validRules: [MemberRuleFormData] = []
        var errors: [String] = []
        
        // 获取现有规则用于重复检查
        let existingRules = ruleManager.getAllRules(for: member)
        
        for (index, form) in ruleForms.enumerated() {
            let trimmedName = form.name.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 验证规则名称
            if trimmedName.isEmpty {
                errors.append("规则\(index + 1): 规则名称不能为空")
                continue
            }
            
            if trimmedName.count > 20 {
                errors.append("规则\(index + 1): 规则名称不能超过20个字符")
                continue
            }
            
            // 验证分值
            guard let value = Int32(form.value), value > 0 else {
                errors.append("规则\(index + 1): 分值必须是大于0的整数")
                continue
            }
            
            if value > 100 {
                errors.append("规则\(index + 1): 分值不能超过100")
                continue
            }
            
            // 检查重复规则
            let isDuplicate = existingRules.contains { existingRule in
                existingRule.name?.lowercased() == trimmedName.lowercased() && 
                existingRule.type == form.type
            }
            
            if isDuplicate {
                errors.append("规则\(index + 1): 已存在同名的\(operationType.displayName)规则")
                continue
            }
            
            // 创建有效的规则数据
            var validForm = form
            validForm.name = trimmedName
            validForm.value = String(value)
            validRules.append(validForm)
        }
        
        return MemberRuleValidationResult(
            isValid: errors.isEmpty,
            errorMessages: errors,
            validRules: validRules
        )
    }
    
    /**
     * 重置表单
     */
    private func resetForm() {
        ruleForms = [MemberRuleFormData(type: operationType.rawValue)]
        validationErrors.removeAll()
        isSubmitting = false
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    /**
     * 检查网络可用性（简化实现）
     */
    private func isNetworkAvailable() -> Bool {
        // 这里可以集成真实的网络检查逻辑
        // 暂时返回true，实际应用中应该检查网络状态
        return true
    }

    /**
     * 模拟网络失败（用于测试）
     */
    private func shouldSimulateNetworkFailure() -> Bool {
        // 在调试模式下可以随机模拟网络失败
        #if DEBUG
        return Int.random(in: 1...10) == 1 // 10%概率模拟失败
        #else
        return false
        #endif
    }
}

// MARK: - 成员规则表单行组件

/**
 * 成员规则表单行组件
 */
struct MemberRuleFormRow: View {

    @Binding var rule: MemberRuleFormData
    let index: Int
    let operationType: StudentPointsOperation.OperationType
    let canDelete: Bool
    let onDelete: () -> Void

    @State private var isPressed = false

    var body: some View {
        VStack(spacing: 12) {
            // 行标题和删除按钮
            HStack {
                Text("规则 \(index + 1)")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                if canDelete {
                    Button(action: {
                        // 触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()

                        onDelete()
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.red.opacity(0.7))
                    }
                }
            }

            // 表单字段
            VStack(spacing: 12) {
                // 规则名称输入
                MemberRuleFormField(
                    title: "member_rule.config.rule_name".localized,
                    text: $rule.name,
                    placeholder: operationType == .add ?
                        "member_rule.config.add_placeholder".localized :
                        "member_rule.config.deduct_placeholder".localized
                )

                // 分值输入
                MemberRuleFormField(
                    title: "member_rule.config.rule_value".localized,
                    text: $rule.value,
                    placeholder: "member_rule.config.value_placeholder".localized,
                    keyboardType: .numberPad
                )
            }
        }
        .padding(16)
        .background(
            Color(hex: operationType.colorHex)
                .opacity(isPressed ? 0.1 : 0.05)
        )
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: operationType.colorHex).opacity(0.2), lineWidth: 1)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
        .onTapGesture {
            // 点击表单行时的触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

/**
 * 成员规则表单字段组件
 */
struct MemberRuleFormField: View {

    let title: String
    @Binding var text: String
    let placeholder: String
    var keyboardType: UIKeyboardType = .default

    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            TextField(placeholder, text: $text)
                .keyboardType(keyboardType)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
}

// MARK: - 数据结构

/**
 * 成员规则表单数据
 */
struct MemberRuleFormData {
    var name: String = ""
    var value: String = ""
    var type: String
    var isFrequent: Bool = true // 默认设为常用规则

    init(type: String) {
        self.type = type
    }
}

/**
 * 成员规则验证结果
 */
struct MemberRuleValidationResult {
    let isValid: Bool
    let errorMessages: [String]
    let validRules: [MemberRuleFormData]
}
