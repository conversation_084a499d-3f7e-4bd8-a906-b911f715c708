//
//  StudentRedemptionFormView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 自定义文本框样式（兑换专用）
 */
struct StudentRedemptionTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
            )
    }
}

/**
 * 学生自定义兑换表单视图
 */
struct StudentRedemptionFormView: View {
    
    // MARK: - Properties
    
    @Binding var isPresented: Bool
    @StateObject private var formData: StudentRedemptionFormData
    let onConfirm: (StudentRedemptionOperation) -> Void
    let onDismiss: () -> Void
    
    // MARK: - Private Properties
    
    @State private var showingConfirmation = false
    @State private var animationTrigger = false
    
    // MARK: - Initialization
    
    init(isPresented: Binding<Bool>, student: Student, onConfirm: @escaping (StudentRedemptionOperation) -> Void, onDismiss: @escaping () -> Void) {
        self._isPresented = isPresented
        self._formData = StateObject(wrappedValue: StudentRedemptionFormData(studentPoints: Int(student.point)))
        self.onConfirm = onConfirm
        self.onDismiss = onDismiss
    }
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    // 注释掉点击外部区域关闭弹窗的手势，防止误操作
                    // .onTapGesture {
                    //     withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    //         onDismiss()
                    //     }
                    // }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    NavigationView {
                        VStack(spacing: 0) {
                            // 内容区域
                            contentView
                            
                            // 底部操作栏
                            bottomActionBar
                        }
                        .background(Color(.systemGroupedBackground))
                        .navigationTitle("redemption.form.title".localized)
                        .navigationBarTitleDisplayMode(.inline)
                        .navigationBarBackButtonHidden(true)
                        .toolbar {
                            ToolbarItem(placement: .navigationBarLeading) {
                                Button("common.cancel".localized) {
                                    dismissKeyboard()
                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                        onDismiss()
                                    }
                                }
                                .foregroundColor(.secondary)
                            }
                        }
                    }
                    .navigationViewStyle(StackNavigationViewStyle())
                    .frame(maxWidth: min(geometry.size.width - 40, 400))
                    .frame(maxHeight: min(geometry.size.height * 0.9, 700))
                    .background(Color(.systemBackground))
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.green.opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - Views
    
    /**
     * 内容视图
     */
    private var contentView: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // 提示信息
                infoSection
                
                // 兑换项目列表
                redemptionItemsSection
                
                // 添加按钮
                addButtonSection
                
                // 底部间距
                Spacer(minLength: 100)
            }
            .padding(.top, 20)
        }
        .modifier(ScrollIndicatorModifier())
    }
    
    /**
     * 信息提示区域
     */
    private var infoSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: "info.circle.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("redemption.form.info.title".localized)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text("redemption.form.info.subtitle".localized(with: "\(formData.studentPoints)"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue.opacity(0.1))
            )
            .padding(.horizontal, 20)
        }
    }
    
    /**
     * 兑换项目列表区域
     */
    private var redemptionItemsSection: some View {
        VStack(spacing: 16) {
            // 列表标题
            HStack {
                Text("redemption.form.items.title".localized)
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            .padding(.horizontal, 20)
            
            // 兑换项目列表
            ForEach(Array(formData.items.enumerated()), id: \.offset) { index, item in
                redemptionItemCard(at: index, item: item)
            }
        }
    }
    
    /**
     * 兑换项目卡片
     */
    private func redemptionItemCard(at index: Int, item: StudentRedemptionOperationItem) -> some View {
        VStack(spacing: 16) {
            // 卡片头部
            HStack {
                Text("redemption.form.item.title".localized(with: "\(index + 1)"))
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 删除按钮
                if formData.items.count > 1 {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            formData.removeItem(at: index)
                        }
                    }) {
                        Image(systemName: "trash.circle.fill")
                            .font(.title3)
                            .foregroundColor(.red)
                    }
                }
            }
            
            // 名称输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("redemption.form.item.name".localized)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                TextField("redemption.form.item.name.placeholder".localized, text: Binding(
                    get: { formData.items[safe: index]?.name ?? "" },
                    set: { formData.updateItemName(at: index, name: $0) }
                ))
                .textFieldStyle(StudentRedemptionTextFieldStyle())
            }
            
            // 积分消耗输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("redemption.form.item.cost".localized)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                TextField("redemption.form.item.cost.placeholder".localized, text: Binding(
                    get: { formData.getItemCostString(at: index) },
                    set: { formData.updateItemCostString(at: index, costString: $0) }
                ))
                .keyboardType(.numberPad)
                .textFieldStyle(StudentRedemptionTextFieldStyle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal, 20)
        .contentShape(Rectangle()) // 确保整个卡片区域能响应手势
        .onTapGesture {
            // 点击卡片区域关闭键盘
            dismissKeyboard()
        }
    }
    
    /**
     * 添加按钮区域
     */
    private var addButtonSection: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                formData.addItem()
            }
            
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            HStack(spacing: 12) {
                Image(systemName: "plus.circle.fill")
                    .font(.title3)
                    .foregroundColor(.white)
                
                Text("redemption.form.add_item".localized)
                    .font(.headline)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(LinearGradient(colors: [Color.green, Color.teal], 
                                       startPoint: .leading, endPoint: .trailing))
            )
            .shadow(color: .green.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .padding(.horizontal, 20)
    }
    
    /**
     * 底部操作栏
     */
    private var bottomActionBar: some View {
        VStack(spacing: 16) {
            // 错误信息显示
            if let error = formData.validationError {
                HStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.caption)
                        .foregroundColor(.red)
                    
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
            }
            
            // 总计信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("redemption.form.summary.title".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 4) {
                        Text("redemption.form.summary.total".localized)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("\(formData.totalCost)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(formData.canAfford ? .orange : .red)
                        
                        Text("redemption.form.summary.points".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 确认按钮
                Button(action: {
                    dismissKeyboard()
                    handleConfirmAction()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Text("redemption.form.confirm".localized)
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(formData.hasValidData ? 
                                  LinearGradient(colors: [Color.blue, Color.purple], 
                                               startPoint: .leading, endPoint: .trailing) :
                                  LinearGradient(colors: [Color.gray.opacity(0.6), Color.gray.opacity(0.4)], 
                                               startPoint: .leading, endPoint: .trailing))
                    )
                    .shadow(color: (formData.hasValidData ? Color.blue : Color.gray).opacity(0.3), 
                           radius: 8, x: 0, y: 4)
                }
                .disabled(!formData.hasValidData)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(
            Rectangle()
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
        )
    }
    
    // MARK: - Actions
    
    /**
     * 处理确认操作
     */
    private func handleConfirmAction() {
        // 验证表单
        let validation = formData.validateForm()
        
        if validation.isValid {
            // 创建兑换操作
            let operation = StudentRedemptionOperation(items: formData.validItems)
            
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            // 执行确认回调
            onConfirm(operation)
        } else {
            // 显示验证错误
            if let errorMessage = validation.errorMessage {
                formData.setValidationError(errorMessage)
            }
            
            // 错误反馈
            let errorFeedback = UINotificationFeedbackGenerator()
            errorFeedback.notificationOccurred(.error)
        }
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - Array Extension

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// MARK: - Custom Modifier

struct ScrollIndicatorModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(iOS 16.0, *) {
            content.scrollIndicators(.hidden)
        } else {
            content
        }
    }
} 