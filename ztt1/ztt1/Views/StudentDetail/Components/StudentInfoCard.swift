//
//  StudentInfoCard.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 学生信息卡片组件
 * 包含学生头像、基本信息、操作按钮网格和积分显示
 */
struct StudentInfoCard: View {
    
    // MARK: - Properties
    let student: Student
    let onAddPointsTapped: () -> Void
    let onDeductPointsTapped: () -> Void
    let onExchangeTapped: () -> Void
    let onLotteryTapped: () -> Void
    let onAnalysisReportTapped: () -> Void
    
    // MARK: - Body
    var body: some View {
        cardContent
    }
    
    /**
     * 卡片内容视图
     */
    private var cardContent: some View {
        ZStack {
            // 美化背景容器
            RoundedRectangle(cornerRadius: 25)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#f8ffe5"),
                            Color(hex: "#edf6d9")
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(height: calculateCardHeight())
                .shadow(color: Color(hex: "#a9d051").opacity(0.15), radius: 8, x: 0, y: 4)
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#a9d051").opacity(0.2),
                                    Color(hex: "#74c07f").opacity(0.1)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
            
            // 装饰性背景元素
            Circle()
                .fill(Color(hex: "#B5E36B").opacity(0.08))
                .frame(width: 60, height: 60)
                .offset(x: 80, y: -40)
            
            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.06))
                .frame(width: 45, height: 45)
                .offset(x: -90, y: 60)
            
            Circle()
                .fill(Color(hex: "#74c07f").opacity(0.05))
                .frame(width: 35, height: 35)
                .offset(x: 100, y: 50)
            
            // 学生头像 - 左上角
            HStack {
                VStack {
                    ZStack {
                        // 头像背景光圈
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#a9d051").opacity(0.1),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 50
                                )
                            )
                            .frame(width: 100, height: 100)
                        
                        Group {
                            let avatarName = student.avatarImageName
                            let _ = print("🖼️ StudentInfoCard - 尝试显示头像: \(avatarName)")
                            let _ = AvatarTestHelper.testStudentAvatarImageName(student: student)

                            // 尝试多种头像加载方式
                            if let uiImage = UIImage(named: avatarName) {
                                let _ = print("✅ 成功加载头像: \(avatarName)")
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 90, height: 90)
                                    .background(
                                        RoundedRectangle(cornerRadius: 15)
                                            .fill(Color.white.opacity(0.5))
                                    )
                                    .clipShape(RoundedRectangle(cornerRadius: 8))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                                    )
                                    .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                            } else {
                                let _ = print("❌ 无法加载头像: \(avatarName)")

                                // 计算备用头像名称
                                let fallbackAvatarName = getFallbackAvatarName(for: student)
                                let _ = print("🔄 尝试备用头像: \(fallbackAvatarName)")

                                if let fallbackImage = UIImage(named: fallbackAvatarName) {
                                    let _ = print("✅ 成功加载备用头像: \(fallbackAvatarName)")
                                    Image(uiImage: fallbackImage)
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                        .frame(width: 90, height: 90)
                                        .background(
                                            RoundedRectangle(cornerRadius: 15)
                                                .fill(Color.white.opacity(0.5))
                                        )
                                        .clipShape(RoundedRectangle(cornerRadius: 8))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(Color.white.opacity(0.3), lineWidth: 2)
                                        )
                                        .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                                } else {
                                    let _ = print("❌ 备用头像也无法加载: \(fallbackAvatarName)")
                                    // 显示占位符
                                    ZStack {
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.gray.opacity(0.3))
                                            .frame(width: 90, height: 90)

                                        VStack {
                                            Image(systemName: "person.circle")
                                                .font(.system(size: 40))
                                                .foregroundColor(.gray)
                                            Text("头像缺失")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                        }
                                    }
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                                    )
                                    .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                                }
                            }
                        }
                    }
                    .padding(.top, DesignSystem.Spacing.md)
                    .padding(.leading, DesignSystem.Spacing.md)
                    Spacer()
                }
                Spacer()
            }
            
            // 学生信息 - 中上区域
            HStack {
                Spacer()
                VStack(alignment: .leading, spacing: 4) {
                    // 学生姓名
                    Text(student.name ?? "student.info.unknown_name".localized)
                        .font(.system(size: 30, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .shadow(color: Color.white.opacity(0.5), radius: 1, x: 0, y: 1)
                        .padding(.top, DesignSystem.Spacing.md)

                    // 角色和年龄信息（如果是家庭成员）或班级信息（如果是学生）
                    if student.isFamilyMemberAdapter,
                       let role = student.roleDisplayName,
                       let age = student.ageValue {
                        Text("student.info.role_age_format".localized(with: role, age))
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    } else {
                        Text("student.info.class_grade".localized)
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }

                    // 学号（仅对真实学生显示）
                    if !student.isFamilyMemberAdapter {
                        Text(student.studentNumber ?? "student.info.default_number".localized)
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }

                    Spacer()
                }
                Spacer()
            }
            
            // 积分显示 - 右下角
            HStack {
                Spacer()
                VStack {
                    Spacer()
                    ZStack {
                        // 积分背景光圈
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#74c07f").opacity(0.1),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 60
                                )
                            )
                            .frame(width: 120, height: 120)
                        
                        Text("\(student.point)")
                            .font(.custom("Impact", size: 90))
                            .foregroundColor(DesignSystem.Colors.scoreDisplay)
                            .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                    .padding(.bottom, DesignSystem.Spacing.md)
                    .padding(.trailing, DesignSystem.Spacing.md)
                    .offset(y: 20)  // 向下移动20pt
                }
            }
            
            // 上排操作按钮 - 左下区域上方
            HStack {
                VStack {
                    Spacer()
                    TopActionButtons(
                        onAddPointsTapped: onAddPointsTapped,
                        onDeductPointsTapped: onDeductPointsTapped,
                        buttonSize: 50,
                        spacing: 5,
                        cornerRadius: 5
                    )
                    .padding(.bottom, 70)  // 为下排按钮留出空间
                    .padding(.leading, DesignSystem.Spacing.md)
                }
                Spacer()
            }
            
            // 下排操作按钮 - 左下角
            HStack {
                VStack {
                    Spacer()
                    BottomActionButtons(
                        student: student,
                        onExchangeTapped: onExchangeTapped,
                        onLotteryTapped: onLotteryTapped,
                        onAnalysisReportTapped: onAnalysisReportTapped,
                        buttonSize: 50,
                        spacing: 5,
                        cornerRadius: 5
                    )
                    .padding(.bottom, DesignSystem.Spacing.md)
                    .padding(.leading, DesignSystem.Spacing.md)
                }
                Spacer()
            }
        }
        .frame(height: calculateCardHeight())
    }
    
    /**
     * 计算卡片高度（基于屏幕高度的百分比）
     * @return 计算后的卡片高度
     */
    private func calculateCardHeight() -> CGFloat {
        // 获取屏幕高度
        let screenHeight = UIScreen.main.bounds.height
        
        // 使用屏幕高度的20%作为卡片高度，最小140pt，最大200pt
        let percentageHeight = screenHeight * 0.40
        return max(140, min(280, percentageHeight))
    }
}

// MARK: - Preview
#Preview {
    let context = PersistenceController.preview.container.viewContext
    let student = Student(context: context)
    student.id = UUID()
    student.name = "张小明"
    student.studentNumber = "001"
    student.gender = "male"
    student.point = 85
    student.createdAt = Date()
    
    return StudentInfoCard(
        student: student,
        onAddPointsTapped: { print("加分") },
        onDeductPointsTapped: { print("扣分") },
        onExchangeTapped: { print("兑换") },
        onLotteryTapped: { print("抽奖") },
        onAnalysisReportTapped: { print("分析报告") }
    )
    .padding()
}

// MARK: - Helper Functions

/**
 * 获取备用头像名称
 */
private func getFallbackAvatarName(for student: Student) -> String {
    if student.isFamilyMemberAdapter {
        // 对于FamilyMember适配对象，尝试根据角色获取头像
        if let studentId = student.id,
           let familyMember = CoreDataManager.shared.getFamilyMember(by: studentId) {
            let role = FamilyRole(rawValue: familyMember.role ?? "other") ?? .other
            let avatarName = role.defaultAvatarName
            print("🔄 FamilyMember备用头像 - 角色: \(familyMember.role ?? "other"), 头像: \(avatarName)")
            return avatarName
        } else {
            let avatarName = student.genderEnum.defaultAvatar
            print("🔄 无法找到FamilyMember，使用性别头像: \(avatarName)")
            return avatarName
        }
    } else {
        let avatarName = student.genderEnum.defaultAvatar
        print("🔄 普通Student备用头像: \(avatarName)")
        return avatarName
    }
}