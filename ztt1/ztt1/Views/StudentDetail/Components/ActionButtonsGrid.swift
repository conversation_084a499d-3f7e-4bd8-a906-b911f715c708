//
//  ActionButtonsGrid.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 操作按钮网格组件
 * 包含5个操作按钮：加分、扣分、兑换、抽奖、分析报告
 */
struct ActionButtonsGrid: View {
    
    // MARK: - Properties
    let onAddPointsTapped: () -> Void
    let onDeductPointsTapped: () -> Void
    let onExchangeTapped: () -> Void
    let onLotteryTapped: () -> Void
    let onAnalysisReportTapped: () -> Void
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            // 第一行：加分和扣分按钮
            HStack(spacing: DesignSystem.Spacing.sm) {
                ActionButton(
                    title: "student_detail.action.add_points".localized,
                    systemIcon: "plus",
                    action: onAddPointsTapped
                )
                
                ActionButton(
                    title: "student_detail.action.deduct_points".localized,
                    systemIcon: "minus",
                    action: onDeductPointsTapped
                )
            }
            
            // 第二行：兑换、抽奖、分析报告按钮
            HStack(spacing: DesignSystem.Spacing.sm) {
                ActionButton(
                    title: "student_detail.action.exchange".localized,
                    imageName: "lingqujilu",
                    action: onExchangeTapped
                )
                
                ActionButton(
                    title: "student_detail.action.lottery".localized,
                    imageName: "choujiang",
                    action: onLotteryTapped
                )
                
                ActionButton(
                    title: "student_detail.action.analysis".localized,
                    imageName: "fenxi",
                    action: onAnalysisReportTapped
                )
            }
        }
    }
}

/**
 * 上排操作按钮组件（加分、扣分）
 */
struct TopActionButtons: View {
    
    // MARK: - Properties
    let onAddPointsTapped: () -> Void
    let onDeductPointsTapped: () -> Void
    let buttonSize: CGFloat
    let spacing: CGFloat
    let cornerRadius: CGFloat
    
    // MARK: - Initialization
    init(
        onAddPointsTapped: @escaping () -> Void,
        onDeductPointsTapped: @escaping () -> Void,
        buttonSize: CGFloat = 40,
        spacing: CGFloat = 8,
        cornerRadius: CGFloat = 8
    ) {
        self.onAddPointsTapped = onAddPointsTapped
        self.onDeductPointsTapped = onDeductPointsTapped
        self.buttonSize = buttonSize
        self.spacing = spacing
        self.cornerRadius = cornerRadius
    }
    
    // MARK: - Body
    var body: some View {
        HStack(spacing: spacing) {
            CustomActionButton(
                title: "student_detail.action.add_points".localized,
                systemIcon: "plus",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onAddPointsTapped
            )
            
            CustomActionButton(
                title: "student_detail.action.deduct_points".localized,
                systemIcon: "minus",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onDeductPointsTapped
            )
        }
    }
}

/**
 * 下排操作按钮组件（兑换、抽奖、分析报告）
 */
struct BottomActionButtons: View {

    // MARK: - Properties
    let student: Student
    let onExchangeTapped: () -> Void
    let onLotteryTapped: () -> Void
    let onAnalysisReportTapped: () -> Void
    let buttonSize: CGFloat
    let spacing: CGFloat
    let cornerRadius: CGFloat

    // MARK: - Initialization
    init(
        student: Student,
        onExchangeTapped: @escaping () -> Void,
        onLotteryTapped: @escaping () -> Void,
        onAnalysisReportTapped: @escaping () -> Void,
        buttonSize: CGFloat = 40,
        spacing: CGFloat = 8,
        cornerRadius: CGFloat = 8
    ) {
        self.student = student
        self.onExchangeTapped = onExchangeTapped
        self.onLotteryTapped = onLotteryTapped
        self.onAnalysisReportTapped = onAnalysisReportTapped
        self.buttonSize = buttonSize
        self.spacing = spacing
        self.cornerRadius = cornerRadius
    }
    
    // MARK: - Body
    var body: some View {
        HStack(spacing: spacing) {
            CustomActionButton(
                title: "student_detail.action.exchange".localized,
                imageName: "lingqujilu",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onExchangeTapped
            )

            CustomActionButton(
                title: "student_detail.action.lottery".localized,
                imageName: "choujiang",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onLotteryTapped
            )

            // AI分析按钮只在角色为"儿子"或"女儿"时显示
            if shouldShowAnalysisButton {
                CustomActionButton(
                    title: "student_detail.action.analysis".localized,
                    imageName: "fenxi",
                    size: buttonSize,
                    cornerRadius: cornerRadius,
                    action: onAnalysisReportTapped
                )
            }
        }
    }

    // MARK: - Private Methods

    /**
     * 判断是否应该显示AI分析按钮
     * 只有角色为"儿子"或"女儿"时才显示
     */
    private var shouldShowAnalysisButton: Bool {
        return BottomActionButtons.shouldShowAnalysisButton(for: student)
    }

    // MARK: - Static Methods

    /**
     * 静态方法：判断是否应该显示AI分析按钮
     * 只有角色为"儿子"或"女儿"时才显示
     */
    static func shouldShowAnalysisButton(for student: Student) -> Bool {
        // 检查是否为FamilyMember适配对象
        if student.isFamilyMemberAdapter,
           let studentId = student.id,
           let familyMember = CoreDataManager.shared.getFamilyMember(by: studentId) {
            let role = FamilyRole(rawValue: familyMember.role ?? "other") ?? .other
            print("🔍 检查AI分析按钮显示权限 - 角色: \(role.shortName), 是否显示: \(role == .son || role == .daughter)")
            return role == .son || role == .daughter
        }

        // 对于普通Student对象，默认显示（保持向后兼容）
        print("🔍 普通Student对象，默认显示AI分析按钮")
        return true
    }
}

/**
 * 可配置的操作按钮组件
 */
struct CustomActionButton: View {
    let title: String
    let systemIcon: String?
    let imageName: String?
    let size: CGFloat
    let cornerRadius: CGFloat
    let action: () -> Void
    
    @State private var isPressed = false
    
    init(title: String, systemIcon: String, size: CGFloat, cornerRadius: CGFloat, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = systemIcon
        self.imageName = nil
        self.size = size
        self.cornerRadius = cornerRadius
        self.action = action
    }
    
    init(title: String, imageName: String, size: CGFloat, cornerRadius: CGFloat, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = nil
        self.imageName = imageName
        self.size = size
        self.cornerRadius = cornerRadius
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isPressed = false
                action()
            }
        }) {
            VStack(spacing: 4) {
                // 图标
                if let systemIcon = systemIcon {
                    Image(systemName: systemIcon)
                        .font(.system(size: size * 0.35, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: size * 0.4, height: size * 0.4)
                        .offset(y: systemIcon == "minus" ? -1 : 0)
                } else if let imageName = imageName {
                    Image(imageName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: size * 0.35, height: size * 0.35)
                        .foregroundColor(.white)
                }
                
                // 标题
                Text(title)
                    .font(.system(size: getFontSize(), weight: .medium))
                    .foregroundColor(.white)
                    .lineLimit(1)
                    .minimumScaleFactor(0.6)
                    .frame(maxWidth: .infinity)
            }
        }
        .frame(width: size, height: size)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#a9d051"),
                    Color(hex: "#8bb83f")
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(cornerRadius)
        .shadow(color: Color(hex: "#a9d051").opacity(isPressed ? 0.4 : 0.25), radius: isPressed ? 6 : 4, x: 0, y: isPressed ? 3 : 2)
        .overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        .buttonStyle(PlainButtonStyle())
    }
    
    /**
     * 根据按钮标题获取合适的字体大小
     */
    private func getFontSize() -> CGFloat {
        switch title {
        case "student_detail.action.analysis".localized:
            return size * 0.20
        default:
            return size * 0.25
        }
    }
}

/**
 * 单个操作按钮组件（保持向后兼容）
 */
private struct ActionButton: View {
    let title: String
    let systemIcon: String?
    let imageName: String?
    let action: () -> Void
    
    init(title: String, systemIcon: String, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = systemIcon
        self.imageName = nil
        self.action = action
    }
    
    init(title: String, imageName: String, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = nil
        self.imageName = imageName
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                // 图标
                if let systemIcon = systemIcon {
                    Image(systemName: systemIcon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                } else if let imageName = imageName {
                    Image(imageName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 16, height: 16)
                        .foregroundColor(.white)
                }
                
                // 标题
                Text(title)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.white)
                    .lineLimit(1)
            }
        }
        .frame(width: 40, height: 40)
        .background(DesignSystem.Colors.actionButtonBackground)
        .cornerRadius(8)
    }
}

// MARK: - Preview
#Preview("ActionButtonsGrid") {
    ActionButtonsGrid(
        onAddPointsTapped: { print("加分") },
        onDeductPointsTapped: { print("扣分") },
        onExchangeTapped: { print("兑换") },
        onLotteryTapped: { print("抽奖") },
        onAnalysisReportTapped: { print("分析报告") }
    )
    .padding()
}

#Preview("TopActionButtons") {
    TopActionButtons(
        onAddPointsTapped: { print("加分") },
        onDeductPointsTapped: { print("扣分") },
        buttonSize: 50,
        spacing: 12,
        cornerRadius: 12
    )
    .padding()
}

#Preview("BottomActionButtons") {
    let context = PersistenceController.preview.container.viewContext
    let student = Student(context: context)
    student.id = UUID()
    student.name = "张小明"
    student.studentNumber = "001"
    student.gender = "male"
    student.point = 85
    student.createdAt = Date()

    return BottomActionButtons(
        student: student,
        onExchangeTapped: { print("兑换") },
        onLotteryTapped: { print("抽奖") },
        onAnalysisReportTapped: { print("分析报告") },
        buttonSize: 50,
        spacing: 12,
        cornerRadius: 12
    )
    .padding()
}