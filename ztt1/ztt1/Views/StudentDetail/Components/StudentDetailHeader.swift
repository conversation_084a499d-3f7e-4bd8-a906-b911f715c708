//
//  StudentDetailHeader.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 学生详情页面头部组件
 * 包含右上角关闭按钮
 */
struct StudentDetailHeader: View {
    
    // MARK: - Properties
    let onClose: () -> Void
    
    // MARK: - Body
    var body: some View {
        HStack {
            Spacer()
            
            // 关闭按钮
            Button(action: onClose) {
                Image("关闭")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 30, height: 30)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            .frame(width: 44, height: 44)  // 扩大点击区域
            .background(Color.clear)
        }
        .padding(.horizontal, 25)  // 设置两侧边距为25pt
        .padding(.top, -10)  // 减少顶部间距，从8pt减少到4pt
    }
}

// MARK: - Preview
#Preview {
    StudentDetailHeader {
        print("关闭按钮点击")
    }
} 