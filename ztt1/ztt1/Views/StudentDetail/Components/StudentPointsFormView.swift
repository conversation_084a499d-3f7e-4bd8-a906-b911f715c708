//
//  StudentPointsFormView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 学生积分操作自定义表单组件
 * 支持多行表单输入、动态添加删除、验证和批量提交
 */
struct StudentPointsFormView: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    @State var formData: StudentPointsFormData
    let onSubmit: ([StudentPointsOperation]) -> Void
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var animationTrigger = false
    @State private var showValidationErrors = false
    @State private var validationResult: StudentPointsFormValidationResult?
    @State private var isSubmitting = false
    @FocusState private var focusedField: UUID?
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        dismissKeyboard()
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(spacing: 0) {
                            // 标题栏
                            headerView
                            
                            // 分隔线
                            dividerView
                            
                            // 表单区域
                            formSection
                            
                            // 操作预览区域
                            previewSection
                            
                            // 底部按钮区域
                            buttonsSection
                        }
                        .frame(maxWidth: min(geometry.size.width - 30, 380))
                        .background(Color.white)
                        .cornerRadius(20)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color(hex: formData.operationType.colorHex).opacity(0.2), lineWidth: 1.5)
                        )
                        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
                        .scaleEffect(animationTrigger ? 1.0 : 0.85)
                        .opacity(animationTrigger ? 1.0 : 0.0)
                        .padding(.horizontal, 15)
                        .padding(.vertical, max(20, (geometry.size.height - 600) / 2))
                    }
                    .frame(maxHeight: geometry.size.height)
                }
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
                dismissKeyboard()
            }
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 标题栏
     */
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("student_points.form.title".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(formData.operationType == .add ? "student_points.form.description_add".localized : "student_points.form.description_deduct".localized)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // 关闭按钮
            Button(action: {
                dismissKeyboard()
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 20)
    }
    
    /**
     * 表单区域
     */
    private var formSection: some View {
        VStack(spacing: 16) {
            // 区域标题
            HStack {
                Text("student_points.form.items_title".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 添加按钮
                if formData.canAddMoreItems {
                    Button(action: addNewItem) {
                        HStack(spacing: 4) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 14))
                            Text("student_points.form.add_item".localized)
                                .font(.system(size: 14, weight: .medium))
                        }
                        .foregroundColor(Color(hex: formData.operationType.colorHex))
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            // 表单项列表
            VStack(spacing: 12) {
                ForEach(Array(formData.items.enumerated()), id: \.element.id) { index, item in
                    FormItemRow(
                        item: binding(for: item),
                        index: index,
                        operationType: formData.operationType,
                        canDelete: formData.canDeleteItems,
                        focusedField: $focusedField,
                        onDelete: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                removeItem(at: index)
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            
            // 验证错误显示
            if showValidationErrors, let result = validationResult, !result.isValid {
                ValidationErrorsView(errorMessages: result.errorMessages)
                    .padding(.horizontal, 20)
                    .transition(.scale.combined(with: .opacity))
            }
        }
    }
    
    /**
     * 操作预览区域
     */
    private var previewSection: some View {
        Group {
            if formData.hasValidData {
                VStack(spacing: 12) {
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 预览内容
                    HStack {
                        Text("student_points.form.preview_title".localized)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                        
                        Text("\(formData.totalPointsChange > 0 ? "+" : "")\(formData.totalPointsChange) 分")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(Color(hex: formData.operationType.colorHex))
                    }
                    .padding(.horizontal, 20)
                    
                    Text("将执行 \(formData.validItems.count) 个\(formData.operationType.displayName)操作")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(.horizontal, 20)
                }
                .padding(.top, 16)
            }
        }
    }
    
    /**
     * 底部按钮区域
     */
    private var buttonsSection: some View {
        HStack(spacing: 12) {
            // 取消按钮
            Button(action: {
                dismissKeyboard()
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Text("student_points.form.cancel".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
            }
            
            // 确认按钮
            Button(action: submitForm) {
                HStack(spacing: 8) {
                    if isSubmitting {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image(systemName: "checkmark.circle")
                            .font(.system(size: 16, weight: .medium))
                    }
                    
                    Text(isSubmitting ? "student_points.form.submitting".localized : "student_points.form.confirm".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: formData.operationType.colorHex),
                            Color(hex: formData.operationType.colorHex).opacity(0.8)
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
                .shadow(color: Color(hex: formData.operationType.colorHex).opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(!formData.hasValidData || isSubmitting)
            .opacity(formData.hasValidData && !isSubmitting ? 1.0 : 0.6)
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 24)
    }
    
    // MARK: - 辅助方法
    
    /**
     * 获取表单项的绑定
     */
    private func binding(for item: StudentPointsFormData.FormItem) -> Binding<StudentPointsFormData.FormItem> {
        guard let index = formData.items.firstIndex(where: { $0.id == item.id }) else {
            return .constant(item)
        }
        return $formData.items[index]
    }
    
    /**
     * 添加新表单项
     */
    private func addNewItem() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            formData.addNewItem()
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 删除表单项
     */
    private func removeItem(at index: Int) {
        formData.removeItem(at: index)
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 提交表单
     */
    private func submitForm() {
        dismissKeyboard()
        
        // 验证表单
        let result = formData.validateItems()
        validationResult = result
        
        if !result.isValid {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                showValidationErrors = true
            }
            
            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            return
        }
        
        // 提交数据
        isSubmitting = true
        let operations = formData.toOperations()
        
        // 模拟提交延迟（实际使用中可以移除）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isSubmitting = false
            
            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)
            
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                onSubmit(operations)
            }
        }
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        focusedField = nil
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

/**
 * 表单项行组件
 */
struct FormItemRow: View {
    
    @Binding var item: StudentPointsFormData.FormItem
    let index: Int
    let operationType: StudentPointsOperation.OperationType
    let canDelete: Bool
    @FocusState.Binding var focusedField: UUID?
    let onDelete: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 行标题
            HStack {
                Text("第 \(index + 1) 项")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 删除按钮
                if canDelete {
                    Button(action: onDelete) {
                        Image(systemName: "trash.circle.fill")
                            .font(.system(size: 18))
                            .foregroundColor(Color.red.opacity(0.7))
                    }
                }
            }
            
            // 输入区域
            VStack(spacing: 8) {
                // 名称输入
                VStack(alignment: .leading, spacing: 4) {
                    Text("student_points.form.name_label".localized)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    TextField("student_points.form.name_placeholder".localized, text: $item.name)
                        .textFieldStyle(StudentPointsTextFieldStyle())
                        .focused($focusedField, equals: item.id)
                }
                
                // 分值输入
                VStack(alignment: .leading, spacing: 4) {
                    Text("student_points.form.value_label".localized)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    TextField("student_points.form.value_placeholder".localized, text: $item.value)
                        .keyboardType(.numberPad)
                        .textFieldStyle(StudentPointsTextFieldStyle())
                        .focused($focusedField, equals: UUID(uuidString: item.id.uuidString + "_value"))
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(hex: operationType.colorHex).opacity(0.05))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: operationType.colorHex).opacity(0.2), lineWidth: 1)
        )
    }
}

/**
 * 验证错误显示组件
 */
struct ValidationErrorsView: View {
    
    let errorMessages: [String]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 14))
                    .foregroundColor(.red)
                
                Text("student_points.form.validation_errors".localized)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.red)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                ForEach(errorMessages, id: \.self) { message in
                    Text("• \(message)")
                        .font(.system(size: 13))
                        .foregroundColor(.red.opacity(0.8))
                }
            }
        }
        .padding(12)
        .background(Color.red.opacity(0.1))
        .cornerRadius(8)
    }
}

/**
 * 学生积分表单文本框样式
 */
struct StudentPointsTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color.white)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        StudentPointsFormView(
            isPresented: .constant(true),
            formData: StudentPointsFormData(operationType: .add),
            onSubmit: { operations in
                print("提交操作: \(operations)")
            },
            onCancel: {
                print("取消操作")
            }
        )
    }
} 