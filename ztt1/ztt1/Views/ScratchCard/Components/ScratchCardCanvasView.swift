//
//  ScratchCardCanvasView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 刮刮卡Canvas刮除组件
 * 实现手势追踪、Canvas绘制和面积计算的核心功能
 */
struct ScratchCardCanvasView: View {
    
    // MARK: - Properties
    let cardItem: ScratchCardItem
    let onProgressUpdate: (Double) -> Void
    let onScratchComplete: () -> Void
    
    // MARK: - State
    @State private var scratchedPath = Path()
    @State private var currentProgress: Double = 0.0
    @State private var isScratching = false
    @State private var scratchParticles: [ScratchParticle] = []
    
    // MARK: - Constants
    private let brushRadius: CGFloat = 15
    private let gridSize = 20 // 20x20网格用于面积计算
    private let completeThreshold: Double = 0.4
    
    // 根据设备类型自适应卡片尺寸，保持3:4比例
    private var cardSize: CGSize {
        if DeviceDetection.isPad {
            return CGSize(width: 360, height: 480) // iPad更大尺寸
        } else {
            return CGSize(width: 240, height: 320) // iPhone原有尺寸
        }
    }
    
    var body: some View {
        ZStack {
            // 底层奖品内容
            prizeContentLayer
                .clipShape(RoundedRectangle(cornerRadius: 20))
            
            // 刮除遮挡层
            scratchableLayer
                .clipShape(RoundedRectangle(cornerRadius: 20))
            
            // 刮除粒子效果
            scratchParticlesLayer
        }
        .frame(width: cardSize.width, height: cardSize.height)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
        )
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged(handleDragChanged)
                .onEnded(handleDragEnded)
        )
    }
    
    // MARK: - Prize Content Layer
    
    /**
     * 底层奖品内容
     */
    private var prizeContentLayer: some View {
        ZStack {
            // 奖品背景渐变
            LinearGradient(
                colors: [
                    Color(hex: "#FFE49E").opacity(0.3),
                    Color(hex: "#B5E36B").opacity(0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            VStack(spacing: DeviceDetection.isPad ? 25 : 20) {
                // 庆祝图标
                Image(systemName: "star.fill")
                    .font(.system(size: DeviceDetection.isPad ? 120 : 80, weight: .bold))
                    .foregroundColor(Color.yellow)
                    .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                
                // 奖品名称
                Text(cardItem.prizeName)
                    .font(.system(size: DeviceDetection.isPad ? 42 : 35, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                    .padding(.horizontal, 20)
                
                // 庆祝文字
                Text("scratch_card.congratulations".localized)
                    .font(.system(size: DeviceDetection.isPad ? 42 : 35, weight: .medium))
                    .foregroundColor(Color.green)
            }
        }
    }
    
    // MARK: - Scratchable Layer
    
    /**
     * 可刮除遮挡层
     */
    private var scratchableLayer: some View {
        Canvas { context, size in
            // 绘制完全不透明的遮挡层背景
            let backgroundRect = CGRect(origin: .zero, size: size)
            context.fill(
                Path(backgroundRect),
                with: .linearGradient(
                    Gradient(colors: [
                        Color.gray,
                        Color.gray.opacity(1)
                    ]),
                    startPoint: CGPoint(x: 0, y: 0),
                    endPoint: CGPoint(x: size.width, y: size.height)
                )
            )
            
            // 添加纹理效果
            addScratchableTexture(context: context, size: size)
            
            // 绘制刮除区域（使用blend mode实现挖空效果）
            context.blendMode = .destinationOut
            context.fill(scratchedPath, with: .color(.black))
        }
    }
    
    /**
     * 添加可刮除层纹理
     */
    private func addScratchableTexture(context: GraphicsContext, size: CGSize) {
        // 添加银色刮刮卡质感（不透明）
        let dotSpacing: CGFloat = 8
        let dotSize: CGFloat = 2
        
        for x in stride(from: 0, to: size.width, by: dotSpacing) {
            for y in stride(from: 0, to: size.height, by: dotSpacing) {
                let dot = Path(ellipseIn: CGRect(
                    x: x,
                    y: y,
                    width: dotSize,
                    height: dotSize
                ))
                context.fill(dot, with: .color(.white.opacity(0.5)))
            }
        }
        
        // 添加"刮一刮"提示文字（不透明）
        let scratchHint = "scratch_card.hint_text".localized
        context.draw(
            Text(scratchHint)
                .font(.system(size: DeviceDetection.isPad ? 28 : 20, weight: .bold))
                .foregroundColor(.white),
            at: CGPoint(x: size.width / 2, y: size.height / 2)
        )
    }
    
    // MARK: - Scratch Particles Layer
    
    /**
     * 刮除粒子效果层
     */
    private var scratchParticlesLayer: some View {
        ZStack {
            ForEach(scratchParticles, id: \.id) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.opacity)
                    .scaleEffect(particle.scale)
            }
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - Gesture Handlers
    
    /**
     * 处理拖拽手势变化
     */
    private func handleDragChanged(value: DragGesture.Value) {
        let location = value.location
        
        // 确保刮除位置在卡片范围内
        guard location.x >= 0 && location.x <= cardSize.width &&
              location.y >= 0 && location.y <= cardSize.height else {
            return
        }
        
        if !isScratching {
            isScratching = true
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
        
        // 添加刮除圆形区域
        let scratchCircle = Path(ellipseIn: CGRect(
            x: location.x - brushRadius,
            y: location.y - brushRadius,
            width: brushRadius * 2,
            height: brushRadius * 2
        ))
        
        scratchedPath.addPath(scratchCircle)
        
        // 生成刮除粒子
        generateScratchParticles(at: location)
        
        // 计算刮除进度
        let newProgress = calculateScratchProgress()
        if newProgress != currentProgress {
            currentProgress = newProgress
            onProgressUpdate(newProgress)
            
            // 检查是否达到完成阈值
            if newProgress >= completeThreshold && !cardItem.isPrizeRevealed {
                // 延迟触发完成，让用户看到最后的刮除效果
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    onScratchComplete()
                }
            }
        }
    }
    
    /**
     * 处理拖拽手势结束
     */
    private func handleDragEnded(value: DragGesture.Value) {
        isScratching = false
        
        // 逐渐清除刮除粒子
        withAnimation(.easeOut(duration: 1.0)) {
            scratchParticles.removeAll()
        }
    }
    
    // MARK: - Progress Calculation
    
    /**
     * 计算刮除进度（网格采样法）
     */
    private func calculateScratchProgress() -> Double {
        let cellWidth = cardSize.width / CGFloat(gridSize)
        let cellHeight = cardSize.height / CGFloat(gridSize)
        
        var scratchedCells = 0
        
        for row in 0..<gridSize {
            for col in 0..<gridSize {
                let cellCenter = CGPoint(
                    x: (CGFloat(col) + 0.5) * cellWidth,
                    y: (CGFloat(row) + 0.5) * cellHeight
                )
                
                if scratchedPath.contains(cellCenter) {
                    scratchedCells += 1
                }
            }
        }
        
        return Double(scratchedCells) / Double(gridSize * gridSize)
    }
    
    // MARK: - Helper Methods
    
    /**
     * 获取皮肤渐变颜色
     */
    private func getSkinGradientColors() -> [Color] {
        switch cardItem.cardSkin {
        case .silver:
            return [Color.gray.opacity(0.9), Color.gray.opacity(0.7)]
        case .gold:
            return [Color.yellow.opacity(0.9), Color.orange.opacity(0.7)]
        case .rainbow:
            return [Color.purple.opacity(0.9), Color.pink.opacity(0.7), Color.blue.opacity(0.8)]
        }
    }
    
    // MARK: - Particle Generation
    
    /**
     * 生成刮除粒子
     */
    private func generateScratchParticles(at location: CGPoint) {
        // 限制粒子数量，避免性能问题
        if scratchParticles.count > 20 {
            scratchParticles.removeFirst(5)
        }
        
        // 生成新粒子
        for _ in 0..<3 {
            let particle = ScratchParticle(
                position: CGPoint(
                    x: location.x + CGFloat.random(in: -10...10),
                    y: location.y + CGFloat.random(in: -10...10)
                ),
                velocity: CGPoint(
                    x: CGFloat.random(in: -30...30),
                    y: CGFloat.random(in: -50...10)
                ),
                color: cardItem.cardSkin.coverColor.opacity(0.6),
                size: CGFloat.random(in: 2...4),
                lifespan: 0.8
            )
            scratchParticles.append(particle)
        }
        
        // 更新现有粒子
        updateScratchParticles()
    }
    
    /**
     * 更新刮除粒子状态
     */
    private func updateScratchParticles() {
        let deltaTime: Double = 1.0 / 60.0
        
        for i in scratchParticles.indices {
            scratchParticles[i].update(deltaTime: deltaTime)
        }
        
        // 移除死亡的粒子
        scratchParticles.removeAll { !$0.isAlive }
    }
}

/**
 * 刮除粒子数据模型
 */
struct ScratchParticle: Identifiable {
    let id = UUID()
    var position: CGPoint
    var velocity: CGPoint
    let color: Color
    let size: CGFloat
    let lifespan: Double
    var age: Double = 0.0
    
    // 计算属性
    var isAlive: Bool {
        return age < lifespan
    }
    
    var opacity: Double {
        let progress = age / lifespan
        return max(0, 1 - progress)
    }
    
    var scale: CGFloat {
        let progress = age / lifespan
        return max(0.1, 1 - CGFloat(progress) * 0.5)
    }
    
    /**
     * 更新粒子状态
     */
    mutating func update(deltaTime: Double) {
        age += deltaTime
        position.x += velocity.x * CGFloat(deltaTime)
        position.y += velocity.y * CGFloat(deltaTime)
        
        // 添加重力效果
        velocity.y += CGFloat(deltaTime) * 100
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        ScratchCardCanvasView(
            cardItem: ScratchCardItem.create(
                index: 0,
                prizeName: "iPhone 15 Pro"
            ),
            onProgressUpdate: { progress in
                print("Scratch progress: \(progress)")
            },
            onScratchComplete: {
                print("Scratch completed!")
            }
        )
    }
}