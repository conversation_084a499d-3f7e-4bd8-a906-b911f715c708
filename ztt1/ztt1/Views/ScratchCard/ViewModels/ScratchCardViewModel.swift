//
//  ScratchCardViewModel.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI
import CoreData
import Combine

/**
 * 刮刮卡视图模型
 * 管理刮刮卡配置加载、状态管理、刮除逻辑和动画控制
 */
@MainActor
class ScratchCardViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var cardItems: [ScratchCardItem] = []
    @Published var isLoading = false
    @Published var showResult = false
    @Published var resultPrize = ""
    @Published var showInsufficientPoints = false
    @Published var showNoConfig = false
    @Published var particles: [ParticleItem] = []
    
    // MARK: - Scratch Card Specific States
    @Published var selectedCardIndex: Int? = nil
    @Published var showScratchOverlay = false
    @Published var scratchProgress: Double = 0.0
    @Published var prizeRevealed = false
    
    // MARK: - Private Properties
    private let student: Student
    private let schoolClass: SchoolClass
    private let coreDataManager = CoreDataManager.shared
    private var scratchCardConfig: LotteryToolConfig?
    private var cancellables = Set<AnyCancellable>()
    private var particleTimer: Timer?
    
    // MARK: - Animation States
    @Published var animationStates: [UUID: ScratchCardAnimationState] = [:]
    
    // MARK: - Constants
    private let maxParticles = 30
    private let particleLifespan: Double = 1.2
    private let scratchThreshold: Double = 0.4
    
    // MARK: - Computed Properties
    
    /**
     * 获取每次刮除消耗的积分
     */
    var costPerScratch: Int {
        return Int(scratchCardConfig?.costPerPlay ?? 5)
    }
    
    /**
     * 检查学生是否有足够积分刮除
     */
    var canAffordScratching: Bool {
        return Int(student.point) >= costPerScratch
    }
    
    /**
     * 获取未刮除的卡片数量
     */
    var unscatchedCount: Int {
        return cardItems.filter { !$0.isScratched }.count
    }
    
    /**
     * 检查是否所有卡片都已刮除
     */
    var allCardsScratched: Bool {
        return cardItems.allSatisfy { $0.isScratched }
    }
    
    // MARK: - Initialization
    
    init(student: Student, schoolClass: SchoolClass) {
        self.student = student
        self.schoolClass = schoolClass
    }
    
    // MARK: - Public Methods
    
    /**
     * 加载刮刮卡配置
     */
    func loadScratchCardConfig() {
        isLoading = true
        
        // 获取班级的刮刮卡配置
        if let config = schoolClass.getLotteryConfig(for: .scratch) {
            scratchCardConfig = config
            generateCardItems(from: config)
            showNoConfig = false
        } else {
            scratchCardConfig = nil
            cardItems = []
            showNoConfig = true
        }
        
        isLoading = false
    }
    
    /**
     * 选择指定索引的卡片
     */
    func selectCard(at index: Int) -> Bool {
        guard index < cardItems.count else { return false }
        guard cardItems[index].isClickable else { return false }
        guard canAffordScratching else {
            showInsufficientPoints = true
            return false
        }
        
        // 立即扣除积分（防止用户退回）
        deductStudentPoints(costPerScratch)
        
        // 设置选中状态
        selectedCardIndex = index
        cardItems[index].startScratching()
        
        // 开始选中动画
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showScratchOverlay = true
        }
        
        return true
    }
    
    /**
     * 更新刮除进度
     */
    func updateScratchProgress(index: Int, progress: Double) {
        guard index < cardItems.count else { return }
        
        cardItems[index].updateScratchProgress(progress)
        scratchProgress = progress
        
        // 检查是否达到显示奖品的阈值
        if progress >= scratchThreshold && !prizeRevealed {
            revealPrize(at: index)
        }
    }
    
    /**
     * 显示奖品
     * 确保刮开时看到的奖品与最终获得的奖品完全一致
     */
    func revealPrize(at index: Int) {
        guard index < cardItems.count else { return }
        
        prizeRevealed = true
        cardItems[index].animationState = .revealing
        
        // ✅ 使用被刮开卡片的奖品，确保显示与获得的奖品一致
        let prize = cardItems[index].prizeName
        resultPrize = prize
        
        // 生成庆祝粒子
        generateCelebrationParticles()
        
        // 延迟显示结果弹窗
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                self.showResult = true
            }
        }
    }
    
    /**
     * 确认刮除结果
     */
    func confirmResult() {
        guard let selectedIndex = selectedCardIndex else { return }
        
        // 生成兑换记录
        createRedemptionRecord(prizeName: resultPrize, cost: costPerScratch)
        
        // 积分已在selectCard中扣除，这里不需要再扣除
        
        // 完成刮除
        cardItems[selectedIndex].completeScratching()
        
        // 重置状态
        resetScratchState()
    }
    
    /**
     * 取消刮除（已废弃，不再使用）
     */
    func cancelScratch() {
        // 由于积分已经扣除，不再提供取消功能
        // 这个方法保留但不执行任何操作
    }
    
    /**
     * 重置所有卡片状态
     */
    func resetAllCards() {
        for i in 0..<cardItems.count {
            cardItems[i].reset()
        }
        animationStates.removeAll()
        particles.removeAll()
        stopParticleAnimation()
        resetScratchState()
    }
    
    // MARK: - Private Methods
    
    /**
     * 根据配置生成卡片项目
     * 实现真正的随机分配，确保每次进入页面奖品排列都不同
     */
    private func generateCardItems(from config: LotteryToolConfig) {
        let items = config.sortedItems
        // ✅ 随机打乱奖品列表，保证每种奖品都有对应卡片，但位置随机
        let shuffledItems = items.shuffled()
        
        cardItems = shuffledItems.enumerated().map { index, item in
            return ScratchCardItem.create(
                index: index,
                prizeName: item.formattedPrizeName, // 现在是随机分配的奖品
                skin: .silver // 默认使用银色皮肤
            )
        }
    }
    
    /**
     * 获取随机奖品
     */
    private func getRandomPrize() -> String {
        guard let config = scratchCardConfig else { return "神秘奖品" }
        let items = config.sortedItems
        guard !items.isEmpty else { return "神秘奖品" }
        
        let randomIndex = Int.random(in: 0..<items.count)
        return items[randomIndex].formattedPrizeName
    }
    
    /**
     * 生成庆祝粒子
     */
    private func generateCelebrationParticles() {
        let particleCount = 20
        let screenCenter = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2)
        
        for _ in 0..<particleCount {
            let particle = ParticleItem(
                startPosition: screenCenter,
                currentPosition: screenCenter,
                velocity: CGPoint(
                    x: CGFloat.random(in: -150...150),
                    y: CGFloat.random(in: -150...150)
                ),
                color: [Color.yellow, Color.orange, Color.red, Color.green, Color.blue].randomElement() ?? Color.yellow,
                size: CGFloat.random(in: 4...8),
                lifespan: particleLifespan
            )
            particles.append(particle)
        }
        
        // 启动粒子动画
        startParticleAnimation()
    }
    
    /**
     * 启动粒子动画
     */
    private func startParticleAnimation() {
        particleTimer?.invalidate()
        particleTimer = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { _ in
            Task { @MainActor in
                self.updateParticles()
            }
        }
    }
    
    /**
     * 停止粒子动画
     */
    private func stopParticleAnimation() {
        particleTimer?.invalidate()
        particleTimer = nil
    }
    
    /**
     * 更新粒子状态
     */
    private func updateParticles() {
        let deltaTime = 1.0 / 60.0
        
        for i in particles.indices {
            particles[i].age += deltaTime
            particles[i].currentPosition.x += particles[i].velocity.x * CGFloat(deltaTime)
            particles[i].currentPosition.y += particles[i].velocity.y * CGFloat(deltaTime)
        }
        
        // 移除死亡的粒子
        particles.removeAll { !$0.isAlive }
        
        // 如果没有粒子了，停止动画
        if particles.isEmpty {
            stopParticleAnimation()
        }
    }
    
    /**
     * 创建兑换记录
     */
    private func createRedemptionRecord(prizeName: String, cost: Int) {
        let context = coreDataManager.viewContext
        
        _ = RedemptionRecord.createScratchCardRecord(
            prizeName: prizeName,
            cost: cost,
            for: student,
            in: context
        )
        
        // 保存到CoreData
        coreDataManager.save()
    }
    
    /**
     * 扣除学生积分
     */
    private func deductStudentPoints(_ points: Int) {
        _ = coreDataManager.viewContext
        
        // 扣除积分
        student.point -= Int32(points)
        
        // 保存更改
        coreDataManager.save()
    }
    
    /**
     * 重置刮除状态
     */
    private func resetScratchState() {
        selectedCardIndex = nil
        showScratchOverlay = false
        showResult = false
        resultPrize = ""
        scratchProgress = 0.0
        prizeRevealed = false
    }
}

// MARK: - Extensions

// MARK: - 扩展已在其他地方定义，这里不重复定义 