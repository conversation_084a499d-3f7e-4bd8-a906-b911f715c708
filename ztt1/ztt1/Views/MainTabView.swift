//
//  MainTabView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 导航状态枚举
 * 用于控制应用的页面导航状态
 */
enum NavigationState {
    case home
    case studentDetail
    case subscription
    case settings // 新增设置页导航状态
}

/**
 * 主导航容器 - 管理整个应用的页面切换
 */
struct MainTabView: View {
     
    @StateObject private var homeViewModel = HomeViewModel()
    @EnvironmentObject var authManager: AuthenticationManager
    
    // MARK: - Navigation State Management
    @State private var navigationState: NavigationState = .home
    @State private var selectedStudentId: String? = nil
    
    // 抽奖道具配置状态
    @State private var showLotteryConfigOnSettings: Bool = false
    
    // 接收订阅页面通知的状态
    @State private var subscriptionTabRequested: Bool = false
    
    // MARK: - Initialization
    init() {
        // 使用SwiftUI的方式处理
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景色
                DesignSystem.Colors.background
                    .ignoresSafeArea(.all)
                
                // 主要内容区域 - 标签页内容
                if navigationState == .home {
                    VStack(spacing: 0) {
                        // 主要内容区域
                        Group {
                            switch homeViewModel.selectedTabIndex {
                            case 0:
                                HomeView(
                                    onMemberSelected: handleMemberSelection,
                                    onStudentSelected: handleStudentSelection
                                )
                                .environmentObject(homeViewModel)
                            case 1:
                                GrowthDiaryView()
                            case 2:
                                ProfileView()
                                    .environmentObject(authManager)
                            default:
                                HomeView(
                                    onMemberSelected: handleMemberSelection,
                                    onStudentSelected: handleStudentSelection
                                )
                                .environmentObject(homeViewModel)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: geometry.size.height - DesignSystem.LiquidTabBar.height + geometry.safeAreaInsets.bottom)
                        .padding(.bottom, DesignSystem.LiquidTabBar.height - geometry.safeAreaInsets.bottom)
                        
                        Spacer(minLength: 0)
                    }
                    
                    // 自定义底部导航栏 - 固定在底部
                    VStack {
                        Spacer()
                        
                        CustomTabBar(selectedIndex: $homeViewModel.selectedTabIndex) { index in
                            homeViewModel.selectTab(index)
                        }
                        .frame(height: DesignSystem.LiquidTabBar.height + geometry.safeAreaInsets.bottom)
                        .padding(.bottom, -geometry.safeAreaInsets.bottom) // 负边距抵消安全区域
                    }
                    .ignoresSafeArea(.all, edges: .bottom) // 忽略所有底部安全区域
                }
                
                // 学生详情页 - 全屏覆盖
                if navigationState == .studentDetail {
                    StudentDetailView(
                        studentId: selectedStudentId,
                        onClose: handleCloseStudentDetail,
                        onNavigateToSettings: handleNavigateToSettings, // 用于抽奖道具配置导航
                        onNavigateToSubscription: handleNavigateToSubscription // 用于订阅页面导航
                    )
                    .id(selectedStudentId) // 强制SwiftUI在学生ID变化时重新创建视图
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing)
                            .combined(with: .opacity)
                            .combined(with: .scale(scale: 0.95)),
                        removal: .move(edge: .trailing)
                            .combined(with: .opacity)
                            .combined(with: .scale(scale: 0.9))
                    ))
                    .zIndex(1) // 确保在最上层
                }
                
                // 订阅页面 - 全屏覆盖
                if navigationState == .subscription {
                    SubscriptionView(onDismiss: handleCloseSubscription)
                        .environmentObject(authManager)
                        .transition(.asymmetric(
                            insertion: .move(edge: .trailing)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.95)),
                            removal: .move(edge: .trailing)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.9))
                        ))
                        .zIndex(2) // 确保在学生详情页之上
                }
            }
        }
        // 使用SwiftUI的onReceive接收通知，替代NotificationCenter观察者
        .onReceive(NotificationCenter.default.publisher(for: .showSubscriptionTab)) { _ in
            handleNavigateToSubscription()
        }
    }
    
    // MARK: - Navigation Methods
    
    /**
     * 处理家庭成员选择事件
     * @param memberId 选中的成员ID
     */
    private func handleMemberSelection(memberId: String) {
        selectedStudentId = memberId // 复用现有的导航逻辑
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            navigationState = .studentDetail
        }
    }

    /**
     * 处理学生选择事件（兼容性保留）
     * @param studentId 选中的学生ID
     */
    private func handleStudentSelection(studentId: String) {
        selectedStudentId = studentId
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            navigationState = .studentDetail
        }
    }
    
    /**
     * 处理关闭学生详情页
     */
    private func handleCloseStudentDetail() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.85)) {
            navigationState = .home
        }
        
        // 立即清除选中的学生ID，避免状态不一致
        selectedStudentId = nil
    }
    
    /**
     * 处理导航到订阅页面
     */
    private func handleNavigateToSubscription() {
        // 直接跳转到订阅页面
        withAnimation(.spring(response: 0.5, dampingFraction: 0.85)) {
            navigationState = .subscription
        }
    }
    
    /**
     * 处理关闭订阅页面
     */
    private func handleCloseSubscription() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.85)) {
            navigationState = .home
        }
        
        // 延迟清除选中的学生ID，确保动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            selectedStudentId = nil
        }
    }
    
    /**
     * 处理导航到设置页面并自动打开抽奖道具配置
     */
    private func handleNavigateToSettings() {
        // 设置标志，表示需要打开抽奖道具配置
        showLotteryConfigOnSettings = true
        
        // 切换到主页并选择设置选项卡
        withAnimation(.spring(response: 0.5, dampingFraction: 0.85)) {
            navigationState = .home
            homeViewModel.selectedTabIndex = 1 // 切换到设置标签
        }
        
        // 延迟清除选中的学生ID，确保动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            selectedStudentId = nil
        }
    }
}

/**
 * 设置页面占位视图
 */
private struct SettingsPlaceholderView: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Image("chengzhang")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 80, height: 80)
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Text("placeholder.settings.title".localized)
                .font(.system(
                    size: DesignSystem.Typography.HeadingMedium.fontSize,
                    weight: DesignSystem.Typography.HeadingMedium.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("placeholder.developing".localized)
                .font(.system(
                    size: DesignSystem.Typography.Body.fontSize,
                    weight: DesignSystem.Typography.Body.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.background)
    }
}

/**
 * 个人中心页面占位视图
 */
private struct ProfilePlaceholderView: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Image("wode13")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 80, height: 80)
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Text("placeholder.profile.title".localized)
                .font(.system(
                    size: DesignSystem.Typography.HeadingMedium.fontSize,
                    weight: DesignSystem.Typography.HeadingMedium.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("placeholder.developing".localized)
                .font(.system(
                    size: DesignSystem.Typography.Body.fontSize,
                    weight: DesignSystem.Typography.Body.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.background)
    }
}

// MARK: - Preview
#Preview {
    MainTabView()
        .environmentObject(AuthenticationManager())
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
