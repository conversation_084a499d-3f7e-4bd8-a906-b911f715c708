//
//  BlindBoxGridView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 盲盒网格布局组件
 * 实现响应式瀑布流布局，支持自适应列数和动态盒子大小
 */
struct BlindBoxGridView: View {
    
    // MARK: - Properties
    @ObservedObject var viewModel: BlindBoxViewModel
    let geometry: GeometryProxy
    let onBoxTapped: (Int) -> Void
    
    // MARK: - State
    @State private var gridAppeared = false
    @State private var itemSize: CGFloat = 120
    
    // MARK: - Constants
    private let spacing: CGFloat = 16
    private let horizontalPadding: CGFloat = 20
    private let minItemSize: CGFloat = 100
    private let maxItemSize: CGFloat = 160
    
    // MARK: - Computed Properties
    
    /**
     * 响应式网格列配置
     */
    private var gridColumns: [GridItem] {
        let availableWidth = geometry.size.width - (horizontalPadding * 2)
        let columnCount = calculateOptimalColumnCount(for: availableWidth)
        let itemWidth = calculateItemSize(for: availableWidth, columnCount: columnCount)
        
        // 更新项目大小
        DispatchQueue.main.async {
            if itemSize != itemWidth {
                itemSize = itemWidth
            }
        }
        
        return Array(repeating: GridItem(.fixed(itemWidth), spacing: spacing), count: columnCount)
    }
    
    /**
     * 计算最优列数
     */
    private func calculateOptimalColumnCount(for width: CGFloat) -> Int {
        let deviceType = UIDevice.current.userInterfaceIdiom
        
        switch deviceType {
        case .phone:
            // iPhone布局策略
            if width > 400 {
                return 3 // 大屏iPhone（Plus/Pro Max）
            } else {
                return 2 // 标准iPhone
            }
        case .pad:
            // iPad布局策略
            if width > 800 {
                return 5 // iPad横屏
            } else if width > 600 {
                return 4 // iPad竖屏
            } else {
                return 3 // iPad分屏模式
            }
        default:
            return 3 // 默认3列
        }
    }
    
    /**
     * 计算项目大小
     */
    private func calculateItemSize(for width: CGFloat, columnCount: Int) -> CGFloat {
        let totalSpacing = CGFloat(columnCount - 1) * spacing
        let availableWidth = width - totalSpacing
        let calculatedSize = availableWidth / CGFloat(columnCount)
        
        return max(minItemSize, min(maxItemSize, calculatedSize))
    }
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVGrid(columns: gridColumns, spacing: spacing) {
                ForEach(Array(viewModel.boxItems.enumerated()), id: \.element.id) { index, boxItem in
                    blindBoxItemView(boxItem: boxItem, index: index)
                        .opacity(gridAppeared ? 1.0 : 0.0)
                        .offset(y: gridAppeared ? 0 : 30)
                        .animation(
                            .easeOut(duration: 0.6)
                            .delay(Double(index) * 0.1),
                            value: gridAppeared
                        )
                }
            }
            .padding(.horizontal, horizontalPadding)
            .padding(.top, 20)
            .padding(.bottom, 40)
        }
        .onAppear {
            withAnimation {
                gridAppeared = true
            }
        }
        .onDisappear {
            gridAppeared = false
        }
    }
    
    // MARK: - Blind Box Item View
    
    /**
     * 单个盲盒项目视图
     */
    private func blindBoxItemView(boxItem: BlindBoxItem, index: Int) -> some View {
        VStack(spacing: 8) {
            // 盲盒3D立方体
            BlindBoxCubeView(
                boxItem: boxItem,
                size: itemSize,
                onTap: {
                    onBoxTapped(index)
                }
            )
            .floating(
                offset: CGFloat.random(in: 6...10),
                rotation: Double.random(in: 2...6),
                delay: Double(index) * 0.15,
                duration: Double.random(in: 1.8...2.4),
                enabled: !boxItem.isOpened && boxItem.explosionState == .idle
            )
            
            // 盲盒标题和状态
            blindBoxInfoView(boxItem: boxItem, index: index)
        }
        .contentShape(Rectangle()) // 扩大点击区域
    }
    
    /**
     * 盲盒信息视图
     */
    private func blindBoxInfoView(boxItem: BlindBoxItem, index: Int) -> some View {
        VStack(spacing: 4) {
            // 盲盒编号
            Text(boxItem.displayTitle)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            // 状态指示器
            statusIndicator(for: boxItem)
        }
        .frame(maxWidth: itemSize)
    }
    
    /**
     * 状态指示器
     */
    @ViewBuilder
    private func statusIndicator(for boxItem: BlindBoxItem) -> some View {
        HStack(spacing: 4) {
            // 状态图标
            Image(systemName: statusIcon(for: boxItem))
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(statusColor(for: boxItem))
            
            // 状态文本
            Text(statusText(for: boxItem))
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(statusColor(for: boxItem))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(statusColor(for: boxItem).opacity(0.1))
        )
        .overlay(
            Capsule()
                .stroke(statusColor(for: boxItem).opacity(0.3), lineWidth: 1)
        )
    }
    
    // MARK: - Status Helpers
    
    /**
     * 获取状态图标
     */
    private func statusIcon(for boxItem: BlindBoxItem) -> String {
        switch boxItem.explosionState {
        case .idle:
            return boxItem.isOpened ? "checkmark.circle.fill" : "questionmark.circle"
        case .exploding:
            return "sparkles"
        case .completed:
            return "gift.fill"
        }
    }
    
    /**
     * 获取状态颜色
     */
    private func statusColor(for boxItem: BlindBoxItem) -> Color {
        switch boxItem.explosionState {
        case .idle:
            return boxItem.isOpened ? .green : .blue
        case .exploding:
            return .orange
        case .completed:
            return .purple
        }
    }
    
    /**
     * 获取状态文本
     */
    private func statusText(for boxItem: BlindBoxItem) -> String {
        switch boxItem.explosionState {
        case .idle:
            return boxItem.isOpened ? "blind_box.status.opened".localized : "blind_box.status.waiting".localized
        case .exploding:
            return "blind_box.status.opening".localized
        case .completed:
            return "blind_box.status.completed".localized
        }
    }
}

/**
 * 盲盒网格统计信息组件
 */
struct BlindBoxStatsView: View {
    
    @ObservedObject var viewModel: BlindBoxViewModel
    
    var body: some View {
        HStack(spacing: 20) {
            // 总数统计
            statItem(
                icon: "shippingbox",
                title: "blind_box.stats.total".localized,
                value: "\(viewModel.boxItems.count)",
                color: .blue
            )
            
            // 未开启统计
            statItem(
                icon: "questionmark.circle",
                title: "blind_box.stats.unopened".localized,
                value: "\(viewModel.unopenedCount)",
                color: .orange
            )
            
            // 已开启统计
            statItem(
                icon: "checkmark.circle.fill",
                title: "blind_box.stats.opened".localized,
                value: "\(viewModel.boxItems.count - viewModel.unopenedCount)",
                color: .green
            )
            
            // 消耗积分
            statItem(
                icon: "star.circle.fill",
                title: "blind_box.stats.cost_per_open".localized,
                value: "\(viewModel.costPerOpen)",
                color: .purple
            )
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(hex: "#f8f9fa"))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#e9ecef"), lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
    }
    
    /**
     * 统计项目组件
     */
    private func statItem(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
            
            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text(title)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
    }
}

/**
 * 空状态视图
 */
struct BlindBoxEmptyStateView: View {
    
    let onConfigureTapped: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // 空状态图标
            Image(systemName: "shippingbox")
                .font(.system(size: 64, weight: .light))
                .foregroundColor(Color(hex: "#a9d051").opacity(0.4))
            
            // 空状态文本
            VStack(spacing: 8) {
                Text("blind_box.no_config".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("blind_box.config_description".localized)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
            }
            
            // 配置按钮
            Button(action: onConfigureTapped) {
                HStack(spacing: 8) {
                    Image(systemName: "gear")
                        .font(.system(size: 14, weight: .medium))
                    
                    Text("blind_box.go_config".localized)
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
            }
        }
        .padding(.horizontal, 40)
        .padding(.vertical, 60)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Preview

#Preview {
    GeometryReader { geometry in
        BlindBoxGridView(
            viewModel: {
                let student = Student()
                let schoolClass = SchoolClass()
                let viewModel = BlindBoxViewModel(student: student, schoolClass: schoolClass)
                
                // 模拟数据
                viewModel.boxItems = (0..<12).map { index in
                    var item = BlindBoxItem.create(index: index, prizeName: "奖品\(index + 1)")
                    if index < 3 {
                        item.completeOpening()
                    }
                    return item
                }
                
                return viewModel
            }(),
            geometry: geometry,
            onBoxTapped: { index in
                print("点击盲盒: \(index)")
            }
        )
    }
    .background(Color.gray.opacity(0.1))
} 