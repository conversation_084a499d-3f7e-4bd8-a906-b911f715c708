//
//  ExplosionAnimationView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 爆炸动画组件
 * 实现盲盒开启时的分解、爆炸和视觉效果系统
 */
struct ExplosionAnimationView: View {
    
    // MARK: - Properties
    let isTriggered: Bool
    let explosionCenter: CGPoint
    let onComplete: () -> Void
    
    // MARK: - Animation States
    @State private var faces: [CubeFace] = []
    @State private var explosionProgress: Double = 0
    @State private var showFlash = false
    @State private var showShockwave = false
    @State private var animationCompleted = false
    
    // MARK: - Constants
    private let maxDistance: CGFloat = 200
    private let explosionDuration: Double = 1.0
    private let faceCount = 6
    
    var body: some View {
        ZStack {
            // 立方体面片动画
            ForEach(faces) { face in
                cubeFaceView(face: face)
            }
            
            // 白色闪光效果
            if showFlash {
                flashOverlay
            }
            
            // 冲击波效果
            if showShockwave {
                shockwaveOverlay
            }
        }
        .onChange(of: isTriggered) { triggered in
            if triggered && !animationCompleted {
                startExplosionAnimation()
            }
        }
        .onDisappear {
            resetAnimation()
        }
    }
    
    // MARK: - Cube Face View
    
    private func cubeFaceView(face: CubeFace) -> some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(faceGradient(for: face.type))
            .frame(width: 40, height: 40)
            .rotation3DEffect(
                .degrees(face.rotation),
                axis: (x: 1, y: 1, z: 0)
            )
            .scaleEffect(face.scale)
            .offset(x: face.offset.x, y: face.offset.y)
            .opacity(max(0, 1 - explosionProgress))
    }
    
    // MARK: - Visual Effects
    
    /**
     * 白色闪光效果
     */
    private var flashOverlay: some View {
        Circle()
            .fill(
                RadialGradient(
                    colors: [
                        Color.white.opacity(0.9),
                        Color.white.opacity(0.3),
                        Color.clear
                    ],
                    center: .center,
                    startRadius: 0,
                    endRadius: 50
                )
            )
            .frame(width: 100, height: 100)
            .position(x: explosionCenter.x, y: explosionCenter.y)
            .blur(radius: 2)
            .animation(.easeOut(duration: 0.2), value: showFlash)
    }
    
    /**
     * 冲击波效果
     */
    private var shockwaveOverlay: some View {
        ZStack {
            // 主冲击波
            Circle()
                .stroke(Color.white.opacity(0.7), lineWidth: 3)
                .scaleEffect(explosionProgress * 4)
                .opacity(1 - explosionProgress)
            
            // 次冲击波
            Circle()
                .stroke(Color.yellow.opacity(0.5), lineWidth: 2)
                .scaleEffect(explosionProgress * 6)
                .opacity(max(0, 0.8 - explosionProgress))
        }
        .position(x: explosionCenter.x, y: explosionCenter.y)
        .animation(.easeOut(duration: explosionDuration), value: explosionProgress)
    }
    
    // MARK: - Animation Methods
    
    /**
     * 开始爆炸动画
     */
    private func startExplosionAnimation() {
        guard !animationCompleted else { return }
        
        // 初始化立方体面片
        generateCubeFaces()
        
        // 动画序列
        executeExplosionSequence()
    }
    
    /**
     * 生成立方体面片
     */
    private func generateCubeFaces() {
        faces = CubeFace.FaceType.allCases.map { faceType in
            CubeFace(
                type: faceType,
                offset: .zero,
                rotation: 0,
                scale: 1.0
            )
        }
    }
    
    /**
     * 执行爆炸动画序列
     */
    private func executeExplosionSequence() {
        // 阶段1: 闪光效果 (0.1秒)
        withAnimation(.easeOut(duration: 0.1)) {
            showFlash = true
        }
        
        // 阶段2: 开始面片飞散 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeOut(duration: 0.3)) {
                self.scatterFaces()
                self.explosionProgress = 0.4
            }
            
            // 显示冲击波
            self.showShockwave = true
        }
        
        // 阶段3: 继续扩散 (0.4秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeInOut(duration: 0.4)) {
                self.continueFaceScattering()
                self.explosionProgress = 0.8
            }
        }
        
        // 阶段4: 消失 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation(.easeIn(duration: 0.3)) {
                self.explosionProgress = 1.0
                self.hideFaces()
            }
        }
        
        // 阶段5: 清理和回调
        DispatchQueue.main.asyncAfter(deadline: .now() + explosionDuration) {
            self.completeAnimation()
        }
    }
    
    /**
     * 面片初始散开
     */
    private func scatterFaces() {
        for i in faces.indices {
            let angle = Double(i) * (2 * .pi / Double(faceCount))
            let distance: CGFloat = 60
            
            faces[i].offset = CGPoint(
                x: Foundation.cos(angle) * distance,
                y: Foundation.sin(angle) * distance
            )
            faces[i].rotation = Double.random(in: 0...180)
            faces[i].scale = CGFloat.random(in: 0.8...1.2)
        }
    }
    
    /**
     * 继续面片散开
     */
    private func continueFaceScattering() {
        for i in faces.indices {
            let angle = Double(i) * (2 * .pi / Double(faceCount))
            let distance: CGFloat = 120
            
            faces[i].offset = CGPoint(
                x: Foundation.cos(angle) * distance,
                y: Foundation.sin(angle) * distance
            )
            faces[i].rotation += Double.random(in: 180...360)
            faces[i].scale = CGFloat.random(in: 0.3...0.8)
        }
    }
    
    /**
     * 隐藏面片
     */
    private func hideFaces() {
        for i in faces.indices {
            faces[i].scale = 0
        }
        
        showFlash = false
        showShockwave = false
    }
    
    /**
     * 完成动画
     */
    private func completeAnimation() {
        animationCompleted = true
        onComplete()
    }
    
    /**
     * 重置动画状态
     */
    private func resetAnimation() {
        faces.removeAll()
        explosionProgress = 0
        showFlash = false
        showShockwave = false
        animationCompleted = false
    }
    
    // MARK: - Helper Methods
    
    /**
     * 获取面片渐变色
     */
    private func faceGradient(for type: CubeFace.FaceType) -> LinearGradient {
        let colors: [Color]
        
        switch type {
        case .front:
            colors = [Color.blue, Color.purple]
        case .back:
            colors = [Color.green, Color.teal]
        case .left:
            colors = [Color.orange, Color.red]
        case .right:
            colors = [Color.pink, Color.purple]
        case .top:
            colors = [Color.yellow, Color.orange]
        case .bottom:
            colors = [Color.gray, Color.black]
        }
        
        return LinearGradient(
            colors: colors,
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

/**
 * 简化的爆炸动画组件
 * 用于更简单的场景
 */
struct SimpleExplosionView: View {
    
    let isTriggered: Bool
    let center: CGPoint
    let onComplete: () -> Void
    
    @State private var explosionScale: CGFloat = 0
    @State private var explosionOpacity: Double = 0
    
    var body: some View {
        ZStack {
            // 主爆炸圆环
            Circle()
                .stroke(Color.white, lineWidth: 4)
                .scaleEffect(explosionScale)
                .opacity(explosionOpacity)
                .position(x: center.x, y: center.y)
            
            // 内圈光晕
            Circle()
                .fill(Color.white.opacity(0.6))
                .scaleEffect(explosionScale * 0.5)
                .opacity(explosionOpacity * 0.7)
                .position(x: center.x, y: center.y)
                .blur(radius: 4)
        }
        .onChange(of: isTriggered) { triggered in
            if triggered {
                startSimpleExplosion()
            }
        }
    }
    
    private func startSimpleExplosion() {
        withAnimation(.easeOut(duration: 0.6)) {
            explosionScale = 3.0
            explosionOpacity = 1.0
        }
        
        withAnimation(.easeIn(duration: 0.4).delay(0.2)) {
            explosionOpacity = 0
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            explosionScale = 0
            onComplete()
        }
    }
}

/**
 * 粒子爆炸动画组件
 * 专门用于粒子效果的爆炸
 */
struct ParticleExplosionView: View {
    
    let isTriggered: Bool
    let center: CGPoint
    let particleCount: Int
    let onComplete: () -> Void
    
    @State private var particles: [ParticleData] = []
    @State private var animationActive = false
    
    private struct ParticleData: Identifiable {
        let id = UUID()
        var position: CGPoint
        let velocity: CGPoint
        let color: Color
        let size: CGFloat
        var life: Double = 1.0
    }
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.life)
                    .scaleEffect(particle.life)
            }
        }
        .onChange(of: isTriggered) { triggered in
            if triggered {
                startParticleExplosion()
            }
        }
    }
    
    private func startParticleExplosion() {
        generateParticles()
        animateParticles()
    }
    
    private func generateParticles() {
        particles = (0..<particleCount).map { _ in
            let angle = Double.random(in: 0...(2 * .pi))
            let speed = CGFloat.random(in: 50...150)
            
            return ParticleData(
                position: center,
                velocity: CGPoint(
                    x: Foundation.cos(angle) * speed,
                    y: Foundation.sin(angle) * speed
                ),
                color: [.red, .orange, .yellow, .green, .blue, .purple].randomElement() ?? .white,
                size: CGFloat.random(in: 4...12)
            )
        }
    }
    
    private func animateParticles() {
        let _ = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { timer in
            updateParticles()
            
            if particles.allSatisfy({ $0.life <= 0 }) {
                timer.invalidate()
                onComplete()
            }
        }
        
        animationActive = true
    }
    
    private func updateParticles() {
        for i in particles.indices {
            particles[i].position.x += particles[i].velocity.x * (1/60.0)
            particles[i].position.y += particles[i].velocity.y * (1/60.0)
            particles[i].life -= 1/60.0
        }
        
        particles.removeAll { $0.life <= 0 }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        ExplosionAnimationView(
            isTriggered: true,
            explosionCenter: CGPoint(x: 200, y: 400),
            onComplete: {
                print("爆炸动画完成")
            }
        )
    }
} 