//
//  ParticleSystemView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 粒子系统组件
 * 实现各种粒子效果，包括庆祝、爆炸、火花等
 */
struct ParticleSystemView: View {
    
    // MARK: - Properties
    let particles: [ParticleItem]
    let isActive: Bool
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                particleView(particle: particle)
            }
        }
        .allowsHitTesting(false) // 粒子不接收触摸事件
    }
    
    // MARK: - Particle View
    
    private func particleView(particle: ParticleItem) -> some View {
        Circle()
            .fill(particle.color)
            .frame(width: particle.size, height: particle.size)
            .position(particle.currentPosition)
            .opacity(particle.opacity)
            .scaleEffect(particle.opacity) // 根据生命周期缩放
            .blur(radius: (1 - particle.opacity) * 2) // 死亡时模糊
    }
}

/**
 * 高级粒子系统组件
 * 支持更复杂的粒子效果和自定义行为
 */
struct AdvancedParticleSystemView: View {
    
    // MARK: - Properties
    @ObservedObject var particleSystem: ParticleSystem
    
    var body: some View {
        TimelineView(.animation) { timeline in
            ZStack {
                ForEach(particleSystem.particles) { particle in
                    advancedParticleView(particle: particle, time: timeline.date)
                }
            }
            .allowsHitTesting(false)
        }
        .onAppear {
            particleSystem.start()
        }
        .onDisappear {
            particleSystem.stop()
        }
    }
    
    // MARK: - Advanced Particle View
    
    private func advancedParticleView(particle: AdvancedParticle, time: Date) -> some View {
        Group {
            switch particle.type {
            case .circle:
                Circle()
                    .fill(particle.color)
            case .star:
                StarShape()
                    .fill(particle.color)
            case .heart:
                HeartShape()
                    .fill(particle.color)
            case .sparkle:
                SparkleShape()
                    .fill(particle.color)
            }
        }
        .frame(width: particle.size, height: particle.size)
        .position(particle.position)
        .opacity(particle.opacity)
        .scaleEffect(particle.scale)
        .rotationEffect(.degrees(particle.rotation))
        .blur(radius: particle.blurRadius)
    }
}

// MARK: - Particle System Manager

/**
 * 粒子系统管理器
 * 负责粒子的生成、更新和销毁
 */
class ParticleSystem: ObservableObject {
    
    // MARK: - Published Properties
    @Published var particles: [AdvancedParticle] = []
    
    // MARK: - Private Properties
    private var emitter: ParticleEmitter
    private var timer: Timer?
    private var isRunning = false
    
    // MARK: - Initialization
    
    init(emitter: ParticleEmitter) {
        self.emitter = emitter
    }
    
    // MARK: - Public Methods
    
    /**
     * 开始粒子系统
     */
    func start() {
        guard !isRunning else { return }
        isRunning = true
        
        timer = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { _ in
            self.update()
        }
    }
    
    /**
     * 停止粒子系统
     */
    func stop() {
        isRunning = false
        timer?.invalidate()
        timer = nil
        particles.removeAll()
    }
    
    /**
     * 手动发射粒子
     */
    func emit(at position: CGPoint, count: Int = -1) {
        let particleCount = count > 0 ? count : emitter.emissionRate
        let newParticles = (0..<particleCount).map { _ in
            emitter.createParticle(at: position)
        }
        particles.append(contentsOf: newParticles)
    }
    
    // MARK: - Private Methods
    
    /**
     * 更新粒子系统
     */
    private func update() {
        let deltaTime = 1.0 / 60.0
        
        // 更新现有粒子
        for i in particles.indices {
            particles[i].update(deltaTime: deltaTime)
        }
        
        // 移除死亡的粒子
        particles.removeAll { !$0.isAlive }
        
        // 根据发射器配置生成新粒子
        if emitter.continuousEmission && particles.count < emitter.maxParticles {
            let newParticles = (0..<emitter.emissionRate).map { _ in
                emitter.createParticle(at: emitter.emissionPosition)
            }
            particles.append(contentsOf: newParticles)
        }
    }
}

// MARK: - Particle Emitter

/**
 * 粒子发射器配置
 */
struct ParticleEmitter {
    
    // MARK: - Emission Properties
    let emissionPosition: CGPoint
    let emissionRate: Int
    let maxParticles: Int
    let continuousEmission: Bool
    
    // MARK: - Particle Properties
    let particleType: AdvancedParticle.ParticleType
    let colors: [Color]
    let sizeRange: ClosedRange<CGFloat>
    let lifespanRange: ClosedRange<Double>
    let velocityRange: ClosedRange<CGFloat>
    let angleRange: ClosedRange<Double>
    
    // MARK: - Physics Properties
    let gravity: CGFloat
    let airResistance: CGFloat
    
    // MARK: - Visual Properties
    let fadeIn: Bool
    let fadeOut: Bool
    let scaleOverLife: Bool
    let rotationSpeed: Double
    
    // MARK: - Preset Emitters
    
    /**
     * 庆祝粒子发射器
     */
    static func celebration(at position: CGPoint) -> ParticleEmitter {
        return ParticleEmitter(
            emissionPosition: position,
            emissionRate: 5,
            maxParticles: 50,
            continuousEmission: false,
            particleType: .star,
            colors: [.yellow, .orange, .red, .pink, .purple],
            sizeRange: 8...16,
            lifespanRange: 2.0...4.0,
            velocityRange: 100...200,
            angleRange: 0...(2 * .pi),
            gravity: 300,
            airResistance: 0.98,
            fadeIn: true,
            fadeOut: true,
            scaleOverLife: true,
            rotationSpeed: 180
        )
    }
    
    /**
     * 爆炸粒子发射器
     */
    static func explosion(at position: CGPoint) -> ParticleEmitter {
        return ParticleEmitter(
            emissionPosition: position,
            emissionRate: 20,
            maxParticles: 100,
            continuousEmission: false,
            particleType: .circle,
            colors: [.white, .yellow, .orange, .red],
            sizeRange: 4...12,
            lifespanRange: 0.5...1.5,
            velocityRange: 150...300,
            angleRange: 0...(2 * .pi),
            gravity: 500,
            airResistance: 0.95,
            fadeIn: false,
            fadeOut: true,
            scaleOverLife: false,
            rotationSpeed: 0
        )
    }
    
    /**
     * 火花粒子发射器
     */
    static func sparkle(at position: CGPoint) -> ParticleEmitter {
        return ParticleEmitter(
            emissionPosition: position,
            emissionRate: 10,
            maxParticles: 30,
            continuousEmission: false,
            particleType: .sparkle,
            colors: [.white, .yellow, .cyan],
            sizeRange: 6...10,
            lifespanRange: 1.0...2.0,
            velocityRange: 50...120,
            angleRange: 0...(2 * .pi),
            gravity: 100,
            airResistance: 0.99,
            fadeIn: true,
            fadeOut: true,
            scaleOverLife: true,
            rotationSpeed: 360
        )
    }
    
    // MARK: - Particle Creation
    
    /**
     * 创建新粒子
     */
    func createParticle(at position: CGPoint) -> AdvancedParticle {
        let angle = Double.random(in: angleRange)
        let velocity = CGFloat.random(in: velocityRange)
        let color = colors.randomElement() ?? .white
        let size = CGFloat.random(in: sizeRange)
        let lifespan = Double.random(in: lifespanRange)
        
        return AdvancedParticle(
            type: particleType,
            position: position,
            velocity: CGPoint(
                x: Foundation.cos(angle) * velocity,
                y: Foundation.sin(angle) * velocity
            ),
            color: color,
            size: size,
            lifespan: lifespan,
            gravity: gravity,
            airResistance: airResistance,
            fadeIn: fadeIn,
            fadeOut: fadeOut,
            scaleOverLife: scaleOverLife,
            rotationSpeed: rotationSpeed
        )
    }
}

// MARK: - Advanced Particle

/**
 * 高级粒子数据模型
 */
struct AdvancedParticle: Identifiable {
    
    let id = UUID()
    
    // MARK: - Basic Properties
    let type: ParticleType
    var position: CGPoint
    var velocity: CGPoint
    let color: Color
    let size: CGFloat
    
    // MARK: - Life Properties
    let lifespan: Double
    var age: Double = 0
    
    // MARK: - Physics Properties
    let gravity: CGFloat
    let airResistance: CGFloat
    
    // MARK: - Visual Properties
    let fadeIn: Bool
    let fadeOut: Bool
    let scaleOverLife: Bool
    let rotationSpeed: Double
    var rotation: Double = 0
    
    // MARK: - Computed Properties
    
    var isAlive: Bool {
        return age < lifespan
    }
    
    var lifeProgress: Double {
        return age / lifespan
    }
    
    var opacity: Double {
        var alpha = 1.0
        
        if fadeIn && lifeProgress < 0.2 {
            alpha = lifeProgress / 0.2
        }
        
        if fadeOut && lifeProgress > 0.8 {
            alpha = (1.0 - lifeProgress) / 0.2
        }
        
        return max(0, min(1, alpha))
    }
    
    var scale: CGFloat {
        if scaleOverLife {
            return CGFloat(1.0 - lifeProgress * 0.5)
        }
        return 1.0
    }
    
    var blurRadius: CGFloat {
        return CGFloat(lifeProgress * 2)
    }
    
    // MARK: - Particle Types
    
    enum ParticleType: CaseIterable {
        case circle
        case star
        case heart
        case sparkle
    }
    
    // MARK: - Update Method
    
    /**
     * 更新粒子状态
     */
    mutating func update(deltaTime: Double) {
        // 更新年龄
        age += deltaTime
        
        // 应用重力
        velocity.y += gravity * CGFloat(deltaTime)
        
        // 应用空气阻力
        velocity.x *= airResistance
        velocity.y *= airResistance
        
        // 更新位置
        position.x += velocity.x * CGFloat(deltaTime)
        position.y += velocity.y * CGFloat(deltaTime)
        
        // 更新旋转
        rotation += rotationSpeed * deltaTime
    }
}

// MARK: - Custom Shapes

/**
 * 星形形状
 */
struct StarShape: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        
        for i in 0..<5 {
            let angle = Double(i) * 2 * .pi / 5 - .pi / 2
            let point = CGPoint(
                x: center.x + Foundation.cos(angle) * radius,
                y: center.y + Foundation.sin(angle) * radius
            )
            
            if i == 0 {
                path.move(to: point)
            } else {
                path.addLine(to: point)
            }
            
            // 内部点
            let innerAngle = angle + .pi / 5
            let innerPoint = CGPoint(
                x: center.x + Foundation.cos(innerAngle) * radius * 0.4,
                y: center.y + Foundation.sin(innerAngle) * radius * 0.4
            )
            path.addLine(to: innerPoint)
        }
        
        path.closeSubpath()
        return path
    }
}

/**
 * 心形形状
 */
struct HeartShape: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let width = rect.width
        let height = rect.height
        
        path.move(to: CGPoint(x: width / 2, y: height))
        
        path.addCurve(
            to: CGPoint(x: 0, y: height / 4),
            control1: CGPoint(x: width / 2, y: height * 3 / 4),
            control2: CGPoint(x: 0, y: height / 2)
        )
        
        path.addArc(
            center: CGPoint(x: width / 4, y: height / 4),
            radius: width / 4,
            startAngle: .radians(.pi),
            endAngle: .radians(0),
            clockwise: false
        )
        
        path.addArc(
            center: CGPoint(x: width * 3 / 4, y: height / 4),
            radius: width / 4,
            startAngle: .radians(.pi),
            endAngle: .radians(0),
            clockwise: false
        )
        
        path.addCurve(
            to: CGPoint(x: width / 2, y: height),
            control1: CGPoint(x: width, y: height / 2),
            control2: CGPoint(x: width / 2, y: height * 3 / 4)
        )
        
        return path
    }
}

/**
 * 火花形状
 */
struct SparkleShape: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        
        // 十字形火花
        path.move(to: CGPoint(x: center.x, y: center.y - radius))
        path.addLine(to: CGPoint(x: center.x, y: center.y + radius))
        path.move(to: CGPoint(x: center.x - radius, y: center.y))
        path.addLine(to: CGPoint(x: center.x + radius, y: center.y))
        
        // 对角线
        let diagonal = radius * 0.7
        path.move(to: CGPoint(x: center.x - diagonal, y: center.y - diagonal))
        path.addLine(to: CGPoint(x: center.x + diagonal, y: center.y + diagonal))
        path.move(to: CGPoint(x: center.x + diagonal, y: center.y - diagonal))
        path.addLine(to: CGPoint(x: center.x - diagonal, y: center.y + diagonal))
        
        return path
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        AdvancedParticleSystemView(
            particleSystem: ParticleSystem(
                emitter: .celebration(at: CGPoint(x: 200, y: 400))
            )
        )
    }
} 