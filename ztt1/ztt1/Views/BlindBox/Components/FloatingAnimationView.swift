//
//  FloatingAnimationView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 悬浮动画包装器组件
 * 为子视图添加轻微的上下浮动和旋转动画效果，营造悬浮感
 */
struct FloatingAnimationView<Content: View>: View {
    
    // MARK: - Properties
    let content: Content
    let floatingOffset: CGFloat
    let rotationAngle: Double
    let animationDelay: Double
    let animationDuration: Double
    let isEnabled: Bool
    
    // MARK: - Animation State
    @State private var isFloating = false
    @State private var rotationState: Double = 0
    
    // MARK: - Initialization
    
    init(
        floatingOffset: CGFloat = 8,
        rotationAngle: Double = 5,
        animationDelay: Double = 0,
        animationDuration: Double = 2.0,
        isEnabled: Bool = true,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.floatingOffset = floatingOffset
        self.rotationAngle = rotationAngle
        self.animationDelay = animationDelay
        self.animationDuration = animationDuration
        self.isEnabled = isEnabled
    }
    
    var body: some View {
        content
            .offset(y: isEnabled ? (isFloating ? floatingOffset : -floatingOffset) : 0)
            .rotationEffect(.degrees(isEnabled ? rotationState : 0))
            .onAppear {
                if isEnabled {
                    startFloatingAnimation()
                }
            }
            .onDisappear {
                stopFloatingAnimation()
            }
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始悬浮动画
     */
    private func startFloatingAnimation() {
        // 延迟启动动画，避免所有盲盒同时开始
        DispatchQueue.main.asyncAfter(deadline: .now() + animationDelay) {
            // 垂直浮动动画
            withAnimation(
                .easeInOut(duration: animationDuration)
                .repeatForever(autoreverses: true)
            ) {
                isFloating.toggle()
            }
            
            // 旋转动画（稍微不同的时机）
            withAnimation(
                .easeInOut(duration: animationDuration * 1.2)
                .repeatForever(autoreverses: true)
            ) {
                rotationState = rotationAngle
            }
        }
    }
    
    /**
     * 停止悬浮动画
     */
    private func stopFloatingAnimation() {
        withAnimation(.easeOut(duration: 0.3)) {
            isFloating = false
            rotationState = 0
        }
    }
}

/**
 * 高级悬浮动画组件
 * 支持更复杂的动画模式和自定义效果
 */
struct AdvancedFloatingAnimationView<Content: View>: View {
    
    // MARK: - Properties
    let content: Content
    let config: FloatingConfig
    
    // MARK: - Animation States
    @State private var verticalOffset: CGFloat = 0
    @State private var horizontalOffset: CGFloat = 0
    @State private var rotationAngle: Double = 0
    @State private var scaleEffect: CGFloat = 1.0
    @State private var animationPhase: Double = 0
    
    // MARK: - Initialization
    
    init(config: FloatingConfig, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.config = config
    }
    
    var body: some View {
        content
            .offset(x: horizontalOffset, y: verticalOffset)
            .rotationEffect(.degrees(rotationAngle))
            .scaleEffect(scaleEffect)
            .onAppear {
                if config.isEnabled {
                    startAdvancedAnimation()
                }
            }
            .onDisappear {
                stopAdvancedAnimation()
            }
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始高级动画
     */
    private func startAdvancedAnimation() {
        DispatchQueue.main.asyncAfter(deadline: .now() + config.delay) {
            // 垂直浮动
            if config.verticalFloating {
                withAnimation(
                    .easeInOut(duration: config.verticalDuration)
                    .repeatForever(autoreverses: true)
                ) {
                    verticalOffset = config.verticalRange
                }
            }
            
            // 水平浮动
            if config.horizontalFloating {
                withAnimation(
                    .easeInOut(duration: config.horizontalDuration)
                    .repeatForever(autoreverses: true)
                ) {
                    horizontalOffset = config.horizontalRange
                }
            }
            
            // 旋转动画
            if config.rotation {
                withAnimation(
                    .linear(duration: config.rotationDuration)
                    .repeatForever(autoreverses: false)
                ) {
                    rotationAngle = config.rotationRange
                }
            }
            
            // 缩放动画
            if config.scaling {
                withAnimation(
                    .easeInOut(duration: config.scaleDuration)
                    .repeatForever(autoreverses: true)
                ) {
                    scaleEffect = config.scaleRange
                }
            }
        }
    }
    
    /**
     * 停止高级动画
     */
    private func stopAdvancedAnimation() {
        withAnimation(.easeOut(duration: 0.5)) {
            verticalOffset = 0
            horizontalOffset = 0
            rotationAngle = 0
            scaleEffect = 1.0
        }
    }
}

// MARK: - Configuration Models

/**
 * 悬浮动画配置
 */
struct FloatingConfig {
    let isEnabled: Bool
    let delay: Double
    
    // 垂直浮动
    let verticalFloating: Bool
    let verticalRange: CGFloat
    let verticalDuration: Double
    
    // 水平浮动
    let horizontalFloating: Bool
    let horizontalRange: CGFloat
    let horizontalDuration: Double
    
    // 旋转
    let rotation: Bool
    let rotationRange: Double
    let rotationDuration: Double
    
    // 缩放
    let scaling: Bool
    let scaleRange: CGFloat
    let scaleDuration: Double
    
    // MARK: - Preset Configurations
    
    /**
     * 默认悬浮配置
     */
    static func `default`(delay: Double = 0) -> FloatingConfig {
        return FloatingConfig(
            isEnabled: true,
            delay: delay,
            verticalFloating: true,
            verticalRange: 8,
            verticalDuration: 2.0,
            horizontalFloating: false,
            horizontalRange: 0,
            horizontalDuration: 0,
            rotation: true,
            rotationRange: 5,
            rotationDuration: 4.0,
            scaling: false,
            scaleRange: 1.0,
            scaleDuration: 0
        )
    }
    
    /**
     * 轻微悬浮配置
     */
    static func subtle(delay: Double = 0) -> FloatingConfig {
        return FloatingConfig(
            isEnabled: true,
            delay: delay,
            verticalFloating: true,
            verticalRange: 4,
            verticalDuration: 3.0,
            horizontalFloating: false,
            horizontalRange: 0,
            horizontalDuration: 0,
            rotation: true,
            rotationRange: 2,
            rotationDuration: 6.0,
            scaling: false,
            scaleRange: 1.0,
            scaleDuration: 0
        )
    }
    
    /**
     * 动态悬浮配置
     */
    static func dynamic(delay: Double = 0) -> FloatingConfig {
        return FloatingConfig(
            isEnabled: true,
            delay: delay,
            verticalFloating: true,
            verticalRange: 12,
            verticalDuration: 1.5,
            horizontalFloating: true,
            horizontalRange: 6,
            horizontalDuration: 2.5,
            rotation: true,
            rotationRange: 10,
            rotationDuration: 3.0,
            scaling: true,
            scaleRange: 1.05,
            scaleDuration: 2.0
        )
    }
    
    /**
     * 禁用动画配置
     */
    static var disabled: FloatingConfig {
        return FloatingConfig(
            isEnabled: false,
            delay: 0,
            verticalFloating: false,
            verticalRange: 0,
            verticalDuration: 0,
            horizontalFloating: false,
            horizontalRange: 0,
            horizontalDuration: 0,
            rotation: false,
            rotationRange: 0,
            rotationDuration: 0,
            scaling: false,
            scaleRange: 1.0,
            scaleDuration: 0
        )
    }
}

// MARK: - View Extensions

extension View {
    
    /**
     * 添加简单悬浮动画
     */
    func floating(
        offset: CGFloat = 8,
        rotation: Double = 5,
        delay: Double = 0,
        duration: Double = 2.0,
        enabled: Bool = true
    ) -> some View {
        FloatingAnimationView(
            floatingOffset: offset,
            rotationAngle: rotation,
            animationDelay: delay,
            animationDuration: duration,
            isEnabled: enabled
        ) {
            self
        }
    }
    
    /**
     * 添加高级悬浮动画
     */
    func advancedFloating(config: FloatingConfig) -> some View {
        AdvancedFloatingAnimationView(config: config) {
            self
        }
    }
}

// MARK: - Preview

#Preview {
    ScrollView {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 20) {
            ForEach(0..<9, id: \.self) { index in
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Text("\(index + 1)")
                            .font(.title2)
                            .foregroundColor(.white)
                    )
                    .floating(
                        offset: CGFloat.random(in: 6...12),
                        rotation: Double.random(in: 3...8),
                        delay: Double(index) * 0.2,
                        duration: Double.random(in: 1.5...2.5)
                    )
            }
        }
        .padding()
    }
    .background(Color.gray.opacity(0.1))
} 