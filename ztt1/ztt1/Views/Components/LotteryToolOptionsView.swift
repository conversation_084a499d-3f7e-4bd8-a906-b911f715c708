//
//  LotteryToolOptionsView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 抽奖道具选择视图
 * 用于选择要配置的抽奖道具类型
 */
struct LotteryToolOptionsView: View {

    // MARK: - Environment

    @EnvironmentObject private var coreDataManager: CoreDataManager

    // MARK: - Properties

    /// 选择的班级
    let selectedClass: SchoolClass

    /// 选择道具类型的回调
    let onToolTypeSelected: (LotteryToolConfig.ToolType) -> Void

    /// 关闭弹窗的回调
    let onDismiss: () -> Void
    
    // MARK: - State

    @State private var animationTrigger = false

    // MARK: - Body

    var body: some View {
        ZStack {
            // 半透明背景遮罩 - 与奖品库配置一致
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissModal()
                }

            // 弹窗对话框 - 使用与奖品库配置相同的布局
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 标题栏
                    headerSection

                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)

                    // 道具选择内容
                    toolSelectionContent

                    // 底部空白
                    Spacer(minLength: 20)
                }
                .frame(maxWidth: min(geometry.size.width - 40, 420))
                .frame(maxHeight: geometry.size.height * 0.8)
                .background(Color.white)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationTrigger = true
            }
        }
    }
    
    // MARK: - View Components
    
    /**
     * 标题栏 - 与奖品库配置一致
     */
    private var headerSection: some View {
        HStack {
            Text("lottery_tool_config.select_tool_type.title".localized)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            // 关闭按钮
            Button(action: {
                dismissModal()
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 24)
        .padding(.bottom, 16)
    }

    /**
     * 道具选择内容 - 与奖品库配置一致
     */
    private var toolSelectionContent: some View {
        ScrollView(.vertical, showsIndicators: true) {
            VStack(spacing: 16) {
                // 班级信息卡片
                classInfoCard

                // 道具选择说明
                HStack {
                    Text("lottery_tool_config.select_tool_type.description".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    Spacer()
                }
                .padding(.horizontal, 24)

                // 显示三种抽奖道具选项
                VStack(spacing: 12) {
                    LotteryToolOptionCard(
                        toolType: .wheel,
                        schoolClass: selectedClass,
                        onTap: {
                            onToolTypeSelected(.wheel)
                        }
                    )

                    LotteryToolOptionCard(
                        toolType: .box,
                        schoolClass: selectedClass,
                        onTap: {
                            onToolTypeSelected(.box)
                        }
                    )

                    LotteryToolOptionCard(
                        toolType: .scratch,
                        schoolClass: selectedClass,
                        onTap: {
                            onToolTypeSelected(.scratch)
                        }
                    )
                }
                .padding(.horizontal, 24)
            }
            .padding(.vertical, 20)
        }
        .frame(maxHeight: UIScreen.main.bounds.height * 0.6)
    }

    /**
     * 班级信息卡片
     */
    private var classInfoCard: some View {
        HStack(spacing: 12) {
            // 班级图标
            ZStack {
                Circle()
                    .fill(Color(hex: "#74c07f").opacity(0.1))
                    .frame(width: 40, height: 40)

                Image(systemName: "person.3.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(hex: "#74c07f"))
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(selectedClass.name ?? "未命名班级")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(String(format: "lottery_tool_config.class_info.student_count_format".localized, selectedClass.studentCount))
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(hex: "#edf5d9").opacity(0.3))
        .cornerRadius(12)
        .padding(.horizontal, 24)
    }

    // MARK: - Helper Methods

    /**
     * 关闭弹窗
     */
    private func dismissModal() {
        onDismiss()
    }
}

/**
 * 抽奖道具选项卡片
 */
struct LotteryToolOptionCard: View {
    
    // MARK: - Properties
    
    let toolType: LotteryToolConfig.ToolType
    let schoolClass: SchoolClass
    let onTap: () -> Void
    
    // MARK: - State
    
    @State private var isPressed = false
    
    // MARK: - Computed Properties
    
    /**
     * 检查是否已配置
     */
    private var isConfigured: Bool {
        return schoolClass.hasLotteryConfig(for: toolType)
    }
    
    /**
     * 获取现有配置
     */
    private var existingConfig: LotteryToolConfig? {
        return schoolClass.getLotteryConfig(for: toolType)
    }
    

    
    /**
     * 获取道具描述
     */
    private var toolDescription: String {
        switch toolType {
        case .wheel:
            return "lottery_tool_config.tool_description.wheel".localized
        case .box:
            return "lottery_tool_config.tool_description.box".localized
        case .scratch:
            return "lottery_tool_config.tool_description.scratch".localized
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // 卡片内容
                cardContent
                
                // 卡片底部
                cardFooter
            }
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: "#74c07f").opacity(isConfigured ? 0.4 : 0.2), lineWidth: isConfigured ? 1.5 : 1)
            )
            .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 2)
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
    
    // MARK: - View Components
    

    
    /**
     * 配置状态标识
     */
    private var configStatusBadge: some View {
        Group {
            if isConfigured {
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "#74c07f"))

                    Text("lottery_tool_config.status.configured".localized)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(Color(hex: "#74c07f"))
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color(hex: "#74c07f").opacity(0.1))
                .cornerRadius(12)
            } else {
                HStack(spacing: 4) {
                    Image(systemName: "plus.circle")
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "#74c07f"))
                    
                    Text("lottery_tool_config.status.not_configured".localized)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(Color(hex: "#74c07f"))
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color(hex: "#74c07f").opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
    
    /**
     * 卡片内容
     */
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: 10) {
            // 道具名称和配置状态
            HStack {
                Text(toolType.displayName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 配置状态标识
                configStatusBadge
            }
            
            // 道具描述
            Text(toolDescription)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // 配置信息
            if isConfigured, let config = existingConfig {
                configurationInfo(config)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 14)
    }
    
    /**
     * 配置信息显示
     */
    private func configurationInfo(_ config: LotteryToolConfig) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Divider()
            
            HStack(spacing: 20) {
                HStack(spacing: 6) {
                    Image(systemName: "number.circle.fill")
                        .font(.caption)
                        .foregroundColor(Color(hex: "#74c07f"))
                    
                    Text(String(format: "lottery_tool_config.config_info.item_count_format".localized, config.itemCount))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                HStack(spacing: 6) {
                    Image(systemName: "star.circle.fill")
                        .font(.caption)
                        .foregroundColor(.orange)
                    
                    Text(String(format: "lottery_tool_config.config_info.cost_per_play_format".localized, config.costPerPlay))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
    }
    
    /**
     * 卡片底部
     */
    private var cardFooter: some View {
        HStack {
            Text(isConfigured ? "lottery_tool_config.action.modify".localized : "lottery_tool_config.action.configure".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(Color(hex: "#74c07f"))
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color(hex: "#74c07f"))
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color(.systemGray6).opacity(0.5))
    }
}

// MARK: - Preview

#if DEBUG
struct LotteryToolOptionsView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建预览数据
        let previewContext = PersistenceController.preview.container.viewContext
        
        let user = User(context: previewContext)
        user.id = UUID()
        user.nickname = "预览用户"
        
        let schoolClass = SchoolClass(context: previewContext)
        schoolClass.id = UUID()
        schoolClass.name = "三年级一班"
        schoolClass.owner = user
        schoolClass.createdAt = Date()
        
        // 添加一些学生
        for i in 1...10 {
            let student = Student(context: previewContext)
            student.id = UUID()
            student.name = "学生\(i)"
            student.studentNumber = "\(i)"
            student.gender = i % 2 == 0 ? "男" : "女"
            student.point = Int32.random(in: 50...100)
            student.schoolClass = schoolClass
        }
        
        // 添加一个已配置的道具
        let config = LotteryToolConfig(context: previewContext)
        config.id = UUID()
        config.toolType = "大转盘"
        config.itemCount = 12
        config.costPerPlay = 5
        config.createdAt = Date()
        config.updatedAt = Date()
        config.schoolClass = schoolClass
        
        return LotteryToolOptionsView(
            selectedClass: schoolClass,
            onToolTypeSelected: { _ in },
            onDismiss: { }
        )
        .environmentObject(CoreDataManager(persistenceController: PersistenceController.preview))
    }
}
#endif 