//
//  FamilyOperationFormView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 全员操作表单弹窗组件
 * 用于输入全家加分或扣分的名称和分值
 */
struct FamilyOperationFormView: View {
    
    @Binding var isPresented: Bool
    let operationType: FamilyOperationType
    let onSubmit: (String, Int) -> Void
    let onCancel: () -> Void
    
    @State private var operationName: String = ""
    @State private var operationValue: String = ""
    @State private var animationTrigger = false
    @State private var showValidationErrors = false
    @State private var isSubmitting = false
    
    // MARK: - Computed Properties
    
    /**
     * 表单标题
     */
    private var formTitle: String {
        switch operationType {
        case .add:
            return "family_operation.form.add_title".localized
        case .deduct:
            return "family_operation.form.deduct_title".localized
        }
    }
    
    /**
     * 表单验证结果
     */
    private var validationResult: (isValid: Bool, errors: [String]) {
        var errors: [String] = []
        
        // 验证操作名称
        let trimmedName = operationName.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedName.isEmpty {
            errors.append("family_operation.validation.name_empty".localized)
        } else if trimmedName.count > 20 {
            errors.append("family_operation.validation.name_too_long".localized)
        }
        
        // 验证分值
        if operationValue.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("family_operation.validation.value_empty".localized)
        } else if let value = Int(operationValue) {
            if value <= 0 {
                errors.append("family_operation.validation.value_positive".localized)
            } else if value > 100 {
                errors.append("family_operation.validation.value_too_high".localized)
            }
        } else {
            errors.append("family_operation.validation.value_invalid".localized)
        }
        
        return (errors.isEmpty, errors)
    }
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        if !isSubmitting {
                            closeForm()
                        }
                    }
                    .transition(.opacity)

                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text(formTitle)
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                        
                        Button(action: {
                            if !isSubmitting {
                                closeForm()
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        .disabled(isSubmitting)
                    }
                    .padding(.horizontal, 24)
                    .padding(.top, 24)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)
                    
                    // 表单内容
                    VStack(spacing: 20) {
                        // 操作名称输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text("family_operation.form.name_label".localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("family_operation.form.name_placeholder".localized, text: $operationName)
                                .textFieldStyle(FamilyOperationTextFieldStyle())
                                .disabled(isSubmitting)
                        }
                        
                        // 分值输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text("family_operation.form.value_label".localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("family_operation.form.value_placeholder".localized, text: $operationValue)
                                .keyboardType(.numberPad)
                                .textFieldStyle(FamilyOperationTextFieldStyle())
                                .disabled(isSubmitting)
                        }
                        
                        // 验证错误显示
                        if showValidationErrors && !validationResult.isValid {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(validationResult.errors, id: \.self) { error in
                                    HStack {
                                        Image(systemName: "exclamationmark.triangle.fill")
                                            .foregroundColor(.red)
                                            .font(.system(size: 12))
                                        Text(error)
                                            .font(.system(size: 12))
                                            .foregroundColor(.red)
                                        Spacer()
                                    }
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(Color.red.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 20)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)
                    
                    // 底部按钮区域
                    HStack(spacing: 16) {
                        // 取消按钮
                        Button(action: {
                            if !isSubmitting {
                                closeForm()
                            }
                        }) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color(hex: "#666666"))
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color(hex: "#f5f5f5"))
                                )
                        }
                        .disabled(isSubmitting)
                        
                        // 提交按钮
                        Button(action: submitForm) {
                            HStack {
                                if isSubmitting {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                }
                                Text(isSubmitting ? "family_operation.form.submitting".localized : "family_operation.form.submit".localized)
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                operationType == .add ? Color(hex: "#a9d051") : Color(hex: "#ff6b6b"),
                                                operationType == .add ? Color(hex: "#8bc34a") : Color(hex: "#ff5252")
                                            ]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                            )
                        }
                        .disabled(isSubmitting)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 20)
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.white)
                            .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    )
                    .frame(maxWidth: min(geometry.size.width - 40, 400))
                    .scaleEffect(animationTrigger ? 1.0 : 0.8)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: animationTrigger)
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                // 重置表单状态
                resetForm()
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 提交表单
     */
    private func submitForm() {
        // 显示验证错误
        showValidationErrors = true
        
        // 验证表单
        let validation = validationResult
        guard validation.isValid else { return }
        
        // 开始提交
        isSubmitting = true
        
        // 延迟执行，模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let trimmedName = operationName.trimmingCharacters(in: .whitespacesAndNewlines)
            let value = Int(operationValue) ?? 0
            
            onSubmit(trimmedName, value)
            isSubmitting = false
        }
    }
    
    /**
     * 关闭表单
     */
    private func closeForm() {
        isPresented = false
        onCancel()
    }
    
    /**
     * 重置表单状态
     */
    private func resetForm() {
        operationName = ""
        operationValue = ""
        showValidationErrors = false
        isSubmitting = false
    }
}

// MARK: - FamilyOperationType
/**
 * 全员操作类型枚举
 */
enum FamilyOperationType {
    case add    // 加分
    case deduct // 扣分
}

/**
 * 全员操作表单文本框样式
 */
struct FamilyOperationTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color.white)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()
        
        FamilyOperationFormView(
            isPresented: .constant(true),
            operationType: .add,
            onSubmit: { name, value in
                print("提交: \(name), \(value)")
            },
            onCancel: {
                print("取消")
            }
        )
    }
}
