//
//  FamilyMemberCardView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/27.
//

import SwiftUI

/**
 * 家庭成员卡片组件
 * 基于StudentCardView设计，保持完全一致的UI和交互
 */
struct FamilyMemberCardView: View {

    let member: FamilyMember
    let memberIndex: Int  // 新增：成员在列表中的索引（从1开始）
    let isDeleteMode: Bool
    let onLongPress: () -> Void
    let onDeleteTapped: () -> Void
    let onCardTapped: () -> Void
    
    @State private var isPressed = false
    @State private var animationTrigger = false
    @State private var isLongPressJustEnded = false
    @State private var cardAppeared = false
    
    var body: some View {
        Button(action: {
            // 如果刚结束长按，忽略此次点击
            if isLongPressJustEnded {
                return
            }
            
            if isDeleteMode {
                onCardTapped()
            } else {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isPressed = true
                    animationTrigger.toggle()
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isPressed = false
                    onCardTapped()
                }
            }
        }) {
            GeometryReader { geometry in
                let breakpoint = ResponsiveBreakpoints.currentBreakpoint(for: geometry)
                let config = ResponsiveBreakpoints.configForBreakpoint(breakpoint)
                let calculator = LayoutCalculator(geometry: geometry, config: config)
                
                ZStack {
                    // 卡片背景
                    RoundedRectangle(cornerRadius: 16)
                        .fill(DesignSystem.Colors.cardBackground)
                        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
                    
                    // 删除模式覆盖层
                    if isDeleteMode {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.red.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.red, lineWidth: 2)
                            )
                    }
                    
                    // 装饰性背景元素
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.05))
                        .frame(width: 50, height: 50)
                        .offset(x: 35, y: -30)
                    
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.08))
                        .frame(width: 30, height: 30)
                        .offset(x: -25, y: 20)
                    
                    // 背景水印层（美化版）- 使用序号作为水印
                    EnhancedWatermarkLayer(
                        text: String(format: "%02d", memberIndex),
                        position: calculator.watermarkPosition,
                        fontSize: calculator.watermarkStyle.fontSize ?? 80,
                        fontWeight: .black,
                        opacity: 0.15,
                        color: Color(hex: "#B5E36B")
                    )
                    
                    // 头像层（美化版）
                    EnhancedAvatarLayer(
                        imageName: getRoleBasedAvatarName(for: member),
                        position: calculator.avatarPosition,
                        size: calculator.avatarSize.width,
                        isPressed: isPressed
                    )
                    
                    // 姓名层（美化版）
                    EnhancedNameLayer(
                        text: member.name ?? "未知",
                        position: calculator.namePosition,
                        fontSize: calculator.nameStyle.fontSize ?? 16,
                        fontWeight: .semibold,
                        color: Color(hex: "#333333"),
                        maxLines: calculator.nameConstraints.maxLines ?? 1,
                        minimumScaleFactor: 0.8
                    )
                    
                    // 积分层（美化版）
                    EnhancedScoreLayer(
                        score: Int(member.currentPoints),
                        position: calculator.scorePosition,
                        fontSize: calculator.scoreStyle.fontSize ?? 18,
                        fontWeight: .bold,
                        color: Color(hex: "#87C441"),
                        animationTrigger: animationTrigger
                    )
                    
                    // 删除按钮
                    if isDeleteMode {
                        VStack {
                            HStack {
                                Spacer()
                                Button(action: onDeleteTapped) {
                                    Image(systemName: "minus.circle.fill")
                                        .font(.system(size: 24))
                                        .foregroundColor(.red)
                                        .background(Color.white)
                                        .clipShape(Circle())
                                }
                                .padding(.top, 8)
                                .padding(.trailing, 8)
                            }
                            Spacer()
                        }
                    }
                    
                    // 按压闪烁效果
                    if isPressed {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.white.opacity(0.5))
                            .transition(.opacity)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(height: DesignSystem.StudentCard.height)
        .padding(DesignSystem.StudentCard.padding)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .simultaneousGesture(
            LongPressGesture(minimumDuration: DesignSystem.DeleteMode.longPressMinimumDuration)
                .onEnded { _ in
                    if !isDeleteMode {
                        isLongPressJustEnded = true
                        onLongPress()
                        
                        // 设置冷却期，防止立即触发点击事件
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                            isLongPressJustEnded = false
                        }
                    }
                }
        )
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                cardAppeared = true
            }
        }
        .scaleEffect(cardAppeared ? 1.0 : 0.9)
        .opacity(cardAppeared ? 1.0 : 0.0)
    }

    /**
     * 根据角色获取对应的头像名称
     */
    private func getRoleBasedAvatarName(for member: FamilyMember) -> String {
        switch member.roleEnum {
        case .father:
            return "爸爸头像"
        case .mother:
            return "妈妈头像"
        case .son:
            return "男生头像"
        case .daughter:
            return "女生头像"
        case .other:
            return "其他头像"
        }
    }
}

// MARK: - Enhanced Layer Components

/**
 * 增强版头像层
 */
private struct EnhancedAvatarLayer: View {
    let imageName: String
    let position: CGPoint
    let size: CGFloat
    let isPressed: Bool
    
    var body: some View {
        ZStack {
            // 头像背景光圈
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#B5E36B").opacity(0.15),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size/2 + 5
                    )
                )
                .frame(width: size + 10, height: size + 10)
                .scaleEffect(isPressed ? 1.1 : 1.0)
            
            // 头像图片
            Image(imageName)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: size, height: size)
                .clipShape(Circle())
                .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
        }
        .position(x: position.x, y: position.y)
        .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isPressed)
    }
}

/**
 * 增强版姓名层
 */
private struct EnhancedNameLayer: View {
    let text: String
    let position: CGPoint
    let fontSize: CGFloat
    let fontWeight: Font.Weight
    let color: Color
    let maxLines: Int
    let minimumScaleFactor: CGFloat
    
    var body: some View {
        Text(text)
            .font(.system(size: fontSize, weight: fontWeight))
            .foregroundColor(color)
            .lineLimit(maxLines)
            .minimumScaleFactor(minimumScaleFactor)
            .position(x: position.x, y: position.y)
    }
}

/**
 * 增强版积分层
 */
private struct EnhancedScoreLayer: View {
    let score: Int
    let position: CGPoint
    let fontSize: CGFloat
    let fontWeight: Font.Weight
    let color: Color
    let animationTrigger: Bool

    /**
     * 根据分数大小计算调整后的位置
     */
    private var adjustedPosition: CGPoint {
        let scoreText = "\(score)"
        let digitCount = scoreText.count

        // 根据数字位数调整X坐标偏移量
        let offsetX: CGFloat
        switch digitCount {
        case 1:
            offsetX = 0 // 单位数不偏移
        case 2:
            offsetX = -8 // 两位数向左偏移8点
        case 3:
            offsetX = -16 // 三位数向左偏移16点
        case 4:
            offsetX = -24 // 四位数向左偏移24点
        default:
            offsetX = -32 // 五位数及以上向左偏移32点
        }

        return CGPoint(x: position.x + offsetX, y: position.y)
    }

    var body: some View {
        Text("\(score)")
            .font(.system(size: fontSize, weight: fontWeight))
            .foregroundColor(color)
            .position(x: adjustedPosition.x, y: adjustedPosition.y)
            .scaleEffect(animationTrigger ? 1.1 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: animationTrigger)
    }
}

/**
 * 增强版水印层
 */
private struct EnhancedWatermarkLayer: View {
    let text: String
    let position: CGPoint
    let fontSize: CGFloat
    let fontWeight: Font.Weight
    let opacity: Double
    let color: Color
    
    var body: some View {
        Text(text)
            .font(.system(size: fontSize, weight: fontWeight))
            .foregroundColor(color.opacity(opacity))
            .position(x: position.x, y: position.y)
    }
}

// MARK: - Preview
#Preview {
    let context = PersistenceController.preview.container.viewContext
    let member = FamilyMember(context: context)
    member.id = UUID()
    member.name = "张小明"
    member.role = "son"
    member.gender = "male"
    member.currentPoints = 85
    member.createdAt = Date()

    return VStack {
        HStack {
            FamilyMemberCardView(
                member: member,
                memberIndex: 1,
                isDeleteMode: false,
                onLongPress: {
                    print("长按家庭成员卡片")
                },
                onDeleteTapped: {
                    print("删除家庭成员")
                },
                onCardTapped: {
                    print("点击家庭成员卡片")
                }
            )
            .frame(width: 160)

            Spacer()
        }
        Spacer()
    }
    .padding()
    .background(DesignSystem.Colors.background)
}
