//
//  ResetPointsFormView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 重置学生积分表单组件
 * 允许用户输入新的积分值并重置班级所有学生的积分
 */
struct ResetPointsFormView: View {
    
    @Binding var isPresented: Bool
    let className: String
    let studentCount: Int
    let onConfirm: (Int) -> Void
    
    @State private var newPoints: String = "0"
    @State private var validationError: String?
    @State private var showConfirmation = false
    
    private var isValidInput: Bool {
        guard let points = Int(newPoints.trimmingCharacters(in: .whitespacesAndNewlines)) else {
            return false
        }
        return points >= 0 && points <= 1000
    }
    
    private var pointsValue: Int {
        return Int(newPoints.trimmingCharacters(in: .whitespacesAndNewlines)) ?? 0
    }
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 表单卡片
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("class_reset.points.title".localized)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                        
                        // 关闭按钮
                        Button(action: {
                            dismissKeyboard()
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    .onTapGesture {
                        // 点击标题栏区域关闭键盘
                        dismissKeyboard()
                    }
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 表单内容
                    VStack(spacing: 20) {
                        // 描述信息
                        VStack(alignment: .leading, spacing: 8) {
                            Text("class_reset.points.description".localized(with: className))
                                .font(.system(size: 14, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("class_reset.points.student_count".localized(with: studentCount))
                                .font(.system(size: 13, weight: .medium))
                                .foregroundColor(Color(hex: "#a9d051"))
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 积分输入区域
                        VStack(alignment: .leading, spacing: 8) {
                            Text("class_reset.points.input_label".localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("class_reset.points.input_placeholder".localized, text: $newPoints)
                                .keyboardType(.numberPad)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .onChange(of: newPoints) { _ in
                                    validateInput()
                                }
                            
                            if let error = validationError {
                                Text(error)
                                    .font(.system(size: 12, weight: .regular))
                                    .foregroundColor(.red)
                            }
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 预览信息
                        if isValidInput {
                            VStack(spacing: 8) {
                                HStack {
                                    Text("class_reset.points.preview_title".localized)
                                        .font(.system(size: 13, weight: .medium))
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                    Spacer()
                                }
                                
                                HStack {
                                    Text("class_reset.points.preview_description".localized)
                                        .font(.system(size: 13, weight: .regular))
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                    
                                    Text("\(pointsValue) " + "common.points_unit".localized)
                                        .font(.system(size: 14, weight: .bold))
                                        .foregroundColor(Color(hex: "#a9d051"))
                                    
                                    Spacer()
                                }
                            }
                            .padding(12)
                            .background(Color(hex: "#f8ffe5"))
                            .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 20)
                    .contentShape(Rectangle()) // 确保整个表单内容区域能响应手势
                    .onTapGesture {
                        // 点击表单内容区域关闭键盘
                        dismissKeyboard()
                    }
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 按钮区域
                    HStack(spacing: 12) {
                        // 取消按钮
                        Button(action: {
                            dismissKeyboard()
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color(hex: "#f5f5f5"))
                                .cornerRadius(8)
                        }
                        
                        // 确认按钮
                        Button(action: {
                            dismissKeyboard()
                            if isValidInput {
                                showConfirmation = true
                            }
                        }) {
                            Text("class_reset.points.confirm_button".localized)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(isValidInput ? Color(hex: "#a9d051") : Color.gray)
                                .cornerRadius(8)
                        }
                        .disabled(!isValidInput)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .frame(width: 320)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .alert("class_reset.points.alert_title".localized, isPresented: $showConfirmation) {
            Button("common.button.cancel".localized, role: .cancel) { }
            Button("common.button.confirm".localized, role: .destructive) {
                onConfirm(pointsValue)
                isPresented = false
            }
        } message: {
            Text("class_reset.points.alert_message".localized(with: className, studentCount, pointsValue))
        }
    }
    
    private func validateInput() {
        let trimmedInput = newPoints.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedInput.isEmpty {
            validationError = "class_reset.validation.points_empty".localized
            return
        }
        
        guard let points = Int(trimmedInput) else {
            validationError = "class_reset.validation.points_invalid".localized
            return
        }
        
        if points < 0 {
            validationError = "class_reset.validation.points_negative".localized
            return
        }
        
        if points > 1000 {
            validationError = "class_reset.validation.points_too_high".localized
            return
        }
        
        validationError = nil
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        ResetPointsFormView(
            isPresented: .constant(true),
            className: "一年级1班",
            studentCount: 25,
            onConfirm: { points in
                print("重置积分为: \(points)")
            }
        )
    }
} 