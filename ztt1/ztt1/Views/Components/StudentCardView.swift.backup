//
//  StudentCardView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 学生卡片组件
 */
struct StudentCardView: View {
    
    let student: Student
    let onTapped: () -> Void
    
    var body: some View {
        Button(action: onTapped) {
            ZStack {
                // 背景学号水印 - 居中显示
                Text(student.studentNumber)
                    .font(.custom("Impact", size: 80))
                    .foregroundColor(Color(hex: "#f8f8f8"))
                    .opacity(1)
                
                // 主要内容
                HStack(spacing: DesignSystem.StudentCard.spacingBetweenElements) {
                    // 头像
                    Image(student.avatarImageName)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(
                            width: DesignSystem.StudentCard.avatarSize,
                            height: DesignSystem.StudentCard.avatarSize
                        )
                        .clipShape(Circle())
                    
                    // 姓名
                    VStack(alignment: .leading, spacing: 2) {
                        Text(student.name)
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .lineLimit(1)
                    }
                    
                    Spacer()
                    
                    // 积分
                    VStack {
                        Spacer()
                        Text("\(student.score)")
                            .font(.system(
                                size: DesignSystem.Typography.Score.fontSize,
                                weight: DesignSystem.Typography.Score.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textScore)
                    }
                }
                .padding(DesignSystem.StudentCard.padding)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .studentCardStyle()
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Preview
#Preview {
    let sampleStudent = Student(
        name: "张小明",
        studentNumber: "01",
        gender: .male,
        score: 85,
        classId: "sample-class"
    )
    
    return VStack {
        HStack {
            StudentCardView(student: sampleStudent) {
                print("点击了学生卡片")
            }
            .frame(width: 160)
            
            Spacer()
        }
        Spacer()
    }
    .padding()
    .background(DesignSystem.Colors.background)
} 