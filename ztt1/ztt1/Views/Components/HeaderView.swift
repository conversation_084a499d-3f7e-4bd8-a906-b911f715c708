//
//  HeaderView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 首页顶部组件 - 包含logo和搜索框
 */
struct HeaderView: View {
    
    @Binding var searchText: String
    @Binding var isSearching: Bool
    @State private var logoRotation: Double = 0
    @State private var searchFocused: Bool = false
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // Logo with enhanced visual effects
            ZStack {
                // 背景渐变圆形
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#FFE49E").opacity(0.3),
                                Color(hex: "#B5E36B").opacity(0.2)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 110, height: 110)
                    .shadow(color: Color(hex: "#B5E36B").opacity(0.3), radius: 15, x: 0, y: 5)
                
                // Logo图片
                Image("logo")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 90, height: 90)
                    .rotationEffect(.degrees(logoRotation))
                    .onTapGesture {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            logoRotation += 360
                        }
                    }
            }
            .offset(y: -20)

            Spacer()
            
            // Enhanced search bar
            HStack(spacing: DesignSystem.Spacing.sm) {
                Image("sousuo")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(searchFocused ? DesignSystem.Colors.primary : DesignSystem.SearchBar.placeholderColor)
                    .animation(.easeInOut(duration: 0.2), value: searchFocused)
                
                TextField("student_grid.search_placeholder".localized, text: $searchText)
                    .font(.system(size: DesignSystem.Typography.Body.fontSize, weight: DesignSystem.Typography.Body.fontWeight))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .onTapGesture {
                        isSearching = true
                        searchFocused = true
                    }
                    .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)) { _ in
                        searchFocused = false
                    }
                
                // 清除按钮
                if !searchText.isEmpty {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            searchText = ""
                            isSearching = false
                            searchFocused = false
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16))
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.white)
                    .shadow(color: Color(hex: "#B5E36B").opacity(searchFocused ? 0.3 : 0.1), radius: searchFocused ? 8 : 4, x: 0, y: 2)
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(
                                searchFocused ? 
                                LinearGradient(
                                    gradient: Gradient(colors: [DesignSystem.Colors.primary, DesignSystem.Colors.secondary]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                ) : 
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.clear, Color.clear]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                ),
                                lineWidth: searchFocused ? 2 : 0
                            )
                    )
            )
            .scaleEffect(searchFocused ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: searchFocused)
            .frame(height: 44)
        }
        .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
        .padding(.top, 5)
        .onAppear {
            // Logo入场动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2)) {
                logoRotation = 0
            }
        }
        // 监听搜索状态变化
        .onReceive([isSearching].publisher.first()) { value in
            searchFocused = value
        }
    }
}

// MARK: - Preview Container
private struct HeaderViewPreviewContainer: View {
    @State private var searchText = ""
    @State private var isSearching = false
    
    var body: some View {
        VStack {
            HeaderView(searchText: $searchText, isSearching: $isSearching)
            Spacer()
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [DesignSystem.Colors.background, Color.white]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
}

// MARK: - Preview
#Preview {
    HeaderViewPreviewContainer()
} 