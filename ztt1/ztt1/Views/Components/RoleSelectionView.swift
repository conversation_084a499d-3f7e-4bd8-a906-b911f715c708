//
//  RoleSelectionView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/26.
//

import SwiftUI

/**
 * 角色选择组件
 * 提供角色选择的弹出菜单界面
 */
struct RoleSelectionView: View {
    
    @Binding var selectedRole: String
    @Binding var isPresented: Bool
    
    private let roles = StudentFormData.availableRoles()
    
    var body: some View {
        ZStack {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isPresented = false
                        }
                    }
                
                // 选择菜单
                VStack(spacing: 0) {
                    // 标题
                    HStack {
                        Text("add_student.form.role".localized)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                        
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 角色选项列表
                    ScrollView {
                        VStack(spacing: 0) {
                            ForEach(roles, id: \.self) { role in
                                RoleOptionRow(
                                    role: role,
                                    isSelected: selectedRole == role,
                                    onTap: {
                                        selectedRole = role
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            isPresented = false
                                        }
                                    }
                                )
                            }
                        }
                    }
                    .frame(maxHeight: 300)
                }
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
                .padding(.horizontal, 40)
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isPresented)
    }
}

/**
 * 角色选项行组件
 */
struct RoleOptionRow: View {
    
    let role: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                Text(StudentFormData.roleDisplayText(for: role))
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18))
                        .foregroundColor(Color(hex: "#a9d051"))
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(isSelected ? Color(hex: "#f8fff2") : Color.clear)
        }
        .buttonStyle(PlainButtonStyle())
        
        // 分隔线
        if role != StudentFormData.availableRoles().last {
            Rectangle()
                .fill(Color(hex: "#f0f0f0"))
                .frame(height: 0.5)
                .padding(.horizontal, 20)
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        RoleSelectionView(
            selectedRole: .constant("son"),
            isPresented: .constant(true)
        )
    }
}
