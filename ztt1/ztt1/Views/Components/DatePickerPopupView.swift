//
//  DatePickerPopupView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/27.
//

import SwiftUI

/**
 * 日期选择器弹窗组件
 * 使用与全员操作弹窗相同的样式和动画效果
 */
struct DatePickerPopupView: View {
    
    @Binding var isPresented: Bool
    @Binding var selectedDate: Date
    let onConfirm: () -> Void
    
    @State private var animationTrigger = false
    @State private var tempDate: Date = Date()
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 日期选择器弹窗
                VStack(spacing: 0) {
                    // 标题
                    Text("growth_diary.date.picker.title".localized)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(.top, 24)
                        .padding(.bottom, 20)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 日期选择器
                    DatePicker(
                        "",
                        selection: $tempDate,
                        displayedComponents: [.date]
                    )
                    .datePickerStyle(WheelDatePickerStyle())
                    .labelsHidden()
                    .padding(.horizontal, 20)
                    .padding(.vertical, 20)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 按钮区域
                    HStack(spacing: 0) {
                        // 取消按钮
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(maxWidth: .infinity, minHeight: 50)
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(width: 1, height: 50)
                        
                        // 确认按钮
                        Button(action: {
                            selectedDate = tempDate
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                            onConfirm()
                        }) {
                            Text("common.button.confirm".localized)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(Color(hex: "#a9d051"))
                                .frame(maxWidth: .infinity, minHeight: 50)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white)
                        .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                )
                .frame(maxWidth: 320)
                .scaleEffect(animationTrigger ? 1.0 : 0.8)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: animationTrigger)
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                tempDate = selectedDate
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()
        
        DatePickerPopupView(
            isPresented: .constant(true),
            selectedDate: .constant(Date()),
            onConfirm: { print("日期确认") }
        )
    }
}
