//
//  ClassOperationFormView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 全班操作表单弹窗组件
 * 用于输入全班加分或扣分的名称和分值
 */
struct ClassOperationFormView: View {
    
    @Binding var isPresented: Bool
    let operationType: ClassOperationType
    let onSubmit: (String, Int) -> Void
    let onCancel: () -> Void
    
    @State private var operationName: String = ""
    @State private var operationValue: String = ""
    @State private var animationTrigger = false
    @State private var showValidationErrors = false
    @State private var isSubmitting = false
    
    // MARK: - Computed Properties
    
    /**
     * 表单标题
     */
    private var formTitle: String {
        switch operationType {
        case .add:
            return "class_operation.form.add_title".localized
        case .deduct:
            return "class_operation.form.deduct_title".localized
        }
    }
    
    /**
     * 表单验证结果
     */
    private var validationResult: FormValidationResult {
        var errors: [String] = []
        
        // 验证操作名称
        if operationName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("class_operation.validation.name_empty".localized)
        } else if operationName.count > 20 {
            errors.append("class_operation.validation.name_too_long".localized)
        }
        
        // 验证分值
        if operationValue.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("class_operation.validation.value_empty".localized)
        } else if let value = Int(operationValue) {
            if value <= 0 {
                errors.append("class_operation.validation.value_positive".localized)
            } else if value > 100 {
                errors.append("class_operation.validation.value_too_high".localized)
            }
        } else {
            errors.append("class_operation.validation.value_invalid".localized)
        }
        
        return FormValidationResult(
            isValid: errors.isEmpty,
            errorMessages: errors
        )
    }
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            closeForm()
                        }
                    }
                    .transition(.opacity)
                
                // 表单卡片
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text(formTitle)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                        
                        // 关闭按钮
                        Button(action: {
                            dismissKeyboard()
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                closeForm()
                            }
                        }) {
                            Image(systemName: "xmark")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(width: 28, height: 28)
                                .background(Color(hex: "#f5f5f5"))
                                .clipShape(Circle())
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    .onTapGesture {
                        // 点击标题栏区域关闭键盘
                        dismissKeyboard()
                    }
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 表单内容
                    VStack(spacing: 16) {
                        // 操作名称输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("class_operation.form.name_label".localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("class_operation.form.name_placeholder".localized, text: $operationName)
                                .textFieldStyle(CustomTextFieldStyle())
                                .disabled(isSubmitting)
                        }
                        
                        // 分值输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("class_operation.form.value_label".localized)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("class_operation.form.value_placeholder".localized, text: $operationValue)
                                .textFieldStyle(CustomTextFieldStyle())
                                .keyboardType(.numberPad)
                                .disabled(isSubmitting)
                        }
                        
                        // 错误信息显示
                        if showValidationErrors && !validationResult.isValid {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(validationResult.errorMessages, id: \.self) { error in
                                    HStack(spacing: 6) {
                                        Image(systemName: "exclamationmark.circle.fill")
                                            .font(.system(size: 12))
                                            .foregroundColor(.red)
                                        
                                        Text(error)
                                            .font(.system(size: 12, weight: .regular))
                                            .foregroundColor(.red)
                                        
                                        Spacer()
                                    }
                                }
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.red.opacity(0.08))
                            .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .contentShape(Rectangle()) // 确保整个表单内容区域能响应手势
                    .onTapGesture {
                        // 点击表单内容区域关闭键盘
                        dismissKeyboard()
                    }
                    
                    // 按钮区域
                    HStack(spacing: 12) {
                        // 取消按钮
                        Button(action: {
                            dismissKeyboard()
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                closeForm()
                            }
                        }) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(height: 44)
                                .frame(maxWidth: .infinity)
                                .background(Color(hex: "#f5f5f5"))
                                .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .disabled(isSubmitting)
                        
                        // 确认按钮
                        Button(action: {
                            dismissKeyboard()
                            submitForm()
                        }) {
                            HStack(spacing: 6) {
                                if isSubmitting {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                }
                                
                                Text(isSubmitting ? "class_operation.form.submitting".localized : "class_operation.form.submit".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .frame(height: 44)
                            .frame(maxWidth: .infinity)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .disabled(isSubmitting)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 20)
                }
                .frame(width: 320)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                // 重置表单状态
                resetForm()
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 提交表单
     */
    private func submitForm() {
        // 显示验证错误
        showValidationErrors = true
        
        // 验证表单
        let validation = validationResult
        guard validation.isValid else { return }
        
        // 开始提交
        isSubmitting = true
        
        // 延迟执行，模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let trimmedName = operationName.trimmingCharacters(in: .whitespacesAndNewlines)
            let value = Int(operationValue) ?? 0
            
            onSubmit(trimmedName, value)
            isSubmitting = false
        }
    }
    
    /**
     * 关闭表单
     */
    private func closeForm() {
        isPresented = false
        onCancel()
    }
    
    /**
     * 重置表单
     */
    private func resetForm() {
        operationName = ""
        operationValue = ""
        showValidationErrors = false
        isSubmitting = false
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

/**
 * 全班操作类型枚举
 */
enum ClassOperationType: String, CaseIterable {
    case add = "add"
    case deduct = "deduct"
    
    var displayName: String {
        switch self {
        case .add:
            return "class_operation.type.add".localized
        case .deduct:
            return "class_operation.type.deduct".localized
        }
    }
}

/**
 * 表单验证结果
 */
struct FormValidationResult {
    let isValid: Bool
    let errorMessages: [String]
}

/**
 * 自定义文本框样式
 */
struct CustomTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 12)
            .background(Color(hex: "#f8f8f8"))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(hex: "#e0e0e0"), lineWidth: 1)
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()
        
        ClassOperationFormView(
            isPresented: .constant(true),
            operationType: .add,
            onSubmit: { name, value in
                print("提交：\(name), \(value)")
            },
            onCancel: {
                print("取消")
            }
        )
    }
} 