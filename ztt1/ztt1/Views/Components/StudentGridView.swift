//
//  StudentGridView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 学生网格视图组件
 */
struct StudentGridView: View {

    let students: [Student]
    let isDeleteMode: Bool
    let hasClasses: Bool // 新增：是否有班级
    let onStudentTapped: (Student) -> Void
    let onEnterDeleteMode: () -> Void
    let onExitDeleteMode: () -> Void
    let onDeleteRequested: (Student) -> Void
    let onCreateClassTapped: () -> Void // 新增：创建班级回调
    let onRefresh: () async -> Void // 新增：下拉刷新回调
    
    // 网格列配置
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md),
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md)
    ]
    
    var body: some View {
        ScrollView {
            if students.isEmpty {
                // 空状态 - 根据是否有班级显示不同内容
                VStack(spacing: DesignSystem.Spacing.lg) {
                    if !hasClasses {
                        // 没有班级时显示创建班级按钮
                        Image("个人中心插图")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 120, height: 120)
                            .opacity(0.6)
                        
                        Text("student_grid.no_class.title".localized)
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text("student_grid.no_class.description".localized)
                            .font(.system(
                                size: DesignSystem.Typography.Caption.fontSize,
                                weight: DesignSystem.Typography.Caption.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                        
                        // 创建班级按钮
                        Button(action: onCreateClassTapped) {
                            HStack(spacing: 8) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.system(size: 16, weight: .medium))
                                Text("student_grid.no_class.create_button".localized)
                                    .font(.system(size: 16, weight: .medium))
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(
                                LinearGradient(
                                    colors: [
                                        Color(hex: "#B5E36B"),
                                        Color(hex: "#9AD055")
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(25)
                            .shadow(
                                color: Color(hex: "#B5E36B").opacity(0.3),
                                radius: 8,
                                x: 0,
                                y: 4
                            )
                        }
                        .scaleEffect(1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: hasClasses)
                        .padding(.top, 16)
                    }
                }
                .padding(.top, 60)
            } else {
                LazyVGrid(columns: columns, spacing: DesignSystem.Spacing.md) {
                    ForEach(students) { student in
                        StudentCardView(
                            student: student,
                            isDeleteMode: isDeleteMode,
                            onLongPress: {
                                onEnterDeleteMode()
                            },
                            onDeleteTapped: {
                                onDeleteRequested(student)
                            },
                            onCardTapped: {
                                if isDeleteMode {
                                    onExitDeleteMode()
                                } else {
                                    onStudentTapped(student)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, DesignSystem.Spacing.xs)
            }
        }
        .refreshable {
            // 执行下拉刷新
            await onRefresh()
        }
    }
}

// MARK: - Preview
#Preview {
    return VStack {
        // 空状态预览（无班级）
        StudentGridView(
            students: [],
            isDeleteMode: false,
            hasClasses: false,
            onStudentTapped: { student in
                print("点击了学生: \(student.name ?? "未知")")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { student in
                print("请求删除学生: \(student.name ?? "未知")")
            },
            onCreateClassTapped: {
                print("点击创建班级")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}

#Preview("Empty State") {
    return VStack {
        // 空状态
        StudentGridView(
            students: [],
            isDeleteMode: false,
            hasClasses: true,
            onStudentTapped: { student in
                print("点击了学生: \(student.name ?? "未知")")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { student in
                print("请求删除学生: \(student.name ?? "未知")")
            },
            onCreateClassTapped: {
                print("点击创建班级")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
} 