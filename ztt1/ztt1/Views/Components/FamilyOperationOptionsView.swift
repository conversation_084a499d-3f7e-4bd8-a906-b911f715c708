//
//  FamilyOperationOptionsView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 全员操作选项菜单组件
 * 显示全家加分和全家扣分两个选项的弹窗菜单
 */
struct FamilyOperationOptionsView: View {
    
    @Binding var isPresented: Bool
    let onAddPoints: () -> Void
    let onDeductPoints: () -> Void
    
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 选项菜单
                VStack(spacing: 0) {
                    // 标题
                    Text("family_operation.options.title".localized)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(.top, 24)
                        .padding(.bottom, 20)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 选项列表
                    VStack(spacing: 0) {
                        // 全家加分选项
                        FamilyOperationOptionRow(
                            icon: "plus.circle.fill",
                            title: "family_operation.options.add_points".localized,
                            description: "family_operation.options.add_description".localized,
                            color: Color(hex: "#a9d051"),
                            action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    onAddPoints()
                                }
                            }
                        )
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 20)
                        
                        // 全家扣分选项
                        FamilyOperationOptionRow(
                            icon: "minus.circle.fill",
                            title: "family_operation.options.deduct_points".localized,
                            description: "family_operation.options.deduct_description".localized,
                            color: Color(hex: "#ff6b6b"),
                            action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    onDeductPoints()
                                }
                            }
                        )
                    }
                    .padding(.bottom, 20)
                }
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white)
                        .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                )
                .frame(maxWidth: 320)
                .scaleEffect(animationTrigger ? 1.0 : 0.8)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: animationTrigger)
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
}

/**
 * 全员操作选项行组件
 */
struct FamilyOperationOptionRow: View {
    
    let icon: String
    let title: String
    let description: String
    let color: Color
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isPressed = false
                }
                action()
            }
        }) {
            HStack(spacing: 16) {
                // 图标
                ZStack {
                    Circle()
                        .fill(color.opacity(0.15))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(color)
                }
                
                // 文字内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text(description)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                Rectangle()
                    .fill(isPressed ? Color(hex: "#f8ffe5") : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()
        
        FamilyOperationOptionsView(
            isPresented: .constant(true),
            onAddPoints: { print("全家加分") },
            onDeductPoints: { print("全家扣分") }
        )
    }
}
