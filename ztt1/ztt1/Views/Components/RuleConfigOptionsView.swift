//
//  RuleConfigOptionsView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 规则配置选项菜单组件
 * 显示加分规则配置和扣分规则配置两个选项的下拉菜单
 */
struct RuleConfigOptionsView: View {
    
    @Binding var isPresented: Bool
    let onAddRulesConfig: () -> Void
    let onDeductRulesConfig: () -> Void
    
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 选项菜单卡片
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("Select rule type".localized)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 选项列表
                    VStack(spacing: 0) {
                        // 加分规则配置选项
                        RuleConfigOptionButton(
                            title: "Add points rules".localized,
                            subtitle: "Set the commonly reasons and points for adding points".localized,
                            iconName: "plus.circle",
                            iconColor: Color(hex: "#74c07f"),
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    onAddRulesConfig()
                                }
                            }
                        )
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#f5f5f5"))
                            .frame(height: 1)
                            .padding(.horizontal, 20)
                        
                        // 扣分规则配置选项
                        RuleConfigOptionButton(
                            title: "Dedect points rules".localized,
                            subtitle: "Set the commonly reasons and points for deducting points".localized,
                            iconName: "minus.circle",
                            iconColor: Color(hex: "#e74c3c"),
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    onDeductRulesConfig()
                                }
                            }
                        )
                    }
                    .padding(.bottom, 10)
                }
                .frame(width: 300)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
}

/**
 * 规则配置选项按钮组件
 */
private struct RuleConfigOptionButton: View {
    
    let title: String
    let subtitle: String
    let iconName: String
    let iconColor: Color
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isPressed = false
                action()
            }
        }) {
            HStack(spacing: 16) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: iconName)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(iconColor)
                }
                
                // 文字内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.6))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                Rectangle()
                    .fill(isPressed ? Color(hex: "#f8ffe5") : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        RuleConfigOptionsView(
            isPresented: .constant(true),
            onAddRulesConfig: {
                print("选择配置加分规则")
            },
            onDeductRulesConfig: {
                print("选择配置扣分规则")
            }
        )
    }
} 