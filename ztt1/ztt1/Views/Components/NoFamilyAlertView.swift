//
//  NoFamilyAlertView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/27.
//

import SwiftUI

/**
 * 无家庭提示弹窗组件
 * 当用户尝试添加成员但还没有创建家庭时显示的提示弹窗
 * 基于NoClassAlertView设计，保持完全一致的UI风格
 */
struct NoFamilyAlertView: View {
    
    @Binding var isPresented: Bool
    let onCreateFamily: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        onCancel()
                    }
                    .transition(.opacity)
                
                // 提示对话框卡片
                VStack(spacing: 20) {
                    // 提示图标
                    Image(systemName: "house.circle.fill")
                        .font(.system(size: 50))
                        .foregroundColor(Color(hex: "#a9d051"))
                        .padding(.top, 10)
                    
                    // 标题
                    Text("add_member.no_family.title".localized)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    // 内容
                    Text("add_member.no_family.message".localized)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                    
                    // 提示信息
                    VStack(spacing: 8) {
                        Text("add_member.no_family.instruction".localized)
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                        
                        Text("add_member.no_family.create_hint".localized)
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(Color(hex: "#a9d051"))
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal, 20)
                    
                    // 按钮区域
                    HStack(spacing: 12) {
                        // 取消按钮
                        Button(action: onCancel) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.ConfirmationDialog.cancelButtonColor)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color.white)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(DesignSystem.ConfirmationDialog.cancelButtonColor, lineWidth: 1.5)
                                )
                                .cornerRadius(12)
                        }
                        
                        // 创建家庭按钮
                        Button(action: onCreateFamily) {
                            Text("add_member.no_family.create_button".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#a9d051"),
                                            Color(hex: "#8bb83f")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 10)
                }
                .frame(maxWidth: DesignSystem.ConfirmationDialog.maxWidth + 20)
                .background(DesignSystem.ConfirmationDialog.backgroundColor)
                .cornerRadius(DesignSystem.ConfirmationDialog.cornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.ConfirmationDialog.cornerRadius)
                        .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 2)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                .scaleEffect(isPresented ? 1.0 : 0.8)
                .opacity(isPresented ? 1.0 : 0.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPresented)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        NoFamilyAlertView(
            isPresented: .constant(true),
            onCreateFamily: {
                print("创建家庭")
            },
            onCancel: {
                print("取消")
            }
        )
    }
}
