//
//  ResetHistoryConfirmView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 重置历史记录确认组件
 * 显示操作警告信息并要求用户确认清除所有历史记录
 */
struct ResetHistoryConfirmView: View {
    
    @Binding var isPresented: Bool
    let className: String
    let studentCount: Int
    let totalRecords: Int
    let onConfirm: () -> Void
    
    @State private var showFinalConfirmation = false
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 确认卡片
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("class_reset.history.title".localized)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(Color(hex: "#ff6b6b"))
                        
                        Spacer()
                        
                        // 关闭按钮
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#ffe5e5"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 警告内容
                    VStack(spacing: 20) {
                        // 警告图标和描述
                        VStack(spacing: 12) {
                            // 警告图标
                            ZStack {
                                Circle()
                                    .fill(Color(hex: "#ff6b6b").opacity(0.1))
                                    .frame(width: 60, height: 60)
                                
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .font(.system(size: 28, weight: .medium))
                                    .foregroundColor(Color(hex: "#ff6b6b"))
                            }
                            .scaleEffect(animationTrigger ? 1.1 : 1.0)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: animationTrigger)
                            
                            // 警告文本
                            Text("class_reset.history.warning".localized)
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(Color(hex: "#ff6b6b"))
                        }
                        
                        // 详细描述
                        VStack(alignment: .leading, spacing: 12) {
                            Text("class_reset.history.description".localized(with: className))
                                .font(.system(size: 14, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                            
                            Text("class_reset.history.details".localized(with: studentCount, totalRecords))
                                .font(.system(size: 13, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity)
                        
                        // 影响说明
                        VStack(spacing: 8) {
                            HStack {
                                Image(systemName: "info.circle.fill")
                                    .font(.system(size: 14))
                                    .foregroundColor(Color(hex: "#a9d051"))
                                
                                Text("class_reset.history.record_types_title".localized)
                                    .font(.system(size: 13, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                
                                Spacer()
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("class_reset.history.record_type.points".localized)
                                        .font(.system(size: 12, weight: .regular))
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                    Spacer()
                                }
                                HStack {
                                    Text("class_reset.history.record_type.exchange".localized)
                                        .font(.system(size: 12, weight: .regular))
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                    Spacer()
                                }
                                HStack {
                                    Text("class_reset.history.record_type.lottery".localized)
                                        .font(.system(size: 12, weight: .regular))
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                    Spacer()
                                }
                            }
                        }
                        .padding(12)
                        .background(Color(hex: "#f8ffe5"))
                        .cornerRadius(8)
                        
                        // 重要提示
                        HStack(spacing: 8) {
                            Image(systemName: "hand.raised.fill")
                                .font(.system(size: 14))
                                .foregroundColor(Color(hex: "#ff6b6b"))
                            
                            Text("class_reset.history.notice".localized)
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(Color(hex: "#ff6b6b"))
                            
                            Spacer()
                        }
                        .padding(12)
                        .background(Color(hex: "#fff5f5"))
                        .cornerRadius(8)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 20)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#ffe5e5"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 按钮区域
                    HStack(spacing: 12) {
                        // 取消按钮
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Text("common.button.cancel".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color(hex: "#f5f5f5"))
                                .cornerRadius(8)
                        }
                        
                        // 确认清除按钮
                        Button(action: {
                            showFinalConfirmation = true
                        }) {
                            Text("class_reset.history.confirm_button".localized)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color(hex: "#ff6b6b"))
                                .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .frame(width: 340)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#ff6b6b").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
        .alert("class_reset.history.final_confirm_title".localized, isPresented: $showFinalConfirmation) {
            Button("class_reset.history.final_cancel".localized, role: .cancel) { }
            Button("class_reset.history.confirm_button".localized, role: .destructive) {
                onConfirm()
                isPresented = false
            }
        } message: {
            Text("class_reset.history.final_confirm_message".localized(with: className, totalRecords))
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        ResetHistoryConfirmView(
            isPresented: .constant(true),
            className: "一年级1班",
            studentCount: 25,
            totalRecords: 156,
            onConfirm: {
                print("确认清除历史记录")
            }
        )
    }
} 