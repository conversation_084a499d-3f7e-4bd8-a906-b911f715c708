//
//  ConfiguredPrizesListView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 已配置奖品列表组件
 * 显示已创建的奖品模板，支持左滑删除功能
 */
struct ConfiguredPrizesListView: View {
    
    @Binding var isPresented: Bool
    @State private var prizes: [PrizeTemplate] = []
    @State private var animationTrigger = false
    @State private var showDeleteConfirmation = false
    @State private var prizeToDelete: PrizeTemplate?
    @State private var isDeleting = false
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        if !isDeleting {
                            closeView()
                        }
                    }
                    .transition(.opacity)
                
                // 列表对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text("prize_config.configured_list.title".localized)
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            // 关闭按钮
                            Button(action: {
                                if !isDeleting {
                                    closeView()
                                }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(Color.gray.opacity(0.6))
                            }
                            .disabled(isDeleting)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 奖品列表内容
                        if prizes.isEmpty {
                            // 空状态
                            VStack(spacing: 16) {
                                Image(systemName: "gift")
                                    .font(.system(size: 48))
                                    .foregroundColor(Color(hex: "#a9d051").opacity(0.4))
                                
                                Text("prize_config.configured_list.empty".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(nil)
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .padding(.vertical, 40)
                        } else {
                            // 奖品列表
                            ScrollView(.vertical, showsIndicators: true) {
                                LazyVStack(spacing: 12) {
                                    ForEach(groupedPrizes.keys.sorted(), id: \.self) { type in
                                        if let typePrizes = groupedPrizes[type], !typePrizes.isEmpty {
                                            // 类型分组标题
                                            HStack {
                                                Text(type)
                                                    .font(.system(size: 16, weight: .semibold))
                                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                                                
                                                Rectangle()
                                                    .fill(Color(hex: "#a9d051").opacity(0.3))
                                                    .frame(height: 1)
                                            }
                                            .padding(.horizontal, 24)
                                            .padding(.top, type == groupedPrizes.keys.sorted().first ? 16 : 24)
                                            
                                            // 该类型的奖品列表
                                            ForEach(typePrizes, id: \.self) { prize in
                                                PrizeListRow(
                                                    prize: prize,
                                                    onDelete: {
                                                        prizeToDelete = prize
                                                        showDeleteConfirmation = true
                                                    }
                                                )
                                                .padding(.horizontal, 24)
                                            }
                                        }
                                    }
                                }
                                .padding(.bottom, 20)
                            }
                            .frame(maxHeight: geometry.size.height * 0.6)
                        }
                        
                        // 底部空白
                        Spacer(minLength: 20)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 420))
                    .frame(maxHeight: geometry.size.height * 0.75)
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#74c07f").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            loadPrizes()
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                loadPrizes()
            } else {
                animationTrigger = false
            }
        }
        .alert("prize_config.delete.confirmation.title".localized, isPresented: $showDeleteConfirmation) {
            Button("prize_config.delete.confirmation.cancel".localized, role: .cancel) {}
            Button("prize_config.delete.confirmation.confirm".localized, role: .destructive) {
                deletePrize()
            }
        } message: {
            if let prize = prizeToDelete {
                Text("prize_config.delete.confirmation.message".localized(with: prize.name ?? ""))
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 按类型分组的奖品
     */
    private var groupedPrizes: [String: [PrizeTemplate]] {
        Dictionary(grouping: prizes) { prize in
            prize.type ?? "虚拟"
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 加载奖品列表
     */
    private func loadPrizes() {
        prizes = coreDataManager.getAllPrizeTemplates().sorted { prize1, prize2 in
            // 首先按类型排序
            let type1 = prize1.type ?? "虚拟"
            let type2 = prize2.type ?? "虚拟"
            if type1 != type2 {
                return type1 < type2
            }
            // 同类型内按积分成本排序
            return prize1.cost < prize2.cost
        }
    }
    
    /**
     * 关闭视图
     */
    private func closeView() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            isPresented = false
        }
    }
    
    /**
     * 删除奖品
     */
    private func deletePrize() {
        guard let prize = prizeToDelete else { return }
        
        isDeleting = true
        
        // 从CoreData删除奖品模板
        coreDataManager.viewContext.delete(prize)
        
        do {
            try coreDataManager.viewContext.save()
            // 重新加载列表
            loadPrizes()
        } catch {
            print("删除奖品模板失败: \(error)")
        }
        
        prizeToDelete = nil
        isDeleting = false
    }
}

/**
 * 奖品列表行组件
 */
private struct PrizeListRow: View {
    
    let prize: PrizeTemplate
    let onDelete: () -> Void
    
    @State private var showingDeleteButton = false
    
    var body: some View {
        HStack(spacing: 16) {
            // 奖品图标
            ZStack {
                Circle()
                    .fill(Color(hex: "#74c07f").opacity(0.1))
                    .frame(width: 44, height: 44)
                
                Image(systemName: prize.prizeType == .physical ? "gift.fill" : "star.fill")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(Color(hex: "#74c07f"))
            }
            
            // 奖品信息
            VStack(alignment: .leading, spacing: 4) {
                Text(prize.name ?? "")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                
                HStack(spacing: 12) {
                    // 奖品类型标签
                    Text(prize.prizeType.displayName)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(prize.prizeType == .physical ? 
                                      Color(hex: "#e67e22") : Color(hex: "#3498db"))
                        )
                    
                    // 积分成本
                    Text("\(prize.cost)积分")
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // 删除按钮
            if showingDeleteButton {
                Button(action: onDelete) {
                    Image(systemName: "trash.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(width: 32, height: 32)
                        .background(Color.red)
                        .cornerRadius(8)
                }
                .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
        .gesture(
            DragGesture(minimumDistance: 20)
                .onEnded { value in
                    if value.translation.width < -50 {
                        // 左滑显示删除按钮
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            showingDeleteButton = true
                        }
                    } else if value.translation.width > 50 {
                        // 右滑隐藏删除按钮
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            showingDeleteButton = false
                        }
                    }
                }
        )
        .onTapGesture {
            // 点击隐藏删除按钮
            if showingDeleteButton {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    showingDeleteButton = false
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    ConfiguredPrizesListView(
        isPresented: .constant(true)
    )
} 