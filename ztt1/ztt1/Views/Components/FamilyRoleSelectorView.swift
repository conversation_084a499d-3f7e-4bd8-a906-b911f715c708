//
//  FamilyRoleSelectorView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import SwiftUI

/**
 * 家庭角色选择器视图
 * 用于添加或编辑家庭成员时选择角色
 */
struct FamilyRoleSelectorView: View {
    @Binding var selectedRole: FamilyRole
    @Binding var selectedGender: Gender
    let family: Family?
    let isEditing: Bool
    
    @StateObject private var permissionManager = FamilyPermissionManager.shared
    
    init(
        selectedRole: Binding<FamilyRole>,
        selectedGender: Binding<Gender>,
        family: Family? = nil,
        isEditing: Bool = false
    ) {
        self._selectedRole = selectedRole
        self._selectedGender = selectedGender
        self.family = family
        self.isEditing = isEditing
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // 角色选择标题
            HStack {
                Text("选择家庭角色")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            // 角色选择网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(availableRoles, id: \.self) { role in
                    RoleOptionView(
                        role: role,
                        isSelected: selectedRole == role,
                        isAvailable: isRoleAvailable(role),
                        action: {
                            selectRole(role)
                        }
                    )
                }
            }
            
            // 性别选择（仅对"其他"角色显示）
            if selectedRole == .other {
                Divider()
                
                VStack(spacing: 12) {
                    HStack {
                        Text("选择性别")
                            .font(.subheadline)
                            .foregroundColor(.primary)
                        Spacer()
                    }
                    
                    HStack(spacing: 16) {
                        ForEach(Gender.allCases, id: \.self) { gender in
                            GenderOptionView(
                                gender: gender,
                                isSelected: selectedGender == gender,
                                action: {
                                    selectedGender = gender
                                }
                            )
                        }
                        Spacer()
                    }
                }
            }
            
            // 角色说明
            if !selectedRole.description.isEmpty {
                VStack(spacing: 8) {
                    HStack {
                        Text("角色说明")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    
                    HStack {
                        Text(selectedRole.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                        Spacer()
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
    }
    
    // MARK: - 计算属性
    
    /**
     * 可用的角色列表
     */
    private var availableRoles: [FamilyRole] {
        if isEditing {
            // 编辑模式下，显示当前角色和其他可用角色
            return FamilyRole.allCases
        } else {
            // 添加模式下，根据性别推荐角色
            return FamilyRole.recommendedRoles(for: selectedGender)
        }
    }
    
    // MARK: - 私有方法
    
    /**
     * 检查角色是否可用
     */
    private func isRoleAvailable(_ role: FamilyRole) -> Bool {
        guard let family = family else { return true }
        
        // 如果是编辑模式且是当前角色，则可用
        if isEditing && role == selectedRole {
            return true
        }
        
        // 检查唯一性角色是否已存在
        if role.isUnique {
            return !family.hasRole(role.rawValue)
        }
        
        return true
    }
    
    /**
     * 选择角色
     */
    private func selectRole(_ role: FamilyRole) {
        guard isRoleAvailable(role) else { return }
        
        selectedRole = role
        
        // 自动设置对应的性别
        if role != .other {
            selectedGender = role.defaultGender
        }
    }
}

// MARK: - 子组件

/**
 * 角色选项视图
 */
struct RoleOptionView: View {
    let role: FamilyRole
    let isSelected: Bool
    let isAvailable: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // 角色头像
                ZStack {
                    Circle()
                        .fill(isSelected ? role.avatarColor : Color.gray.opacity(0.2))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: role.iconName)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(isSelected ? .white : .gray)
                }
                .overlay(
                    Circle()
                        .stroke(
                            isSelected ? role.avatarColor : Color.clear,
                            lineWidth: 3
                        )
                )
                
                // 角色名称
                Text(role.shortName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? role.avatarColor : .primary)
                
                // 不可用提示
                if !isAvailable {
                    Text("已存在")
                        .font(.caption2)
                        .foregroundColor(.red)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(4)
                }
            }
            .padding(.vertical, 12)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? role.avatarColor.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                isSelected ? role.avatarColor : Color.gray.opacity(0.3),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!isAvailable)
        .opacity(isAvailable ? 1.0 : 0.6)
    }
}

/**
 * 性别选项视图
 */
struct GenderOptionView: View {
    let gender: Gender
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: gender.iconName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isSelected ? .white : gender.color)
                
                Text(gender.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? gender.color : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(gender.color, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览

#Preview("角色选择器") {
    FamilyRoleSelectorView(
        selectedRole: .constant(.father),
        selectedGender: .constant(.male)
    )
}

#Preview("性别选择") {
    FamilyRoleSelectorView(
        selectedRole: .constant(.other),
        selectedGender: .constant(.male)
    )
}
