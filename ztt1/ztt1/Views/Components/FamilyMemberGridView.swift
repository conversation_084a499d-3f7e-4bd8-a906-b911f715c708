//
//  FamilyMemberGridView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/27.
//

import SwiftUI

/**
 * 家庭成员网格视图组件
 * 基于StudentGridView设计，保持完全一致的UI和交互
 */
struct FamilyMemberGridView: View {

    let members: [FamilyMember]
    let isDeleteMode: Bool
    let hasFamilies: Bool // 是否有家庭
    let onMemberTapped: (FamilyMember) -> Void
    let onEnterDeleteMode: () -> Void
    let onExitDeleteMode: () -> Void
    let onDeleteRequested: (FamilyMember) -> Void
    let onCreateFamilyTapped: () -> Void // 创建家庭回调
    let onRefresh: () async -> Void // 下拉刷新回调
    
    // 网格列配置
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md),
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md)
    ]
    
    var body: some View {
        ScrollView {
            if members.isEmpty {
                // 空状态视图
                VStack(spacing: DesignSystem.Spacing.lg) {
                    if !hasFamilies {
                        // 无家庭状态
                        VStack(spacing: DesignSystem.Spacing.md) {
                            Image(systemName: "house")
                                .font(.system(size: 60))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                            
                            Text("还没有创建家庭")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("点击下方按钮创建您的第一个家庭")
                                .font(.system(size: 14))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                                .multilineTextAlignment(.center)
                            
                            Button(action: onCreateFamilyTapped) {
                                HStack {
                                    Image(systemName: "plus")
                                    Text("创建家庭")
                                }
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 24)
                                .padding(.vertical, 12)
                                .background(DesignSystem.Colors.primary)
                                .cornerRadius(25)
                            }
                        }
                    } else {
                        // 有家庭但无成员状态
                        VStack(spacing: DesignSystem.Spacing.md) {
                            Image(systemName: "person.2")
                                .font(.system(size: 60))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                            
                            Text("还没有家庭成员")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("点击右上角的"+"按钮添加家庭成员")
                                .font(.system(size: 14))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                                .multilineTextAlignment(.center)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, 60)
            } else {
                LazyVGrid(columns: columns, spacing: DesignSystem.Spacing.md) {
                    ForEach(Array(members.enumerated()), id: \.element.id) { index, member in
                        FamilyMemberCardView(
                            member: member,
                            memberIndex: index + 1,  // 从1开始编号
                            isDeleteMode: isDeleteMode,
                            onLongPress: {
                                onEnterDeleteMode()
                            },
                            onDeleteTapped: {
                                onDeleteRequested(member)
                            },
                            onCardTapped: {
                                if isDeleteMode {
                                    onExitDeleteMode()
                                } else {
                                    onMemberTapped(member)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, DesignSystem.Spacing.xs)
            }
        }
        .refreshable {
            // 执行下拉刷新
            await onRefresh()
        }
    }
}

// MARK: - Preview
#Preview {
    return VStack {
        // 空状态预览（无家庭）
        FamilyMemberGridView(
            members: [],
            isDeleteMode: false,
            hasFamilies: false,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name ?? "未知")")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name ?? "未知")")
            },
            onCreateFamilyTapped: {
                print("点击创建家庭")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}

#Preview("Empty State") {
    return VStack {
        // 空状态（有家庭但无成员）
        FamilyMemberGridView(
            members: [],
            isDeleteMode: false,
            hasFamilies: true,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name ?? "未知")")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name ?? "未知")")
            },
            onCreateFamilyTapped: {
                print("点击创建家庭")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}

#Preview("With Members") {
    let context = PersistenceController.preview.container.viewContext

    // 创建示例家庭成员
    let father = FamilyMember(context: context)
    father.id = UUID()
    father.name = "爸爸"
    father.role = "father"
    father.gender = "male"
    father.currentPoints = 120
    father.createdAt = Date()

    let mother = FamilyMember(context: context)
    mother.id = UUID()
    mother.name = "妈妈"
    mother.role = "mother"
    mother.gender = "female"
    mother.currentPoints = 135
    mother.createdAt = Date()

    let son = FamilyMember(context: context)
    son.id = UUID()
    son.name = "小明"
    son.role = "son"
    son.gender = "male"
    son.currentPoints = 85
    son.createdAt = Date()

    let daughter = FamilyMember(context: context)
    daughter.id = UUID()
    daughter.name = "小红"
    daughter.role = "daughter"
    daughter.gender = "female"
    daughter.currentPoints = 92
    daughter.createdAt = Date()

    return VStack {
        FamilyMemberGridView(
            members: [father, mother, son, daughter],
            isDeleteMode: false,
            hasFamilies: true,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name ?? "未知")")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name ?? "未知")")
            },
            onCreateFamilyTapped: {
                print("点击创建家庭")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}
