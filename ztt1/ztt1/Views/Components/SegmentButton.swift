//
//  SegmentButton.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/8.
//

import SwiftUI

/**
 * 自定义分段按钮组件
 * 解决iPad上SegmentedPicker需要长按才能触发的问题
 */
struct SegmentButton<T: Hashable>: View {
    @Binding var selection: T
    let options: [T]
    let labels: [T: String]
    
    // 自定义样式属性
    var backgroundColor: Color = Color(.systemBackground)
    var selectedColor: Color = Color(.systemBlue)
    var textColor: Color = Color(.label)
    var selectedTextColor: Color = .white
    var cornerRadius: CGFloat = 7.0
    var fontSize: CGFloat = 14.0
    var height: CGFloat = 32
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(options, id: \.self) { option in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        self.selection = option
                    }
                }) {
                    Text(labels[option] ?? "\(option)")
                        .font(.system(size: fontSize))
                        .fontWeight(selection == option ? .medium : .regular)
                        .foregroundColor(selection == option ? selectedTextColor : textColor)
                        .frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height: height)
                        .background(selection == option ? selectedColor : backgroundColor)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(backgroundColor)
        .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        .overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
                .stroke(selectedColor, lineWidth: 1)
                .opacity(0.2)
        )
    }
}

// 便捷使用的扩展
extension SegmentButton where T == String {
    init(selection: Binding<String>, options: [String]) {
        self._selection = selection
        self.options = options
        self.labels = Dictionary(uniqueKeysWithValues: options.map { ($0, $0) })
    }
}

// MARK: - Preview
struct SegmentButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            SegmentButton(
                selection: .constant("选项1"),
                options: ["选项1", "选项2", "选项3"]
            )
            
            SegmentButton(
                selection: .constant("male"),
                options: ["male", "female"],
                labels: ["male": "男", "female": "女"],
                selectedColor: Color(hex: "#a9d051")
            )
        }
        .padding()
    }
} 