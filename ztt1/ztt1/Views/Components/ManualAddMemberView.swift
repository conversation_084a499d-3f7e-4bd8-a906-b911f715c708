//
//  ManualAddMemberView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 手动添加成员表单组件
 * 支持单个和批量成员信息输入，可动态添加表单行
 */
struct ManualAddMemberView: View {
    
    @Binding var isPresented: Bool
    @State private var memberForms: [MemberFormData] = [MemberFormData()]
    @State private var isSubmitting: Bool = false
    @State private var validationErrors: [String] = []
    @State private var animationTrigger = false
    
    let onSubmit: ([MemberFormData]) -> Void
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    // 注释掉点击外部区域关闭弹窗的手势，防止误操作
                    // .onTapGesture {
                    //     if !isSubmitting {
                    //         onCancel()
                    //     }
                    // }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text("add_member.form.title".localized)
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            // 添加成员按钮
                            Button(action: addNewMemberForm) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.system(size: 26))
                                    .foregroundColor(Color(hex: "#a9d051"))
                            }
                            .disabled(isSubmitting)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        .onTapGesture {
                            // 点击标题栏区域关闭键盘
                            dismissKeyboard()
                        }
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 表单内容区域
                        ScrollView(.vertical, showsIndicators: true) {
                            VStack(spacing: 16) {
                                ForEach(memberForms.indices, id: \.self) { index in
                                    MemberFormRow(
                                        member: $memberForms[index],
                                        index: index,
                                        canDelete: memberForms.count > 1,
                                        onDelete: {
                                            removeMemberForm(at: index)
                                        }
                                    )
                                    .disabled(isSubmitting)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                        }
                        .frame(maxHeight: geometry.size.height * 0.5)
                        .contentShape(Rectangle()) // 确保整个滚动区域都能响应手势
                        .onTapGesture {
                            // 点击表单内容区域关闭键盘
                            dismissKeyboard()
                        }
                        
                        // 验证错误信息
                        if !validationErrors.isEmpty {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(validationErrors, id: \.self) { error in
                                    HStack {
                                        Image(systemName: "exclamationmark.triangle.fill")
                                            .foregroundColor(.red)
                                            .font(.system(size: 12))
                                        Text(error)
                                            .font(.system(size: 12))
                                            .foregroundColor(.red)
                                        Spacer()
                                    }
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.red.opacity(0.1))
                        }
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 底部按钮区域
                        HStack(spacing: 16) {
                            // 取消按钮
                            Button(action: {
                                if !isSubmitting {
                                    onCancel()
                                }
                            }) {
                                Text("common.button.cancel".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(Color(hex: "#666666"))
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color(hex: "#f5f5f5"))
                                    )
                            }
                            .disabled(isSubmitting)
                            
                            // 提交按钮
                            Button(action: submitForm) {
                                HStack {
                                    if isSubmitting {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    }
                                    Text(isSubmitting ? "add_member.form.submitting".localized : "add_member.form.submit".localized)
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [
                                                    Color(hex: "#a9d051"),
                                                    Color(hex: "#8bc34a")
                                                ]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                )
                            }
                            .disabled(isSubmitting || memberForms.isEmpty)
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 20)
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.white)
                            .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    )
                    .frame(maxWidth: min(geometry.size.width - 40, 500))
                    .frame(maxHeight: geometry.size.height * 0.85)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                    .scaleEffect(animationTrigger ? 1.0 : 0.8)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: animationTrigger)
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                // 重置表单状态
                resetForm()
            } else {
                animationTrigger = false
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 添加新的成员表单行
     */
    private func addNewMemberForm() {
        let _ = withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            memberForms.append(MemberFormData())
        }
    }
    
    /**
     * 删除指定索引的成员表单行
     */
    private func removeMemberForm(at index: Int) {
        guard memberForms.count > 1 else { return }

        let _ = withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            memberForms.remove(at: index)
        }
    }
    
    /**
     * 提交表单
     */
    private func submitForm() {
        // 验证表单
        let validation = validateForms()
        validationErrors = validation.errors
        
        guard validation.isValid else { return }
        
        // 开始提交
        isSubmitting = true
        
        // 延迟执行，模拟处理时间
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            onSubmit(memberForms)
            isSubmitting = false
        }
    }
    
    /**
     * 重置表单状态
     */
    private func resetForm() {
        memberForms = [MemberFormData()]
        validationErrors = []
        isSubmitting = false
    }
    
    /**
     * 验证所有表单
     */
    private func validateForms() -> (isValid: Bool, errors: [String]) {
        var errors: [String] = []

        for (index, form) in memberForms.enumerated() {
            let memberIndex = index + 1

            // 验证姓名
            if form.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                errors.append("member.form.validation.row_error".localized(with: memberIndex, "member.form.validation.name_empty".localized))
            } else if form.name.count > 20 {
                errors.append("member.form.validation.row_error".localized(with: memberIndex, "member.form.validation.name_too_long".localized))
            }

            // 验证初始积分
            if let points = Int(form.initialPoints), points < 0 {
                errors.append("member.form.validation.row_error".localized(with: memberIndex, "member.form.validation.points_negative".localized))
            } else if let points = Int(form.initialPoints), points > 1000 {
                errors.append("member.form.validation.row_error".localized(with: memberIndex, "member.form.validation.points_too_high".localized))
            } else if !form.initialPoints.isEmpty && Int(form.initialPoints) == nil {
                errors.append("member.form.validation.row_error".localized(with: memberIndex, "member.form.validation.points_invalid".localized))
            }
        }

        return (errors.isEmpty, errors)
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - MemberFormData
/**
 * 成员表单数据模型
 */
struct MemberFormData {
    var name: String = ""
    var initialPoints: String = "0"
    var role: String = "son"
    var gender: String = "male"
    var birthDate: Date = Date()

    /**
     * 格式化后的姓名（去除首尾空格）
     */
    var formattedName: String {
        return name.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /**
     * 初始积分数值
     */
    var initialPointsValue: Int {
        return Int(initialPoints) ?? 0
    }

    /**
     * 计算年龄
     */
    var age: Int {
        let calendar = Calendar.current
        let now = Date()
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: now)
        return ageComponents.year ?? 0
    }
}

// MARK: - MemberFormRow
/**
 * 成员表单行组件
 */
struct MemberFormRow: View {

    @Binding var member: MemberFormData
    let index: Int
    let canDelete: Bool
    let onDelete: () -> Void

    @State private var showRoleSelection = false
    @State private var showDatePicker = false

    var body: some View {
        VStack(spacing: 12) {
            // 行标题和删除按钮
            HStack {
                Text("add_member.form.member_number_label".localized(with: "\(index + 1)"))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                if canDelete {
                    Button(action: onDelete) {
                        Image(systemName: "minus.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.red.opacity(0.7))
                    }
                }
            }

            // 表单字段
            VStack(spacing: 12) {
                // 姓名和初始积分水平布局
                HStack(spacing: 12) {
                    // 姓名输入
                    VStack(alignment: .leading, spacing: 6) {
                        Text("add_member.form.name".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        TextField("add_member.form.name_placeholder".localized, text: $member.name)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    .frame(maxWidth: .infinity)

                    // 初始积分输入
                    VStack(alignment: .leading, spacing: 6) {
                        Text("add_member.form.initial_points".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        TextField("0", text: $member.initialPoints)
                            .keyboardType(.numberPad)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    .frame(width: 100)
                }

                // 角色选择和出生日期
                HStack(spacing: 12) {
                    // 角色选择
                    VStack(alignment: .leading, spacing: 6) {
                        Text("add_member.form.role".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        Button(action: {
                            showRoleSelection = true
                        }) {
                            HStack {
                                Text("member.role.\(member.role)".localized)
                                    .font(.system(size: 14))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)

                                Spacer()

                                Image(systemName: "chevron.down")
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(hex: "#f8fff2"))
                            .cornerRadius(6)
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .frame(maxWidth: 100) // 减小角色选择框宽度

                    // 出生日期选择
                    VStack(alignment: .leading, spacing: 6) {
                        Text("add_member.form.birth_date".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        Button(action: {
                            showDatePicker = true
                        }) {
                            HStack {
                                Text(formatDate(member.birthDate))
                                    .font(.system(size: 14))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)

                                Spacer()

                                Image(systemName: "calendar")
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(hex: "#f8fff2"))
                            .cornerRadius(6)
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
        .padding(16)
        .background(Color(hex: "#f8ffe5").opacity(0.5))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
        .overlay(
            // 角色选择弹窗
            MemberRoleSelectionView(
                selectedRole: $member.role,
                isPresented: $showRoleSelection
            )
        )
        .overlay(
            // 日期选择器
            Group {
                if showDatePicker {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                        .onTapGesture {
                            showDatePicker = false
                        }

                    VStack {
                        Spacer()

                        VStack(spacing: 0) {
                            // 标题栏
                            HStack {
                                Button("取消") {
                                    showDatePicker = false
                                }
                                .foregroundColor(.gray)

                                Spacer()

                                Text("add_member.form.birth_date".localized)
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)

                                Spacer()

                                Button("确定") {
                                    showDatePicker = false
                                }
                                .foregroundColor(Color(hex: "#a9d051"))
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color.white)

                            // 日期选择器
                            DatePicker(
                                "",
                                selection: $member.birthDate,
                                in: ...Date(),
                                displayedComponents: .date
                            )
                            .datePickerStyle(WheelDatePickerStyle())
                            .labelsHidden()
                            .background(Color.white)
                        }
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
                    }
                    .padding(.horizontal, 20)
                    .transition(.move(edge: .bottom))
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showDatePicker)
        )
    }

    // MARK: - Private Methods

    /**
     * 格式化日期显示
     */
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale.current
        return formatter.string(from: date)
    }
}

/**
 * 成员表单字段组件
 */
struct MemberFormField: View {

    let title: String
    @Binding var text: String
    let placeholder: String

    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            TextField(placeholder, text: $text)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
}

/**
 * 成员角色选择视图
 */
struct MemberRoleSelectionView: View {

    @Binding var selectedRole: String
    @Binding var isPresented: Bool

    private let roles = ["son", "daughter", "father", "mother", "other"]

    var body: some View {
        ZStack {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isPresented = false
                        }
                    }

                // 选择菜单
                VStack(spacing: 0) {
                    // 标题
                    HStack {
                        Text("add_member.form.role".localized)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        Spacer()

                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)

                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)

                    // 角色选项列表
                    ScrollView {
                        VStack(spacing: 0) {
                            ForEach(roles, id: \.self) { role in
                                MemberRoleOptionRow(
                                    role: role,
                                    isSelected: selectedRole == role,
                                    onTap: {
                                        selectedRole = role
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            isPresented = false
                                        }
                                    }
                                )
                            }
                        }
                    }
                    .frame(maxHeight: 300)
                }
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
                .padding(.horizontal, 40)
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isPresented)
    }
}

/**
 * 成员角色选项行组件
 */
struct MemberRoleOptionRow: View {

    let role: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack {
                Text("member.role.\(role)".localized)
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18))
                        .foregroundColor(Color(hex: "#a9d051"))
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(isSelected ? Color(hex: "#f8fff2") : Color.clear)
        }
        .buttonStyle(PlainButtonStyle())

        // 分隔线
        if role != roles.last {
            Rectangle()
                .fill(Color(hex: "#f0f0f0"))
                .frame(height: 0.5)
                .padding(.horizontal, 20)
        }
    }

    private let roles = ["son", "daughter", "father", "mother", "other"]
}
