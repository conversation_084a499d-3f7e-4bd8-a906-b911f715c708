//
//  StudentCardView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 学生卡片组件
 */
struct StudentCardView: View {
    
    let student: Student
    let isDeleteMode: Bool
    let onLongPress: () -> Void
    let onDeleteTapped: () -> Void
    let onCardTapped: () -> Void
    
    @State private var isPressed = false
    @State private var animationTrigger = false
    @State private var isLongPressJustEnded = false
    @State private var cardAppeared = false
    
    var body: some View {
        Button(action: {
            // 如果刚结束长按，忽略此次点击
            if isLongPressJustEnded {
                return
            }
            
            if isDeleteMode {
                onCardTapped()
            } else {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isPressed = true
                    animationTrigger.toggle()
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isPressed = false
                    onCardTapped()
                }
            }
        }) {
            GeometryReader { geometry in
                let breakpoint = ResponsiveBreakpoints.currentBreakpoint(for: geometry)
                let config = ResponsiveBreakpoints.configForBreakpoint(breakpoint)
                let calculator = LayoutCalculator(geometry: geometry, config: config)
                
                ZStack {
                    // 美化背景层
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white,
                                    Color(hex: "#f8fdf0").opacity(0.8)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(
                            color: Color(hex: "#B5E36B").opacity(isPressed ? 0.4 : 0.15),
                            radius: isPressed ? 12 : 6,
                            x: 0,
                            y: isPressed ? 6 : 3
                        )
                        .overlay(
                                                    RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#B5E36B").opacity(0.3),
                                        Color(hex: "#FFE49E").opacity(0.2)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: isPressed ? 2 : 1
                            )
                        )
                    
                    // 装饰性背景元素
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.06))
                        .frame(width: 40, height: 40)
                        .offset(x: 35, y: -15)
                    
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.08))
                        .frame(width: 30, height: 30)
                        .offset(x: -25, y: 20)
                    
                    // 背景水印层（美化版）
                    EnhancedWatermarkLayer(
                        text: student.studentNumber ?? "000",
                        position: calculator.watermarkPosition,
                        fontSize: calculator.watermarkStyle.fontSize ?? 80,
                        fontWeight: .black,
                        opacity: 0.15,
                        color: Color(hex: "#B5E36B")
                    )
                    
                    // 头像层（美化版）
                    EnhancedAvatarLayer(
                        imageName: student.avatarImageName,
                        position: calculator.avatarPosition,
                        size: calculator.avatarSize.width,
                        isPressed: isPressed
                    )
                    
                    // 姓名层（美化版）
                    EnhancedNameLayer(
                        text: student.name ?? "未知",
                        position: calculator.namePosition,
                        fontSize: calculator.nameStyle.fontSize ?? 16,
                        fontWeight: .semibold,
                        color: Color(hex: "#333333"),
                        maxLines: calculator.nameConstraints.maxLines ?? 1,
                        minimumScaleFactor: 0.8
                    )
                    
                    // 积分层（美化版）
                    EnhancedScoreLayer(
                        score: Int(student.point),
                        position: calculator.scorePosition,
                        fontSize: calculator.scoreStyle.fontSize ?? 18,
                        fontWeight: .bold,
                        color: Color(hex: "#87C441"),
                        animationTrigger: animationTrigger
                    )
                    
                    // 删除按钮
                    if isDeleteMode {
                        Button(action: onDeleteTapped) {
                            ZStack {
                                Circle()
                                    .fill(DesignSystem.DeleteMode.deleteButtonBackground)
                                    .frame(
                                        width: DesignSystem.DeleteMode.deleteButtonSize,
                                        height: DesignSystem.DeleteMode.deleteButtonSize
                                    )
                                    .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                                
                                Image(systemName: "xmark")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(DesignSystem.DeleteMode.deleteButtonIcon)
                            }
                        }
                        .offset(
                            x: DesignSystem.DeleteMode.deleteButtonOffset.x,
                            y: DesignSystem.DeleteMode.deleteButtonOffset.y
                        )
                        .scaleEffect(isDeleteMode ? 1.0 : 0.1)
                        .animation(.spring(response: 0.25, dampingFraction: 0.8), value: isDeleteMode)
                        .zIndex(10)
                    }
                    
                    // 按压闪烁效果
                    if isPressed {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.white.opacity(0.5))
                            .transition(.opacity)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(height: DesignSystem.StudentCard.height)
        .padding(DesignSystem.StudentCard.padding)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .simultaneousGesture(
            LongPressGesture(minimumDuration: DesignSystem.DeleteMode.longPressMinimumDuration)
                .onEnded { _ in
                    if !isDeleteMode {
                        isLongPressJustEnded = true
                        onLongPress()
                        
                        // 设置冷却期，防止立即触发点击事件
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                            isLongPressJustEnded = false
                        }
                    }
                }
        )

        // 入场动画效果
        .opacity(cardAppeared ? 1.0 : 0.0)
        .offset(y: cardAppeared ? 0 : 50)
        .scaleEffect(cardAppeared ? 1.0 : 0.7)
        .animation(
            .spring(response: 0.8, dampingFraction: 0.7)
            .delay(Double.random(in: 0...0.3)),
            value: cardAppeared
        )
        .onAppear {
            // 触发入场动画
            if !isDeleteMode {
                // 确保动画立即触发
                DispatchQueue.main.async {
                    withAnimation {
                        cardAppeared = true
                    }
                }
                
                // 同时触发内部元素动画，减少延迟
                DispatchQueue.main.asyncAfter(deadline: .now() + Double.random(in: 0.1...0.3)) {
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        animationTrigger.toggle()
                    }
                }
            }
        }
    }
}

// MARK: - Enhanced Components

/**
 * 增强版头像层
 */
private struct EnhancedAvatarLayer: View {
    let imageName: String
    let position: CGPoint
    let size: CGFloat
    let isPressed: Bool
    
    var body: some View {
        ZStack {
            // 头像背景光圈
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#B5E36B").opacity(0.15),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: size/2 + 5
                    )
                )
                .frame(width: size + 10, height: size + 10)
                .scaleEffect(isPressed ? 1.1 : 1.0)
            
            // 头像图片
            Image(imageName)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: size, height: size)
                .clipShape(Circle())
                .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
        }
        .position(x: position.x, y: position.y)
        .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isPressed)
    }
}

/**
 * 增强版姓名层
 */
private struct EnhancedNameLayer: View {
    let text: String
    let position: CGPoint
    let fontSize: CGFloat
    let fontWeight: Font.Weight
    let color: Color
    let maxLines: Int
    let minimumScaleFactor: CGFloat
    
    var body: some View {
        Text(text)
            .font(.system(size: fontSize, weight: fontWeight))
            .foregroundColor(color)
            .shadow(color: Color.white.opacity(0.8), radius: 1, x: 0, y: 1)
            .lineLimit(maxLines)
            .minimumScaleFactor(minimumScaleFactor)
            .position(x: position.x, y: position.y)
    }
}

/**
 * 增强版积分层
 */
private struct EnhancedScoreLayer: View {
    let score: Int
    let position: CGPoint
    let fontSize: CGFloat
    let fontWeight: Font.Weight
    let color: Color
    let animationTrigger: Bool

    /**
     * 根据分数大小计算调整后的位置
     */
    private var adjustedPosition: CGPoint {
        let scoreText = "\(score)"
        let digitCount = scoreText.count

        // 根据数字位数调整X坐标偏移量
        let offsetX: CGFloat
        switch digitCount {
        case 1:
            offsetX = 0 // 单位数不偏移
        case 2:
            offsetX = -8 // 两位数向左偏移8点
        case 3:
            offsetX = -16 // 三位数向左偏移16点
        case 4:
            offsetX = -24 // 四位数向左偏移24点
        default:
            offsetX = -32 // 五位数及以上向左偏移32点
        }

        return CGPoint(x: position.x + offsetX, y: position.y)
    }

    var body: some View {
        ZStack {
            // 积分数字
            Text("\(score)")
                .font(.system(size: fontSize + 1, weight: fontWeight))
                .foregroundColor(color)
                .shadow(color: color.opacity(0.3), radius: 2, x: 0, y: 1)
                .scaleEffect(animationTrigger ? 1.05 : 1.0)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: animationTrigger)
        }
        .position(x: adjustedPosition.x, y: adjustedPosition.y)
    }
}

/**
 * 增强版水印层
 */
private struct EnhancedWatermarkLayer: View {
    let text: String
    let position: CGPoint
    let fontSize: CGFloat
    let fontWeight: Font.Weight
    let opacity: Double
    let color: Color
    
    var body: some View {
        Text(text)
            .font(.system(size: fontSize, weight: fontWeight))
            .foregroundColor(color.opacity(opacity))
            .position(x: position.x, y: position.y)
    }
}

// MARK: - Preview
#Preview {
    return VStack {
        HStack {
            Text("学生卡片预览")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.gray)
            
            Spacer()
        }
        Spacer()
    }
    .padding()
    .background(
        LinearGradient(
            gradient: Gradient(colors: [DesignSystem.Colors.background, Color.white]),
            startPoint: .top,
            endPoint: .bottom
        )
    )
} 