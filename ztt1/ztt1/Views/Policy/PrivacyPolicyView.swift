//
//  PrivacyPolicyView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 隐私政策页面
 */
struct PrivacyPolicyView: View {
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text(privacyPolicyContent)
                        .font(.system(size: 14))
                        .lineSpacing(4)
                        .foregroundColor(.primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                }
            }
            .navigationTitle("隐私政策")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private let privacyPolicyContent = """
《团团转》隐私政策

发布/更新日期：2025年7月20日
生效日期：2025年7月20日

     引言： 我们非常重视您的个人信息和学生数据的安全。本《隐私政策》（以下简称"本政策"）旨在帮助您了解我们在您使用《团团转》应用（以下简称"本应用"）时如何收集、使用、存储、共享和保护您的信息，以及您享有哪些权利。
                
                《团团转》是一款专为幼儿园和小学教师设计的班级管理工具。我们理解教育数据的敏感性，因此采取了多项措施保护您和学生的信息安全。我们承诺遵守中华人民共和国《网络安全法》、《数据安全法》、《个人信息保护法》等相关法律法规的要求。
                
                请您在使用本应用前仔细阅读并理解本政策。如您有任何疑问，可通过本政策末尾提供的联系方式与我们联系。
                
一、我们收集的信息
                1.1 账户信息
                • 您通过Apple ID登录时，我们会收集Apple提供的有限信息，包括：
                   - Apple ID关联的电子邮箱地址
                   - Apple ID的匿名标识符
                   - 您的显示姓名（如您已授权）
                • 我们不会获取您的Apple ID密码或其他Apple账户详情
                
                1.2 使用数据
                • 您输入的学生姓名、学号、性别、行为记录等信息
                • 设备信息：设备型号、系统版本、应用版本，用于优化应用性能和解决兼容性问题
                • 错误报告：当应用发生崩溃或错误时，我们可能会收集相关日志信息（不包含个人身份信息）
                
                1.3 我们不收集的信息
                • 我们不收集或访问您设备上的联系人、相册、定位、摄像头、麦克风等信息
                • 我们不收集您的支付信息（所有支付交易由Apple处理）
                • 我们不收集或存储您在应用中创建的任何个人内容，包括学生信息、班级信息、积分记录、奖品设置、生成的分析报告等
                
二、信息的使用方式
                2.1 提供核心功能
                • 实现班级管理、学生管理、积分记录等基本功能
                • 处理您的订阅请求，提供相应的会员功能
                • 通过 CloudKit 实现多设备数据同步
                
                2.2 改进服务质量
                • 分析应用使用情况，优化用户界面和功能设计
                • 诊断和修复技术问题
                • 开发新功能以满足教师用户需求
                
                2.3 AI分析报告生成
                • 在您请求时，我们会将学生积分记录发送至DeepSeek API生成行为分析报告
                • 此过程中，我们会进行严格的数据脱敏处理：
                   - 仅传输时间、原因、分值等非身份信息
                   - 不会上传学生姓名、学号等可识别个人身份的信息
                   - 分析结果仅在本地设备显示，不会在云端存储
                
                2.4 我们不会进行的行为
                • 不会将您的数据用于营销、广告或推送目的
                • 不会向任何第三方出售或出租您的个人信息
                • 不会将学生信息用于教育管理以外的目的
                • 不会在未经您明确许可的情况下访问您的iCloud数据
                
三、信息的存储与同步机制
                3.1 本地存储
                • 您的数据主要存储在设备本地，使用CoreData进行安全存储
                • 本地数据使用系统安全机制进行保护，其他应用无法访问
                • 当您卸载应用时，部分本地数据可能会被保留，以便您重新安装后恢复使用
                
                3.2 iCloud同步
                • 您的数据会通过Apple的CloudKit服务同步到您自己的iCloud账户
                • 同步数据受到Apple的加密保护，我们无法访问您在iCloud中的数据
                • 您可以在任何登录相同Apple ID的设备上访问您的数据
                
                3.3 数据保留期限
                • 您的数据将保留至您主动删除账户或数据
                • 如果您选择删除账户，所有数据（本地和云端）将被永久删除
                • 我们不会在服务器上额外存储您的个人数据副本
                
                3.4 数据备份建议
                • 我们建议您定期通过iCloud或iTunes备份设备数据
                • 重要数据（如学生学期表现记录）和生成的AI分析报告可考虑导出保存
                
四、第三方服务说明
                本应用集成了以下第三方服务，您在使用本应用时可能会受到这些第三方的隐私政策影响：
                
                4.1 Apple服务
                • Apple ID：用于用户身份认证
                • CloudKit：提供数据同步服务
                • StoreKit：处理应用内购买和订阅
                这些服务受Apple隐私政策约束，详情请访问：https://www.apple.com/legal/privacy/
                
                4.2 RevenueCat
                • 用途：管理订阅状态和订阅周期
                • 收集信息：设备标识符、订阅状态（不包含支付详情）
                • 隐私政策：https://www.revenuecat.com/privacy/
                
                4.3 DeepSeek API
                • 用途：仅在您请求时生成学生行为分析报告
                • 收集信息：脱敏后的积分记录数据（不含个人身份信息）
                • 数据处理：通过HTTPS加密传输，请求完成后不保留数据
                • 隐私政策：https://www.deepseek.com/privacy-policy
                
                我们谨慎选择第三方服务提供商，并要求他们提供适当的隐私和安全保障。但请注意，这些第三方的做法不在我们的控制范围内，我们建议您同时查阅这些第三方的隐私政策。
                
五、用户的数据控制权
                您对自己的数据拥有充分的控制权，包括：
                
                5.1 访问与查看
                • 您可以随时在应用内查看您创建的所有数据
                • 您可以查看所有班级、学生和历史记录信息
                
                5.2 修改与更新
                • 您可以随时修改班级信息、学生信息和规则设置
                • 您可以更正积分记录（通过撤销功能）
                
                5.3 删除数据
                • 删除单个内容：您可以删除单个学生、班级或记录
                • 部分删除：您可以选择性地删除部分历史数据
                • 完全删除：您可以通过"删除账号"功能删除所有数据
                
                5.4 数据删除流程
                当您选择删除账号时，我们将执行以下流程：
                • 删除本地数据：清除设备上的所有CoreData存储
                • 删除云端数据：删除与您账号关联的所有CloudKit数据
                • 清除认证信息：移除所有登录状态和凭证
                • 完成删除：永久移除所有用户关联数据
                
                数据删除是不可逆的操作，删除后无法恢复，请在操作前确认。
                
六、信息安全保障措施
                我们采取多重措施保护您的信息安全：
                
                6.1 数据加密
                • 传输加密：所有网络通信使用HTTPS协议加密传输
                • 存储加密：本地数据和iCloud同步数据都经过加密存储
                
                6.2 访问控制
                • 身份认证：使用Apple ID安全认证机制
                • 访问限制：您的数据仅限您本人访问，开发团队无法访问用户数据
                
                6.3 应用安全
                • 代码审查：应用代码经过安全审查，避免常见安全漏洞
                • 定期更新：我们会定期更新应用以修复潜在的安全问题
                
                6.4 第三方审核
                • App Store审核：我们的应用通过Apple App Store的安全审核
                • API安全：所有第三方API调用均采用安全的认证机制
                
                6.5 数据隔离
                • 用户数据相互隔离，不同用户之间无法访问彼此的数据
                • 开发环境与生产环境严格分离
                
七、面向儿童的数据声明
                本应用专为教育工作者（教师）设计，不直接面向儿童用户。关于学生数据，我们特别声明：
                
                7.1 学生数据收集
                • 所有学生数据均由教师用户输入，我们不直接从儿童处收集信息
                • 学生数据仅限于基础身份标识（姓名、学号、性别）和教育记录（积分、奖励），不包含敏感个人信息
                
                7.2 教育目的限制
                • 学生数据仅用于教育管理目的
                • 不会将学生数据用于商业营销、广告或其他非教育目的
                
                7.3 教师责任
                • 教师用户应确保：
                   - 有合法权限收集和使用学生信息
                   - 在必要范围内最小化收集学生信息
                   - 遵守相关教育和儿童保护法规
                   - 妥善保管设备和账户，防止未授权访问学生数据
                
                7.4 数据可移植性
                • 教师可导出学生数据，确保学期结束后数据安全转移或归档

                7.5 专门政策
                 • 关于儿童个人信息保护的详细规定，请参阅我们的《儿童个人隐私保护政策》
                
八、隐私政策变更通知机制
                我们可能会不时更新本隐私政策，以反映法律法规变化或应用功能调整。当发生重大变更时：
                
                8.1 通知方式
                • 应用内通知：通过弹窗或消息中心通知
                • 显著提示：在应用启动时显示政策更新提醒
                • 电子邮件：可能向您的注册邮箱发送通知（如有重大变更）
                
                8.2 更新记录
                • 每次更新政策时，我们会在顶部更新"最后更新日期"
                • 重大变更将附带变更摘要，帮助您理解主要修改内容
                
                8.3 用户选择
                • 当隐私政策发生实质性变更时，您可能需要重新确认同意
                • 如您不同意更新后的政策，可能会影响您使用部分或全部功能
                
                8.4 历史版本
                • 我们会保留隐私政策的历史版本供您查阅
                • 您可通过本政策末尾的联系方式请求查看历史版本
                
九、联系我们
                如果您对本隐私政策有任何疑问、建议或投诉，请通过以下方式联系我们：
                
                • 电子邮箱：<EMAIL>
                • 应用内反馈：设置 > 帮助与反馈
                
                我们会在收到您的请求后15个工作日内回复。如果您对我们的回复不满意，您还可以：
                
                • 向您所在地区的数据保护监管机构投诉
                • 通过法律途径寻求解决方案
                
                感谢您对《团团转》应用的信任和支持，我们将持续努力保护您的信息安全。
                
本隐私政策的最终解释权归开发团队所有。
"""
}

// MARK: - Preview
#Preview {
    PrivacyPolicyView()
}