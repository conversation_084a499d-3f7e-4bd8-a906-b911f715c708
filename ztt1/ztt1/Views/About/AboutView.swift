//
//  AboutView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 关于页面
 */
struct AboutView: View {
    
    @Environment(\.dismiss) private var dismiss
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var showChildrenPrivacyPolicy = false
    @State private var pageAppeared = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变 - 与语言切换页面一致
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#f8fdf0"),
                        Color(hex: "#f0f8e0")
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // 装饰性背景元素
                GeometryReader { geometry in
                    // 左上角装饰圆
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#a9d051").opacity(0.1),
                                    Color(hex: "#8bb83f").opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .position(x: -20, y: 50)
                    
                    // 右下角装饰圆
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#8bb83f").opacity(0.08),
                                    Color(hex: "#a9d051").opacity(0.03)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 150, height: 150)
                        .position(x: geometry.size.width + 30, y: geometry.size.height - 50)
                }
                
                VStack(spacing: DesignSystem.Spacing.xl) {
                    // 标题区域
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        Text("")
                            .font(.system(
                                size: DesignSystem.Typography.HeadingLarge.fontSize,
                                weight: DesignSystem.Typography.HeadingLarge.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : -20)
                            .animation(.easeOut(duration: 0.6).delay(0.1), value: pageAppeared)
                        
                        Text("")
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : -10)
                            .animation(.easeOut(duration: 0.6).delay(0.3), value: pageAppeared)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    
                    // 政策选项列表
                    VStack(spacing: DesignSystem.Spacing.md) {
                        AboutItemView(
                            title: "settings.item.user_agreement".localized,
                            iconName: "doc.text",
                            isFirst: true
                        ) {
                            showUserAgreement = true
                        }
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 30)
                        .animation(.easeOut(duration: 0.6).delay(0.5), value: pageAppeared)
                        
                        AboutItemView(
                            title: "settings.item.privacy_policy".localized,
                            iconName: "hand.raised"
                        ) {
                            showPrivacyPolicy = true
                        }
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 30)
                        .animation(.easeOut(duration: 0.6).delay(0.6), value: pageAppeared)
                        
                        AboutItemView(
                            title: "settings.item.children_privacy_policy".localized,
                            iconName: "figure.and.child.holdinghands"
                        ) {
                            showChildrenPrivacyPolicy = true
                        }
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 30)
                        .animation(.easeOut(duration: 0.6).delay(0.7), value: pageAppeared)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    
                    Spacer()
                    
                    // 版本信息
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        Text("版本信息")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)

                        Text("v\(appVersion)")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    }
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.8), value: pageAppeared)

                    // 备案号链接
                    Button(action: {
                        if let url = URL(string: "https://beian.miit.gov.cn/#/Integrated/recordQuery") {
                            UIApplication.shared.open(url)
                        }
                    }) {
                        Text("粤ICP备2025394023号-6A")
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.9), value: pageAppeared)
                    .padding(.bottom, DesignSystem.Spacing.lg)
                }
                .padding(.top, DesignSystem.Spacing.lg)
            }
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    }
                }
            }
        }
        .onAppear {
            withAnimation {
                pageAppeared = true
            }
        }
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showChildrenPrivacyPolicy) {
            ChildrenPrivacyPolicyView()
        }
    }
    
    // 获取应用版本号
    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    // 获取构建号
    private var buildNumber: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
}

/**
 * 关于页面选项视图
 */
struct AboutItemView: View {
    let title: String
    let iconName: String
    let isFirst: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    init(title: String, iconName: String, isFirst: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.iconName = iconName
        self.isFirst = isFirst
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = true
                action()
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isPressed = false
            }
        }) {
            ZStack {
                // 背景容器
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(hex: "#f6fbe9"))
                    .frame(height: 60)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.clear, lineWidth: 2)
                    )
                    .shadow(color: Color.black.opacity(0.04), radius: 4, x: 0, y: 2)
                
                HStack(spacing: DesignSystem.Spacing.md) {
                    // 图标
                    ZStack {
                        Circle()
                            .fill(Color(hex: "#a9d051"))
                            .frame(width: 32, height: 32)
                        
                        Image(systemName: iconName)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    }
                    
                    // 标题
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    // 箭头
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.horizontal, DesignSystem.Spacing.md)
                
                // 按压效果
                if isPressed {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.white.opacity(0.3))
                        .frame(height: 60)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    AboutView()
}