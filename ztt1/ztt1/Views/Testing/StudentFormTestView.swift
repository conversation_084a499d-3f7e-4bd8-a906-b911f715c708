//
//  StudentFormTestView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/7/26.
//

import SwiftUI

/**
 * 学生表单测试视图
 * 用于测试新增的角色选择和出生日期功能
 */
struct StudentFormTestView: View {
    
    @State private var showForm = false
    @State private var students: [StudentFormData] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("学生表单测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Button("显示添加学生表单") {
                    showForm = true
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                
                if !students.isEmpty {
                    Text("已添加的学生:")
                        .font(.headline)
                    
                    ScrollView {
                        VStack(alignment: .leading, spacing: 10) {
                            ForEach(students, id: \.id) { student in
                                VStack(alignment: .leading, spacing: 5) {
                                    Text("姓名: \(student.name)")
                                    Text("学号: \(student.studentNumber)")
                                    Text("性别: \(student.gender == "male" ? "男" : "女")")
                                    Text("角色: \(StudentFormData.roleDisplayText(for: student.role))")
                                    Text("出生日期: \(formatDate(student.birthDate))")
                                    Text("年龄: \(student.age)岁")
                                    Text("初始积分: \(student.initialPointsValue)")
                                }
                                .padding()
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(8)
                            }
                        }
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("表单测试")
        }
        .overlay(
            ManualAddStudentView(
                isPresented: $showForm,
                onSubmit: { studentForms in
                    students.append(contentsOf: studentForms)
                    showForm = false
                },
                onCancel: {
                    showForm = false
                }
            )
        )
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale.current
        return formatter.string(from: date)
    }
}

#Preview {
    StudentFormTestView()
}
