//
//  ReportContentView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * 报告内容展示组件
 * 负责显示AI生成的学生行为分析报告详细内容
 */
struct ReportContentView: View {
    
    // MARK: - Properties
    let report: AIAnalysisReport
    let onCopy: () -> Void
    
    // MARK: - State
    @State private var contentAppeared = false
    
    // MARK: - Body
    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 顶部占位，为导航栏留空间
                Color.clear
                    .frame(height: 80)
                
                VStack(spacing: 24) {
                    // 报告头部信息
                    reportHeaderSection
                    
                    // 报告主要内容
                    reportMainContent
                    
                    // 操作按钮
                    actionButtons
                    
                    // 底部间距
                    Color.clear
                        .frame(height: 40)
                }
                .padding(.horizontal, 20)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8).delay(0.2)) {
                contentAppeared = true
            }
        }
    }
    
    // MARK: - Subviews
    
    /**
     * 报告头部信息
     */
    private var reportHeaderSection: some View {
        VStack(spacing: 16) {
            // 报告标题
            HStack {
                Image(systemName: report.reportType == .professional ? "brain.head.profile" : "message")
                    .font(.system(size: 24))
                    .foregroundColor(Color(hex: "#74c07f"))
                
                Text(report.reportTitle)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
            }
            
            // 基本统计信息
            statisticsInfoCard
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 20)
        .animation(.easeOut(duration: 0.6).delay(0.1), value: contentAppeared)
    }
    
    /**
     * 统计信息卡片
     */
    private var statisticsInfoCard: some View {
        VStack(spacing: 12) {
            // 学生基本信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("ai_analysis.student_info".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text(report.studentName)
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                Spacer()
                
                // 生成时间
                VStack(alignment: .trailing, spacing: 4) {
                    Text("ai_analysis.generated_time".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text(report.formattedGeneratedTime)
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
            
            Divider()
                .background(Color(hex: "#E8F5E8"))
            
            // 记录统计
            HStack(spacing: 20) {
                statisticItem(
                    title: "ai_analysis.total_records".localized,
                    value: "\(report.totalRecords)",
                    color: Color(hex: "#74c07f")
                )
                
                statisticItem(
                    title: "ai_analysis.positive_records".localized,
                    value: "\(report.positiveRecords)",
                    color: Color(hex: "#26C34B")
                )
                
                statisticItem(
                    title: "ai_analysis.negative_records".localized,
                    value: "\(report.negativeRecords)",
                    color: Color(hex: "#FF5B5B")
                )
            }
            
            // 积极行为比例
            HStack {
                Text("ai_analysis.positive_ratio".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Spacer()
                
                Text(report.formattedPositiveRatio)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color(hex: "#26C34B"))
            }
        }
        .padding(20)
        .background(Color(hex: "#F8FDF0"))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(hex: "#E8F5E8"), lineWidth: 1)
        )
    }
    
    /**
     * 统计项目
     */
    private func statisticItem(title: String, value: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(color)
            
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
    }
    
    /**
     * 报告主要内容
     */
    private var reportMainContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 分析报告标题
            HStack {
                Image(systemName: report.reportType == .professional ? "doc.text" : "bubble.left.and.bubble.right")
                    .font(.system(size: 18))
                    .foregroundColor(Color(hex: "#74c07f"))
                
                Text(report.reportType == .professional ? "ai_analysis.professional_analysis".localized : "ai_analysis.feedback_content".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
            }
            
            // AI分析内容
            analysisContentView
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: contentAppeared)
    }
    
    /**
     * 分析内容视图
     */
    private var analysisContentView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 根据报告类型展示不同样式
            if report.reportType == .parentFeedback {
                // 家长反馈样式，移除老师头像，直接显示内容
                Text(report.analysisContent)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineSpacing(6)
                    .fixedSize(horizontal: false, vertical: true)
                    .padding(16)
                    .background(Color.white)
                    .cornerRadius(16)
            } else {
                // 专业分析报告样式
                Text(report.analysisContent)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineSpacing(6)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(hex: "#E8F5E8"), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
    
    /**
     * 操作按钮区域
     */
    private var actionButtons: some View {
        VStack(spacing: 16) {
            // 复制报告按钮
            copyButton
            
            // 添加提示文本
            Text("ai_analysis.save_tip".localized)
                .font(.system(size: 12))
                .foregroundColor(DesignSystem.Colors.textTertiary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 10)
                .padding(.top, 4)
            
            // AI生成内容仅供参考提示
            Text("AI生成内容仅供参考")
                .font(.system(size: 11))
                .foregroundColor(DesignSystem.Colors.textTertiary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 10)
                .padding(.top, 2)
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 20)
        .animation(.easeOut(duration: 0.6).delay(0.5), value: contentAppeared)
    }
    
    private var copyButton: some View {
        Button(action: {
            onCopy()
        }) {
            HStack(spacing: 8) {
                Image(systemName: "doc.on.doc")
                    .font(.system(size: 14))
                    .foregroundColor(.white)
                
                Text("ai_analysis.copy_report".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#74c07f"),
                        Color(hex: "#5da961")
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(16)
            .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 5, x: 0, y: 3)
        }
    }
} 