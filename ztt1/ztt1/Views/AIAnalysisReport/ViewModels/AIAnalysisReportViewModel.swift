//
//  AIAnalysisReportViewModel.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation
import SwiftUI

/**
 * AI分析报告视图模型
 * 负责管理AI分析报告的业务逻辑、状态管理和用户交互
 */
@MainActor
class AIAnalysisReportViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var report: AIAnalysisReport?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var showError: Bool = false
    @Published var canGenerateReport: Bool = false
    @Published var permissionResult: AIAnalysisPermissionResult?
    @Published var selectedReportType: ReportType = .professional
    
    // MARK: - Dependencies
    private let aiService: AIAnalysisService
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - Private Properties
    private var currentStudent: Student?
    private var currentUser: User?
    
    // MARK: - Initialization
    
    init(aiService: AIAnalysisService? = nil) {
        self.aiService = aiService ?? AIAnalysisService()
        self.currentUser = coreDataManager.getCurrentUser()
    }
    
    // MARK: - Public Methods
    
    /**
     * 设置学生并检查权限
     */
    func setupStudent(_ student: Student) {
        self.currentStudent = student
        _ = checkPermissions()
    }
    
    /**
     * 生成AI分析报告
     */
    func generateReport() async {
        guard let student = currentStudent else {
            showErrorMessage("ai_analysis.error.no_student".localized)
            return
        }
        
        // 防止重复调用
        if isLoading {
            print("⚠️ 忽略重复的生成请求")
            return
        }
        
        // 重新检查权限
        let permission = aiService.validatePermissions(for: currentUser, student: student)
        guard permission.hasPermission else {
            showErrorMessage(permission.reason ?? "ai_analysis.error.permission_unknown".localized)
            return
        }
        
        print("🚀 开始生成AI分析报告 - 学生: \(student.name ?? "未知"), 报告类型: \(selectedReportType)")
        
        isLoading = true
        errorMessage = nil
        showError = false
        
        do {
            // 准备脱敏数据
            guard let analysisData = aiService.prepareAnalysisData(from: student) else {
                throw AIAnalysisError.insufficientData(recordCount: student.sortedPointRecords.filter { !$0.isReversed }.count)
            }
            
            print("📊 准备分析数据完成，记录数: \(analysisData.pointRecords.count)")
            
            // 调用AI服务生成报告，传入选定的报告类型
            let generatedReport = try await aiService.generateAnalysisReport(data: analysisData, reportType: selectedReportType)
            
            // 更新UI
            self.report = generatedReport
            
            print("✅ AI分析报告生成成功")
            
            // 触觉反馈
            let feedbackGenerator = UINotificationFeedbackGenerator()
            feedbackGenerator.notificationOccurred(.success)
            
        } catch let error as AIAnalysisError {
            print("❌ AI分析错误: \(error.errorDescription ?? "未知错误")")
            handleAIAnalysisError(error)
            
            // 错误触觉反馈
            let feedbackGenerator = UINotificationFeedbackGenerator()
            feedbackGenerator.notificationOccurred(.error)
            
        } catch {
            print("❌ 未预期错误: \(error.localizedDescription)")
            handleGenericError(error)
            
            // 错误触觉反馈
            let feedbackGenerator = UINotificationFeedbackGenerator()
            feedbackGenerator.notificationOccurred(.error)
        }
        
        isLoading = false
    }
    
    /**
     * 复制报告到剪贴板
     */
    func copyReportToClipboard() {
        guard let report = report else {
            showErrorMessage("ai_analysis.error.no_report".localized)
            return
        }
        
        let pasteboard = UIPasteboard.general
        pasteboard.string = report.fullReportText
        
        // 显示成功提示
        showSuccessMessage("ai_analysis.copy_success".localized)
        
        // 触觉反馈
        let feedbackGenerator = UINotificationFeedbackGenerator()
        feedbackGenerator.notificationOccurred(.success)
        
        print("📋 报告已复制到剪贴板")
    }
    
    /**
     * 检查权限状态
     */
    func checkPermissions() -> AIAnalysisPermissionResult {
        guard let student = currentStudent else {
            let result = AIAnalysisPermissionResult.denied(reason: "ai_analysis.error.no_student".localized)
            permissionResult = result
            canGenerateReport = false
            return result
        }
        
        let result = aiService.validatePermissions(for: currentUser, student: student)
        permissionResult = result
        canGenerateReport = result.hasPermission
        
        if !result.hasPermission {
            errorMessage = result.reason
        }
        
        return result
    }
    
    /**
     * 刷新权限状态（用户升级会员后调用）
     */
    func refreshPermissions() {
        currentUser = coreDataManager.getCurrentUser()
        _ = checkPermissions()
    }
    
    /**
     * 清除当前报告
     */
    func clearReport() {
        report = nil
        errorMessage = nil
        showError = false
    }
    
    /**
     * 设置报告类型
     */
    func setReportType(_ type: ReportType) {
        selectedReportType = type
        print("📋 切换报告类型为: \(type)")
    }
    
    /**
     * 获取报告类型显示名称
     */
    func getReportTypeName(_ type: ReportType) -> String {
        switch type {
        case .professional:
            return "ai_analysis.report_type.professional".localized
        case .parentFeedback:
            return "ai_analysis.report_type.parent_feedback".localized
        }
    }
    
    /**
     * 重新生成报告，可以指定新的报告类型
     */
    func regenerateReport(withType type: ReportType? = nil) async {
        if let type = type {
            selectedReportType = type
        }
        clearReport()
        await generateReport()
    }
    
    // MARK: - Private Methods
    
    /**
     * 处理AI分析错误
     */
    private func handleAIAnalysisError(_ error: AIAnalysisError) {
        let errorDescription = error.errorDescription ?? "ai_analysis.error.unknown".localized
        showErrorMessage(errorDescription)
        
        print("❌ AI分析错误: \(errorDescription)")
    }
    
    /**
     * 处理通用错误
     */
    private func handleGenericError(_ error: Error) {
        let errorDescription = "ai_analysis.error.generic".localized
        showErrorMessage(errorDescription)
        
        print("❌ 通用错误: \(error.localizedDescription)")
    }
    
    /**
     * 显示错误信息
     */
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    /**
     * 显示成功信息
     */
    private func showSuccessMessage(_ message: String) {
        // 可以实现临时成功提示
        // 这里暂时使用print，后续可以添加Toast或其他UI提示
        print("✅ \(message)")
    }
    
    /**
     * 获取权限状态描述
     */
    func getPermissionStatusDescription() -> String {
        guard let result = permissionResult else {
            return "ai_analysis.status.checking".localized
        }
        
        if result.hasPermission {
            return "ai_analysis.status.ready".localized
        } else {
            return result.reason ?? "ai_analysis.status.unknown".localized
        }
    }
    
    /**
     * 是否可以显示升级按钮
     */
    var canShowUpgradeButton: Bool {
        return permissionResult?.canUpgrade == true
    }
    
    /**
     * 获取学生统计信息
     */
    func getStudentStatistics() -> (recordCount: Int, positiveCount: Int, negativeCount: Int)? {
        guard let student = currentStudent else { return nil }
        
        let validRecords = student.sortedPointRecords.filter { !$0.isReversed }
        let positiveRecords = validRecords.filter { $0.value > 0 }
        let negativeRecords = validRecords.filter { $0.value < 0 }
        
        return (
            recordCount: validRecords.count,
            positiveCount: positiveRecords.count,
            negativeCount: negativeRecords.count
        )
    }
    
    /**
     * 获取网络状态
     */
    var isNetworkAvailable: Bool {
        return aiService.isNetworkAvailable
    }
}