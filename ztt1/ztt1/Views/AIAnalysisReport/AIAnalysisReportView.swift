//
//  AIAnalysisReportView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import SwiftUI

/**
 * AI分析报告主视图
 * 负责展示学生的AI行为分析报告，包含权限检查、加载状态和报告内容
 */
struct AIAnalysisReportView: View {
    
    // MARK: - Properties
    let student: Student
    let onDismiss: () -> Void
    let onNavigateToSubscription: (() -> Void)?
    
    // MARK: - State
    @StateObject private var viewModel = AIAnalysisReportViewModel()
    @State private var pageAppeared = false
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                createBackgroundGradient()
                
                // 主要内容
                if viewModel.canGenerateReport {
                    if let report = viewModel.report {
                        // 显示报告内容
                        reportContentView(report: report)
                    } else if viewModel.isLoading {
                        // 加载状态
                        loadingView
                    } else {
                        // 生成报告按钮
                        generateReportView
                    }
                } else {
                    // 权限不足视图
                    permissionDeniedView
                }
                
                // 顶部导航栏
                topNavigationBar
            }
        }
        .navigationBarHidden(true)
                .onAppear {
            setupView()
        }
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定") {
                viewModel.showError = false
            }
        } message: {
            Text(viewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - Subviews
    
    /**
     * 创建背景渐变
     */
    private func createBackgroundGradient() -> some View {
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(hex: "#fcfff4"), location: 0.0),
                .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                .init(color: Color.white, location: 0.7),
                .init(color: Color(hex: "#fafffe"), location: 1.0)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea(.all)
    }
    
    /**
     * 顶部导航栏
     */
    private var topNavigationBar: some View {
        VStack {
            HStack {
                // 返回按钮
                Button(action: onDismiss) {
                    Image("fanhui")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                .frame(width: 44, height: 44)
                
                Spacer()
                
                // 标题
                Text("ai_analysis.title".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 右侧占位（保持对称）
                Color.clear
                    .frame(width: 44, height: 44)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            Spacer()
        }
        .opacity(pageAppeared ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.6), value: pageAppeared)
    }
    
    /**
     * 权限不足视图
     */
    private var permissionDeniedView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // 权限图标
            Image(systemName: "lock.circle")
                .font(.system(size: 64))
                .foregroundColor(Color(hex: "#FFB84D"))
                .opacity(pageAppeared ? 1.0 : 0.0)
                .scaleEffect(pageAppeared ? 1.0 : 0.8)
                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: pageAppeared)
            
            // 权限说明
            VStack(spacing: 12) {
                Text("ai_analysis.permission_title".localized)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(viewModel.getPermissionStatusDescription())
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            .opacity(pageAppeared ? 1.0 : 0.0)
            .offset(y: pageAppeared ? 0 : 20)
            .animation(.easeOut(duration: 0.6).delay(0.4), value: pageAppeared)
            
            // 升级按钮
            if viewModel.canShowUpgradeButton {
                Button(action: {
                    // 关闭当前页面并跳转到订阅页面
                    onDismiss()
                    // 触发订阅页面跳转
                    onNavigateToSubscription?()
                }) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 16))
                        Text("ai_analysis.upgrade_button".localized)
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#FFB84D"),
                                Color(hex: "#FFA000")
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: Color(hex: "#FFB84D").opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .opacity(pageAppeared ? 1.0 : 0.0)
                .scaleEffect(pageAppeared ? 1.0 : 0.9)
                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.6), value: pageAppeared)
            }
            
            Spacer()
        }
        .padding(.horizontal, 24)
    }
    
    /**
     * 生成报告视图
     */
    private var generateReportView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // 学生信息卡片
            studentInfoCard
            
            // 报告类型选择器
            reportTypeSelector
                .padding(.top, 8)
            
            // 生成按钮
            Button(action: {
                Task {
                    await viewModel.generateReport()
                }
            }) {
                HStack {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 18))
                    Text("ai_analysis.generate_button".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#74c07f"),
                            Color(hex: "#5da961")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(25)
                .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(!viewModel.isNetworkAvailable)
            .opacity(viewModel.isNetworkAvailable ? 1.0 : 0.6)
            
            // 网络状态提示
            if !viewModel.isNetworkAvailable {
                Text("ai_analysis.no_network".localized)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }
            
            Spacer()
        }
        .padding(.horizontal, 24)
        .opacity(pageAppeared ? 1.0 : 0.0)
        .offset(y: pageAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: pageAppeared)
    }
    
    /**
     * 学生信息卡片
     */
    private var studentInfoCard: some View {
        VStack(spacing: 16) {
            // 学生头像和基本信息
            HStack(spacing: 16) {
                Image(student.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 60, height: 60)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 3)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(student.name ?? "ai_analysis.unknown_student".localized)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("ai_analysis.current_points".localized + ": \(student.point)")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    if let stats = viewModel.getStudentStatistics() {
                        Text("ai_analysis.record_count".localized + ": \(stats.recordCount)")
                            .font(.system(size: 14))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                
                Spacer()
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }
    
    /**
     * 报告类型选择器
     */
    private var reportTypeSelector: some View {
        VStack(spacing: 12) {
            Text("ai_analysis.select_report_type".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 两种报告类型的选择按钮
            HStack(spacing: 12) {
                reportTypeButton(
                    type: .professional,
                    icon: "doc.text",
                    title: "ai_analysis.report_type.professional".localized,
                    description: "ai_analysis.professional_description".localized
                )
                
                reportTypeButton(
                    type: .parentFeedback,
                    icon: "message",
                    title: "ai_analysis.report_type.parent_feedback".localized,
                    description: "ai_analysis.feedback_description".localized
                )
            }
        }
    }
    
    /**
     * 报告类型按钮
     */
    private func reportTypeButton(type: ReportType, icon: String, title: String, description: String) -> some View {
        let isSelected = viewModel.selectedReportType == type
        
        return Button(action: {
            viewModel.setReportType(type)
        }) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: icon)
                        .font(.system(size: 16))
                        .foregroundColor(isSelected ? Color.white : Color(hex: "#5da961"))
                    
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(isSelected ? Color.white : DesignSystem.Colors.textPrimary)
                }
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(isSelected ? Color.white.opacity(0.9) : DesignSystem.Colors.textSecondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
            .padding(12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                isSelected ?
                LinearGradient(
                    gradient: Gradient(colors: [Color(hex: "#74c07f"), Color(hex: "#5da961")]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ) :
                LinearGradient(
                    gradient: Gradient(colors: [Color(hex: "#F8F8F8"), Color(hex: "#F8F8F8")]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        isSelected ? Color.clear : Color(hex: "#E0E0E0"),
                        lineWidth: 1
                    )
            )
        }
    }
    
    /**
     * 加载视图
     */
    private var loadingView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // 加载动画
            ProgressView()
                .scaleEffect(1.5)
                .tint(Color(hex: "#74c07f"))
            
            Text("ai_analysis.generating".localized)
                .font(.system(size: 16))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Text("ai_analysis.please_wait".localized)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textTertiary)
            
            Spacer()
        }
    }
    
    /**
     * 报告内容视图
     */
    private func reportContentView(report: AIAnalysisReport) -> some View {
        VStack(spacing: 0) {
            // 报告详细内容
            ReportContentView(
                report: report,
                onCopy: {
                    viewModel.copyReportToClipboard()
                }
            )
        }
        .opacity(pageAppeared ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.6), value: pageAppeared)
    }
    
    // MARK: - Methods
    
    /**
     * 设置视图
     */
    private func setupView() {
        viewModel.setupStudent(student)
        
        withAnimation(.easeInOut(duration: 0.8)) {
            pageAppeared = true
        }
    }
} 