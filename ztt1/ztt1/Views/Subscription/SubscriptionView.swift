//
//  SubscriptionView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 订阅页面主视图
 * 实现三层ZStack架构：独立组件层(最底层) -> 背景图层(中间层) -> 分段选项卡层(最上层)
 * 支持响应式布局，适配不同设备尺寸
 */
struct SubscriptionView: View {
    
    // MARK: - Properties
    var onDismiss: (() -> Void)? = nil
    
    // MARK: - Environment
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authManager: AuthenticationManager
    
    // MARK: - View Models
    @StateObject private var subscriptionViewModel = SubscriptionViewModel()

    // 添加试用管理器和订阅管理器 - 与ProfileView保持一致
    @StateObject private var trialManager = TrialManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    
    // MARK: - State
    @State private var selectedMembershipType: Int = 0 // 0: 初级会员, 1: 高级会员
    @State private var selectedPriceType: Int = 0 // 0: 月会员, 1: 年会员
    @State private var agreementAccepted: Bool = false
    @State private var showSuccessModal: Bool = false // 订阅成功弹窗状态
    
    // MARK: - 分层动画状态管理
    @State private var backgroundAppeared: Bool = false
    @State private var decorationAppeared: Bool = false  
    @State private var userInfoAppeared: Bool = false
    @State private var contentAppeared: Bool = false
    @State private var tabAppeared: Bool = false
    @State private var pageAppeared: Bool = false // 保留原有状态以兼容现有逻辑
    
    // MARK: - Computed Properties
    // 从AuthenticationManager获取真实用户数据，与ProfileView保持一致
    private var userName: String {
        return authManager.currentUser?.name ?? "user_info.parent_nickname".localized
    }
    
    private var userID: String {
        return authManager.getUserEmail() ?? "profile.no_email".localized
    }
    
    private var membershipLevel: String {
        // 优先检查试用状态 - 与ProfileView保持一致
        if trialManager.isTrialActive {
            return "subscription.membership.premium_trial".localized
        }

        // 检查RevenueCat订阅状态 - 与ProfileView保持一致
        switch revenueCatManager.currentSubscriptionLevel {
        case .basic:
            return "subscription.membership.basic".localized
        case .premium:
            return "subscription.membership.premium".localized
        case .free:
            return "subscription.user_level_regular".localized
        }
    }
    
    private var expirationDate: String {
        // 优先检查试用到期时间 - 与ProfileView保持一致
        if trialManager.isTrialActive, let trialExpiration = trialManager.trialExpirationDate {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年MM月dd日"
            return formatter.string(from: trialExpiration)
        }

        // 检查RevenueCat订阅到期时间 - 与ProfileView保持一致
        if let revenueCatExpiration = revenueCatManager.expirationDate {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年MM月dd日"
            return formatter.string(from: revenueCatExpiration)
        }

        return "subscription.not_activated".localized
    }
    
    var body: some View {
        GeometryReader { geometry in
            setupGeometry(geometry: geometry)
        }
        .onAppear {
            setupEntranceAnimations()
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建装饰性背景系统
     * 参考个人中心页面的美化背景设计
     */
    private func createDecorativeBackground(geometry: GeometryProxy) -> some View {
        let screenWidth = geometry.size.width
        let screenHeight = geometry.size.height
        
        return ZStack {
            // 美化背景渐变 - 与个人中心页面保持一致的风格
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)
            .opacity(backgroundAppeared ? 1.0 : 0.0)
            .animation(.easeIn(duration: DesignSystem.SubscriptionPage.AnimationPresets.Background.fadeInDuration), value: backgroundAppeared)
            
            // 装饰性背景元素 - 参考个人中心设计
            Group {
                // 左上角大圆圈
                Circle()
                    .fill(DesignSystem.SubscriptionPage.DecorationElements.circle1Color)
                    .frame(
                        width: DesignSystem.AdaptiveLayout.SubscriptionPage.DecorationElements.circle1Size,
                        height: DesignSystem.AdaptiveLayout.SubscriptionPage.DecorationElements.circle1Size
                    )
                    .position(
                        x: screenWidth / 2 + DesignSystem.AdaptiveLayout.SubscriptionPage.DecorationElements.circle1Position.x,
                        y: screenHeight / 2 + DesignSystem.AdaptiveLayout.SubscriptionPage.DecorationElements.circle1Position.y
                    )
                    .opacity(decorationAppeared ? 1.0 : 0.0)
                    .scaleEffect(decorationAppeared ? DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleEnd : DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleStart)
                    .animation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springResponse, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.SubscriptionPage.AnimationPresets.Decoration.delay), value: decorationAppeared)
                
                // 右上角中等圆圈
                Circle()
                    .fill(DesignSystem.SubscriptionPage.DecorationElements.circle2Color)
                    .frame(
                        width: DesignSystem.SubscriptionPage.DecorationElements.circle2Size,
                        height: DesignSystem.SubscriptionPage.DecorationElements.circle2Size
                    )
                    .position(
                        x: screenWidth / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle2Position.x,
                        y: screenHeight / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle2Position.y
                    )
                    .opacity(decorationAppeared ? 1.0 : 0.0)
                    .scaleEffect(decorationAppeared ? DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleEnd : DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleStart)
                    .animation(.spring(response: DesignSystem.SubscriptionPage.AnimationPresets.springResponse, dampingFraction: DesignSystem.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.SubscriptionPage.AnimationPresets.Decoration.delay + 0.1), value: decorationAppeared)
                
                // 右下角小圆圈
                Circle()
                    .fill(DesignSystem.SubscriptionPage.DecorationElements.circle3Color)
                    .frame(
                        width: DesignSystem.SubscriptionPage.DecorationElements.circle3Size,
                        height: DesignSystem.SubscriptionPage.DecorationElements.circle3Size
                    )
                    .position(
                        x: screenWidth / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle3Position.x,
                        y: screenHeight / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle3Position.y
                    )
                    .opacity(decorationAppeared ? 1.0 : 0.0)
                    .scaleEffect(decorationAppeared ? DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleEnd : DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleStart)
                    .animation(.spring(response: DesignSystem.SubscriptionPage.AnimationPresets.springResponse, dampingFraction: DesignSystem.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.SubscriptionPage.AnimationPresets.Decoration.delay + 0.2), value: decorationAppeared)
                
                // 左下角白色圆圈
                Circle()
                    .fill(DesignSystem.SubscriptionPage.DecorationElements.circle4Color)
                    .frame(
                        width: DesignSystem.SubscriptionPage.DecorationElements.circle4Size,
                        height: DesignSystem.SubscriptionPage.DecorationElements.circle4Size
                    )
                    .position(
                        x: screenWidth / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle4Position.x,
                        y: screenHeight / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle4Position.y
                    )
                    .opacity(decorationAppeared ? 1.0 : 0.0)
                    .scaleEffect(decorationAppeared ? DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleEnd : DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleStart)
                    .animation(.spring(response: DesignSystem.SubscriptionPage.AnimationPresets.springResponse, dampingFraction: DesignSystem.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.SubscriptionPage.AnimationPresets.Decoration.delay + 0.3), value: decorationAppeared)
                
                // 右侧小装饰圆圈
                Circle()
                    .fill(DesignSystem.SubscriptionPage.DecorationElements.circle5Color)
                    .frame(
                        width: DesignSystem.SubscriptionPage.DecorationElements.circle5Size,
                        height: DesignSystem.SubscriptionPage.DecorationElements.circle5Size
                    )
                    .position(
                        x: screenWidth / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle5Position.x,
                        y: screenHeight / 2 + DesignSystem.SubscriptionPage.DecorationElements.circle5Position.y
                    )
                    .opacity(decorationAppeared ? 1.0 : 0.0)
                    .scaleEffect(decorationAppeared ? DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleEnd : DesignSystem.SubscriptionPage.AnimationPresets.Decoration.scaleStart)
                    .animation(.spring(response: DesignSystem.SubscriptionPage.AnimationPresets.springResponse, dampingFraction: DesignSystem.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.SubscriptionPage.AnimationPresets.Decoration.delay + 0.4), value: decorationAppeared)
            }
        }
    }
    
    /**
     * 设置入场动画序列
     * 实现分层协调的动画效果
     */
    private func setupEntranceAnimations() {
        // 重置所有动画状态
        resetAnimationStates()
        
        // 分层启动动画序列
        withAnimation(.easeIn(duration: DesignSystem.SubscriptionPage.AnimationPresets.Background.duration).delay(DesignSystem.SubscriptionPage.AnimationPresets.Background.delay)) {
            backgroundAppeared = true
        }
        
        withAnimation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springResponse, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.SubscriptionPage.AnimationPresets.Decoration.delay)) {
            decorationAppeared = true
        }
        
        withAnimation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.delay)) {
            userInfoAppeared = true
        }
        
        withAnimation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.delay)) {
            contentAppeared = true
        }
        
        withAnimation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.delay)) {
            tabAppeared = true
        }
        
        // 保持原有的pageAppeared状态兼容性
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
            pageAppeared = true
        }
    }
    
    /**
     * 重置动画状态
     */
    private func resetAnimationStates() {
        backgroundAppeared = false
        decorationAppeared = false
        userInfoAppeared = false
        contentAppeared = false
        tabAppeared = false
        pageAppeared = false
    }
    
    /**
     * 根据GeometryProxy设置页面布局
     */
         private func setupGeometry(geometry: GeometryProxy) -> some View {
        let screenWidth = geometry.size.width
        let screenHeight = geometry.size.height
        let _ = geometry.safeAreaInsets.top
        let _ = geometry.safeAreaInsets.leading
        let _ = geometry.safeAreaInsets.trailing
        
        return ZStack {
            // 底层：屏幕背景色
            Color(hex: "#fcfff4")
                .ignoresSafeArea(.all)
            
            // 第一层：独立组件层
            ZStack {
                // 个人信息组件 - 绝对定位到顶部30%
                SubscriptionUserInfoSection(
                    userName: userName,
                    userID: userID,
                    membershipLevel: membershipLevel,
                    expirationDate: expirationDate,
                    onBackPressed: handleBackPressed
                )
                .frame(height: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.UserInfoSection.heightPercentage)
                .position(
                    x: screenWidth / 2,
                    y: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.UserInfoSection.topPositionPercentage
                )
                .opacity(userInfoAppeared ? 1.0 : 0.0)
                .offset(y: userInfoAppeared ? 0 : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.offsetY)
                .scaleEffect(userInfoAppeared ? DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.scaleEnd : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.scaleStart)
                .animation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.delay), value: userInfoAppeared)
                
                // 会员内容组件 - 绝对定位到下方
                MembershipContentView(
                    selectedMembershipType: $selectedMembershipType,
                    selectedPriceType: $selectedPriceType,
                    agreementAccepted: $agreementAccepted,
                    isLoading: subscriptionViewModel.isLoading,
                    purchaseSuccess: subscriptionViewModel.purchaseSuccess,
                    errorMessage: subscriptionViewModel.errorMessage,
                    onSubscribePressed: handleSubscribePressed
                )
                .position(
                    x: screenWidth / 2,
                    y: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.ContentSection.topOffsetPercentage + 120
                )
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.offsetY)
                .animation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.delay), value: contentAppeared)
            }
            
            // 第二层：背景图层（中间层，覆盖在独立组件之上）
            BackgroundImageLayer(selectedMembershipType: $selectedMembershipType)
            
            // 第三层：分段选项卡层（最上层，透明背景）
            VStack {
                Spacer()
                    .frame(height: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.topOffsetPercentage)
                
                MembershipTabSegment(
                    selectedTab: $selectedMembershipType,
                    onTabChanged: handleTabChanged
                )
                .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight)
                .padding(.horizontal, DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.horizontalPadding) // 使用可调整的水平边距
                .opacity(tabAppeared ? 1.0 : 0.0)
                .scaleEffect(tabAppeared ? DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.scaleEnd : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.scaleStart)
                .offset(y: tabAppeared ? 0 : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.offsetY)
                .animation(.spring(
                    response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.duration,
                    dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping
                ).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.delay), value: tabAppeared)
                
                Spacer()
            }
        }
        .ignoresSafeArea(.all) // 允许内容扩展到安全区域
        .overlay(
            // 订阅成功弹窗
            Group {
                if showSuccessModal {
                    SubscriptionSuccessModal(
                        isPresented: $showSuccessModal,
                        onConfirm: {
                            showSuccessModal = false
                            // 延迟返回个人中心，让弹窗关闭动画完成
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                handleBackPressed()
                            }
                        }
                    )
                    .zIndex(1000) // 确保弹窗在最上层
                }
            }
        )
    }
    
    /**
     * 处理返回按钮点击
     */
    private func handleBackPressed() {
        print("返回按钮被点击")
        
        // 添加退出动画 - 逆序隐藏各层
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            tabAppeared = false
            contentAppeared = false  
            userInfoAppeared = false
            decorationAppeared = false
            backgroundAppeared = false
            pageAppeared = false
        }
        
        // 延迟关闭页面，等待动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            if let onDismiss = onDismiss {
                onDismiss()
            } else {
                dismiss()
            }
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 处理分段选项卡切换
     */
    private func handleTabChanged(to type: Int) {
        let membershipTypeName = type == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized
        print("会员类型切换到: \(membershipTypeName)")

        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            // selectedMembershipType 通过 Binding 自动更新
            // 重置价格选择为月会员
            selectedPriceType = 0
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 处理订阅按钮点击
     */
    private func handleSubscribePressed() {
        let membershipTypeName = selectedMembershipType == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized
        let priceTypeName = selectedPriceType == 0 ? "subscription.pricing.monthly".localized : "subscription.pricing.yearly".localized

        print("🛒 订阅按钮被点击 - \(membershipTypeName) \(priceTypeName)")

        // 重置购买状态
        subscriptionViewModel.resetPurchaseState()

        // 确定产品ID
        let productId = getProductId(
            membershipType: selectedMembershipType,
            priceType: selectedPriceType
        )

        print("🆔 准备购买产品: \(productId)")

        // 开始购买流程
        Task {
            await purchaseSubscription(productId: productId)
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 根据用户选择获取产品ID
     */
    private func getProductId(membershipType: Int, priceType: Int) -> String {
        switch (membershipType, priceType) {
        case (0, 0): // 初级会员 + 月度
            return "com.ztt1.subscription.monthly.basic"
        case (0, 1): // 初级会员 + 年度
            return "com.ztt1.subscription.yearly.basic"
        case (1, 0): // 高级会员 + 月度
            return "com.ztt1.subscription.monthly.premium"
        case (1, 1): // 高级会员 + 年度
            return "com.ztt1.subscription.yearly.premium"
        default:
            return "com.ztt1.subscription.monthly.basic" // 默认为初级月度
        }
    }
    
    /**
     * 执行订阅购买
     */
    private func purchaseSubscription(productId: String) async {
        // 重置状态
        await MainActor.run {
            subscriptionViewModel.isLoading = true
            subscriptionViewModel.errorMessage = ""
            subscriptionViewModel.purchaseSuccess = false
            RevenueCatManager.shared.purchaseSuccess = false
            // 同步协议接受状态
            subscriptionViewModel.agreementAccepted = agreementAccepted
            print("🔄 状态同步完成 - SubscriptionView协议状态: \(agreementAccepted), ViewModel协议状态: \(subscriptionViewModel.agreementAccepted)")
        }

        // 直接调用 RevenueCat 购买
        let success = await RevenueCatManager.shared.purchaseProduct(productId: productId)

        // 等待一段时间让 delegate 回调有机会执行
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 等待1秒

        await MainActor.run {
            subscriptionViewModel.isLoading = false

            // 检查 RevenueCat 的购买成功状态或直接的购买结果
            let finalSuccess = success || RevenueCatManager.shared.purchaseSuccess

            if finalSuccess {
                // 购买成功
                print("✅ 订阅成功: \(productId)")
                subscriptionViewModel.purchaseSuccess = true

                // 成功反馈
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)

                // 显示订阅成功弹窗
                showSuccessModal = true

            } else {
                // 购买失败或取消
                let errorMsg = RevenueCatManager.shared.errorMessage ?? "未知错误"
                print("❌ 订阅失败或取消: \(errorMsg)")

                // 检查是否是用户取消
                if errorMsg.contains("用户取消") || errorMsg.contains("cancelled") {
                    // 用户取消，不显示错误提示，只重置状态
                    print("ℹ️ 用户取消了购买，重置UI状态")
                    subscriptionViewModel.errorMessage = ""
                } else {
                    // 其他错误，显示错误反馈
                    subscriptionViewModel.errorMessage = errorMsg
                    let errorFeedback = UINotificationFeedbackGenerator()
                    errorFeedback.notificationOccurred(.error)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    SubscriptionView()
        .environmentObject(AuthenticationManager())
        .preferredColorScheme(.light)
}