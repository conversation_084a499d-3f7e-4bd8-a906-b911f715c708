//
//  MembershipContentView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 会员内容展示组件
 * 包含权益描述、价格标签、订阅按钮和服务协议勾选
 */
struct MembershipContentView: View {
    
    // MARK: - Properties
    @Binding var selectedMembershipType: Int // 0: 初级, 1: 高级
    @Binding var selectedPriceType: Int // 0: 月会员, 1: 年会员
    @Binding var agreementAccepted: Bool
    let isLoading: Bool
    let purchaseSuccess: Bool
    let errorMessage: String?
    let onSubscribePressed: () -> Void
    
    // MARK: - Computed Properties
    private var primaryFeatures: [String] {
        return [
            "subscription.feature.manage_classes_two".localized,
            "subscription.feature.unlock_wheel".localized,
            "subscription.feature.multi_device_sync".localized,
            "subscription.feature.basic_functions".localized
        ]
    }
    
    private var advancedFeatures: [String] {
        return [
            "subscription.feature.all_basic_benefits".localized,
            "subscription.feature.manage_classes_five".localized,
            "subscription.feature.unlock_box_scratch".localized,
            "subscription.feature.ai_reports".localized
        ]
    }
    
    private var priceData: [(title: String, price: String, unit: String)] {
        return [
            (title: "subscription.pricing.monthly".localized, price: "38", unit: "¥"),
            (title: "subscription.pricing.yearly".localized, price: "188", unit: "¥")
        ]
    }
    
    private var advancedPriceData: [(title: String, price: String, unit: String)] {
        return [
            (title: "subscription.pricing.monthly".localized, price: "58", unit: "¥"),
            (title: "subscription.pricing.yearly".localized, price: "388", unit: "¥")
        ]
    }
    
    // MARK: - State
    @State private var contentAppeared = false
    @State private var subscribeButtonPressed = false
    @State private var showingMembershipAgreement = false
    @State private var showAgreementReminder = false
    
    var body: some View {
        VStack(alignment: .center) {
            // 会员权益描述
            createFeatureList()
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : 20)
                .animation(.easeOut(duration: 0.6).delay(0.4), value: contentAppeared)
            
            // 价格标签
            createPriceCards()
                .padding(.top, DesignSystem.SubscriptionPage.ContentSection.featureToPriceSpacing)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : 30)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.5), value: contentAppeared)
            
            // 订阅按钮
            createSubscribeButton()
                .padding(.top, DesignSystem.SubscriptionPage.ContentSection.priceToButtonSpacing)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : 20)
                .animation(.spring(response: 0.7, dampingFraction: 0.8).delay(0.6), value: contentAppeared)
            
            // 服务协议勾选
            createAgreementSection()
                .padding(.top, DesignSystem.SubscriptionPage.ContentSection.buttonToAgreementSpacing)
                .opacity(contentAppeared ? 1.0 : 0.0)
                .animation(.easeIn(duration: 0.4).delay(0.7), value: contentAppeared)
        }
        .padding(.horizontal, DesignSystem.SubscriptionPage.ContentSection.horizontalPadding)
        .background(Color(hex: "#fcfff4")) // 设置会员内容展示组件背景颜色
        .cornerRadius(0) // 添加圆角以更好地显示背景
        .onAppear {
            contentAppeared = true
        }
        .sheet(isPresented: $showingMembershipAgreement) {
            MembershipAgreementView()
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建会员权益列表
     */
    private func createFeatureList() -> some View {
        let features = selectedMembershipType == 0 ? primaryFeatures : advancedFeatures
        
        return VStack(alignment: .leading, spacing: DesignSystem.SubscriptionPage.FeatureList.verticalSpacing) {
            ForEach(Array(features.enumerated()), id: \.offset) { index, feature in
                HStack(spacing: DesignSystem.SubscriptionPage.FeatureList.spacing) {
                    Image("huiyuan")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(
                            width: DesignSystem.SubscriptionPage.FeatureList.iconSize,
                            height: DesignSystem.SubscriptionPage.FeatureList.iconSize
                        )
                        .foregroundColor(DesignSystem.SubscriptionPage.FeatureList.textColor)
                    
                    Text(feature)
                        .font(.system(
                            size: DesignSystem.SubscriptionPage.FeatureList.fontSize,
                            weight: .regular
                        ))
                        .foregroundColor(DesignSystem.SubscriptionPage.FeatureList.textColor)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(x: contentAppeared ? 0 : -20)
                .animation(.easeOut(duration: 0.5).delay(0.4 + Double(index) * 0.1), value: contentAppeared)
            }
        }
    }
    
    /**
     * 创建价格卡片
     * 修复版本：使用稳定的ID避免切换时的跳动动画
     */
    private func createPriceCards() -> some View {
        let prices = selectedMembershipType == 0 ? priceData : advancedPriceData
        
        return HStack(spacing: DesignSystem.SubscriptionPage.PriceCard.spacing) {
            // 月会员卡片 - 固定ID
            createPriceCard(
                title: prices[0].title,
                price: prices[0].price,
                unit: prices[0].unit,
                isSelected: selectedPriceType == 0,
                index: 0
            )
            .id("monthly_card") // 固定ID，避免重建
            
            // 年会员卡片 - 固定ID
            createPriceCard(
                title: prices[1].title,
                price: prices[1].price,
                unit: prices[1].unit,
                isSelected: selectedPriceType == 1,
                index: 1
            )
            .id("yearly_card") // 固定ID，避免重建
        }
        .animation(.easeInOut(duration: 0.25), value: selectedMembershipType) // 会员类型切换时的整体平滑动画
    }
    
    /**
     * 创建单个价格卡片
     */
    private func createPriceCard(title: String, price: String, unit: String, isSelected: Bool, index: Int) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                selectedPriceType = index
            }
            
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            VStack(spacing: 12) {
                // 标题
                Text(title)
                    .font(.system(
                        size: DesignSystem.SubscriptionPage.PriceCard.titleFont,
                        weight: .medium
                    ))
                    .foregroundColor(DesignSystem.SubscriptionPage.PriceCard.titleColor)
                
                // 价格
                HStack(alignment: .firstTextBaseline, spacing: 2) {
                    Text(unit)
                        .font(.system(
                            size: DesignSystem.SubscriptionPage.PriceCard.unitFont,
                            weight: .medium
                        ))
                        .foregroundColor(DesignSystem.SubscriptionPage.PriceCard.priceColor)
                    
                    Text(price)
                        .font(.system(
                            size: DesignSystem.SubscriptionPage.PriceCard.priceFont,
                            weight: .bold
                        ))
                        .foregroundColor(DesignSystem.SubscriptionPage.PriceCard.priceColor)
                        // iOS 16.0+ 才支持 contentTransition，移除以保证兼容性
                }
            }
            .frame(
                width: DesignSystem.SubscriptionPage.PriceCard.width,
                height: DesignSystem.SubscriptionPage.PriceCard.height
            )
            .background(
                isSelected ? 
                DesignSystem.SubscriptionPage.PriceCard.selectedBackgroundColor :
                DesignSystem.SubscriptionPage.PriceCard.unselectedBackgroundColor
            )
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.SubscriptionPage.PriceCard.cornerRadius)
                    .stroke(
                        isSelected ? 
                        DesignSystem.SubscriptionPage.PriceCard.selectedBorderColor :
                        DesignSystem.SubscriptionPage.PriceCard.unselectedBorderColor,
                        lineWidth: isSelected ? 
                        DesignSystem.SubscriptionPage.PriceCard.selectedBorderWidth :
                        DesignSystem.SubscriptionPage.PriceCard.unselectedBorderWidth
                    )
            )
            .cornerRadius(DesignSystem.SubscriptionPage.PriceCard.cornerRadius)
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .shadow(
                color: isSelected ? 
                DesignSystem.SubscriptionPage.PriceCard.selectedBorderColor.opacity(0.3) :
                Color.clear,
                radius: isSelected ? 8 : 0,
                x: 0,
                y: isSelected ? 4 : 0
            )
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.3), value: price) // 添加价格变化的平滑动画
    }
    
    /**
     * 创建订阅按钮
     */
    private func createSubscribeButton() -> some View {
        VStack(spacing: 8) {
            // 订阅按钮
            Button(action: {
                print("🔘 订阅按钮被点击")
                print("🔍 协议接受状态: \(agreementAccepted)")

                guard agreementAccepted else {
                    print("⚠️ 用户未勾选协议，阻止购买")
                    // 显示协议提醒
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        showAgreementReminder = true
                    }

                    // 3秒后自动隐藏提醒
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            showAgreementReminder = false
                        }
                    }

                    // 触觉反馈 - 错误
                    let feedbackGenerator = UINotificationFeedbackGenerator()
                    feedbackGenerator.notificationOccurred(.error)
                    return
                }
                
                guard !isLoading else {
                    return // 正在加载中，禁止重复点击
                }
                
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    subscribeButtonPressed = true
                }
                
                onSubscribePressed()
                
                // 恢复按钮状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                    subscribeButtonPressed = false
                }
                
                // 触觉反馈 - 成功
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }) {
                HStack(spacing: 8) {
                    // 加载指示器
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }
                    
                    // 按钮文本
                    Text(getButtonText())
                        .font(.system(
                            size: DesignSystem.SubscriptionPage.SubscribeButton.fontSize,
                            weight: DesignSystem.SubscriptionPage.SubscribeButton.fontWeight
                        ))
                        .foregroundColor(DesignSystem.SubscriptionPage.SubscribeButton.textColor)
                }
                .frame(maxWidth: .infinity)
                .frame(height: DesignSystem.SubscriptionPage.SubscribeButton.height)
                .background(getButtonBackgroundColor())
                .cornerRadius(DesignSystem.SubscriptionPage.SubscribeButton.cornerRadius)
                .shadow(
                    color: getButtonShadowColor(),
                    radius: (agreementAccepted && !isLoading) ? 8 : 0,
                    x: 0,
                    y: (agreementAccepted && !isLoading) ? 4 : 0
                )
                .scaleEffect(subscribeButtonPressed ? 0.95 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: subscribeButtonPressed)
                .animation(.easeInOut(duration: 0.3), value: agreementAccepted)
                .animation(.easeInOut(duration: 0.3), value: isLoading)
                .animation(.easeInOut(duration: 0.3), value: purchaseSuccess)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(!agreementAccepted || isLoading)
            
            // 协议提醒显示
            if showAgreementReminder {
                HStack(spacing: 6) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.orange)

                    Text("purchase.agreement.reminder".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.orange)
                }
                .padding(.horizontal, 16)
                .transition(.opacity.combined(with: .move(edge: .top)))
            }

            // 错误信息显示
            if let errorMessage = errorMessage, !errorMessage.isEmpty, !isLoading, !showAgreementReminder {
                Text(errorMessage)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 16)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            // 成功信息显示
            if purchaseSuccess && !isLoading {
                HStack(spacing: 6) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.green)
                    
                    Text("purchase.success_processing".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.green)
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }
    
    /**
     * 获取按钮文本
     */
    private func getButtonText() -> String {
        if isLoading {
            return "purchase.processing".localized
        } else if purchaseSuccess {
            return "purchase.success".localized
        } else {
            return "subscription.subscribe_button".localized
        }
    }
    
    /**
     * 获取按钮背景色
     */
    private func getButtonBackgroundColor() -> Color {
        if purchaseSuccess {
            return .green
        } else if !agreementAccepted {
            return DesignSystem.SubscriptionPage.SubscribeButton.backgroundColor.opacity(0.5)
        } else if isLoading {
            return DesignSystem.SubscriptionPage.SubscribeButton.backgroundColor.opacity(0.8)
        } else {
            return DesignSystem.SubscriptionPage.SubscribeButton.backgroundColor
        }
    }
    
    /**
     * 获取按钮阴影色
     */
    private func getButtonShadowColor() -> Color {
        if purchaseSuccess {
            return Color.green.opacity(0.3)
        } else if agreementAccepted && !isLoading {
            return DesignSystem.SubscriptionPage.SubscribeButton.backgroundColor.opacity(0.3)
        } else {
            return Color.clear
        }
    }
    
    /**
     * 创建服务协议勾选区域
     */
    private func createAgreementSection() -> some View {
        HStack(spacing: DesignSystem.SubscriptionPage.AgreementSection.spacing) {
            // 勾选框按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    agreementAccepted.toggle()
                }
                
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }) {
                ZStack {
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(DesignSystem.SubscriptionPage.AgreementSection.textColor, lineWidth: 1.5)
                        .frame(
                            width: DesignSystem.SubscriptionPage.AgreementSection.checkboxSize,
                            height: DesignSystem.SubscriptionPage.AgreementSection.checkboxSize
                        )
                    
                    if agreementAccepted {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(DesignSystem.SubscriptionPage.AgreementSection.textColor)
                            .frame(
                                width: DesignSystem.SubscriptionPage.AgreementSection.checkboxSize,
                                height: DesignSystem.SubscriptionPage.AgreementSection.checkboxSize
                            )
                        
                        Image(systemName: "checkmark")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: agreementAccepted)
            }
            .buttonStyle(PlainButtonStyle())

            // 协议文本
            HStack(spacing: 0) {
                Text("subscription.agreement_prompt".localized)
                    .font(.system(
                        size: DesignSystem.SubscriptionPage.AgreementSection.fontSize,
                        weight: .regular
                    ))
                    .foregroundColor(DesignSystem.SubscriptionPage.AgreementSection.textColor)

                Text("subscription.agreement_link".localized)
                    .font(.system(
                        size: DesignSystem.SubscriptionPage.AgreementSection.fontSize,
                        weight: .regular
                    ))
                    .foregroundColor(Color(hex: "#858585"))
                    .onTapGesture {
                        showingMembershipAgreement = true
                    }
            }
            
            Spacer()
        }
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State var membershipType = 1
        @State var priceType = 0
        @State var agreement = false
        
        var body: some View {
            ScrollView {
                VStack(spacing: 40) {
                    // 切换会员类型的控制
                    Picker("会员类型", selection: $membershipType) {
                                            Text("subscription.membership.basic".localized).tag(0)
                    Text("subscription.membership.premium".localized).tag(1)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.horizontal)
                    
                    // 会员内容组件
                    MembershipContentView(
                        selectedMembershipType: $membershipType,
                        selectedPriceType: $priceType,
                        agreementAccepted: $agreement,
                        isLoading: false,
                        purchaseSuccess: false,
                        errorMessage: nil
                    ) {
                        print("订阅按钮被点击")
                    }
                    
                    Spacer()
                }
            }
            .background(Color(hex: "#f8fdf0"))
        }
    }
    
    return PreviewWrapper()
} 