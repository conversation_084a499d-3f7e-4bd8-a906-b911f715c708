//
//  MembershipTabSegment.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI

/**
 * 会员等级切换分段选项卡组件
 * 透明背景设计，与背景图完美重合
 * 支持平滑的切换动画
 */
struct MembershipTabSegment: View {
    
    // MARK: - Properties
    @Binding var selectedTab: Int // 0: 初级会员, 1: 高级会员
    let onTabChanged: (Int) -> Void
    
    // MARK: - Computed Properties
    private var tabTitles: [String] {
        return [
            "subscription.membership.basic".localized,
            "subscription.membership.premium".localized
        ]
    }
    
    // MARK: - State
    @State private var tabAppeared = false
    @State private var buttonPressedStates = [false, false]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 透明背景容器
                RoundedRectangle(cornerRadius: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabCornerRadius)
                    .fill(Color.clear) // 完全透明，让背景图透过
                    .frame(
                        width: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.totalWidth,
                        height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight
                    )
                
                // 分段选项卡按钮
                HStack(spacing: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.spacing) {
                    ForEach(0..<tabTitles.count, id: \.self) { index in
                        createTabButton(title: tabTitles[index], index: index, geometry: geometry)
                    }
                }
                .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight)
            }
            // 应用整体偏移
            .offset(
                x: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.offsetX,
                y: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.offsetY
            )
            // 应用整体缩放
            .scaleEffect(
                x: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.scaleX,
                y: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.scaleY
            )
        }
        .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight)
        .opacity(tabAppeared ? 1.0 : 0.0)
        .scaleEffect(tabAppeared ? 1.0 : 0.9)
        .animation(.spring(
            response: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.animationSpringResponse,
            dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.animationSpringDamping
        ).delay(0.3), value: tabAppeared)
        .onAppear {
            tabAppeared = true
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建分段选项卡按钮
     * 📌 新增：扩大点击区域，提升用户体验
     */
    private func createTabButton(title: String, index: Int, geometry: GeometryProxy) -> some View {
        let isSelected = selectedTab == index
        let config = DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.self
        
        // 如果设置了自定义宽度，使用它，否则平均分配
        let buttonWidth = config.tabWidth > 0 ? config.tabWidth : geometry.size.width / CGFloat(tabTitles.count)
        
        // 计算扩展的点击区域尺寸
        let touchWidth = max(buttonWidth + config.touchAreaPadding * 2, config.touchAreaMinWidth)
        let touchHeight = max(config.tabHeight + config.touchAreaPadding * 2, config.touchAreaMinHeight)
        
        return Button(action: {
            animateTabSelection(to: index)
        }) {
            ZStack {
                // 📌 不可见的扩大点击区域 - 提供更大的触发范围
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: touchWidth, height: touchHeight)
                    .contentShape(Rectangle()) // 确保整个区域都可以点击
                
                // 可视化的按钮内容
                ZStack {
                    // 按钮背景 - 选中时显示白色半透明背景
                    RoundedRectangle(cornerRadius: config.tabCornerRadius)
                        .fill(
                            isSelected ? 
                            Color.white.opacity(config.selectedBackgroundOpacity) : 
                            Color.clear.opacity(config.unselectedBackgroundOpacity)
                        )
                        .shadow(
                            color: isSelected ? 
                            DesignSystem.SubscriptionPage.MembershipTab.activeShadowColor.opacity(config.shadowOpacity) : 
                            Color.clear,
                            radius: isSelected ? config.shadowRadius : 0,
                            x: config.shadowOffsetX,
                            y: isSelected ? config.shadowOffsetY : 0
                        )
                        .animation(.easeInOut(duration: config.animationDuration), value: isSelected)
                    
                    // 按钮文字
                    Text(title)
                        .font(.system(
                            size: config.textFontSize,
                            weight: DesignSystem.SubscriptionPage.MembershipTab.fontWeight
                        ))
                        .foregroundColor(
                            isSelected ? 
                            DesignSystem.SubscriptionPage.MembershipTab.inactiveTextColor.opacity(config.selectedTextOpacity) : 
                            DesignSystem.SubscriptionPage.MembershipTab.activeTextColor.opacity(config.unselectedTextOpacity)
                        )
                        .shadow(
                            color: Color.clear, // 完全移除文字阴影
                            radius: 0,
                            x: 0,
                            y: 0
                        )
                        .animation(.easeInOut(duration: 0.2), value: isSelected)
                }
                .frame(width: buttonWidth, height: config.tabHeight)
                .scaleEffect(buttonPressedStates[index] ? config.pressedScale : 1.0)
                .animation(.spring(
                    response: config.animationSpringResponse,
                    dampingFraction: config.animationSpringDamping
                ), value: buttonPressedStates[index])
            }
        }
        .buttonStyle(PlainButtonStyle())
        .simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    buttonPressedStates[index] = true
                }
                .onEnded { _ in
                    buttonPressedStates[index] = false
                }
        )
    }
    
    /**
     * 执行选项卡切换动画
     */
    private func animateTabSelection(to index: Int) {
        guard selectedTab != index else { return }
        
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            selectedTab = index
        }
        
        // 触发回调
        onTabChanged(index)
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State var selectedTab = 1
        
        var body: some View {
            ZStack {
                // 模拟背景图
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#e7f7c4"),
                        Color(hex: "#d4f1a8")
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 40) {
                    Spacer()
                    
                    // 分段选项卡
                    MembershipTabSegment(selectedTab: $selectedTab) { newTab in
                        print("切换到: \(newTab == 0 ? "初级会员" : "高级会员")")
                    }
                    .frame(height: 44)
                    .padding(.horizontal, 60)
                    
                    // 显示当前选中状态
                    Text("tab.current_selection".localized(with: selectedTab == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized))
                        .font(.title2)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
            }
        }
    }
    
    return PreviewWrapper()
}