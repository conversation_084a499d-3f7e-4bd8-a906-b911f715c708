//
//  SubscriptionSuccessModal.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/16.
//

import SwiftUI

/**
 * 订阅成功弹窗组件
 * 显示订阅成功信息，用户点击确认后返回个人中心
 */
struct SubscriptionSuccessModal: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    let onConfirm: () -> Void
    
    // MARK: - State
    @State private var modalAppeared = false
    @State private var iconScale: CGFloat = 0.5
    @State private var iconRotation: Double = 0
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景不关闭弹窗，确保用户看到成功信息
                }
            
            // 弹窗内容
            VStack(spacing: 0) {
                modalContent
                    .background(modalBackground)
                    .cornerRadius(20)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    .scaleEffect(modalAppeared ? 1.0 : 0.8)
                    .opacity(modalAppeared ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: modalAppeared)
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            withAnimation {
                modalAppeared = true
            }
            
            // 成功图标动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.3)) {
                iconScale = 1.0
            }
            
            withAnimation(.easeInOut(duration: 0.5).delay(0.5)) {
                iconRotation = 360
            }
        }
    }
    
    // MARK: - Modal Content
    private var modalContent: some View {
        VStack(spacing: 24) {
            // 顶部装饰
            topDecoration
            
            // 成功图标
            successIcon
            
            // 标题和描述
            textContent
            
            // 确认按钮
            confirmButton
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 32)
    }
    
    // MARK: - Top Decoration
    private var topDecoration: some View {
        HStack {
            Spacer()
            
            // 装饰性小图标
            HStack(spacing: 8) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(Color(hex: "#a9d051").opacity(0.3))
                        .frame(width: 6, height: 6)
                        .scaleEffect(modalAppeared ? 1.0 : 0.0)
                        .animation(.spring(response: 0.5, dampingFraction: 0.8).delay(Double(index) * 0.1 + 0.4), value: modalAppeared)
                }
            }
        }
    }
    
    // MARK: - Success Icon
    private var successIcon: some View {
        ZStack {
            // 背景圆圈
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#a9d051").opacity(0.2),
                            Color(hex: "#8bc34a").opacity(0.1)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .scaleEffect(modalAppeared ? 1.0 : 0.5)
                .animation(.spring(response: 0.7, dampingFraction: 0.6).delay(0.2), value: modalAppeared)
            
            // 成功图标
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 40, weight: .medium))
                .foregroundColor(Color(hex: "#a9d051"))
                .scaleEffect(iconScale)
                .rotationEffect(.degrees(iconRotation))
        }
    }
    
    // MARK: - Text Content
    private var textContent: some View {
        VStack(spacing: 12) {
            // 标题
            Text("purchase.success".localized)
                .font(.system(size: 22, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .opacity(modalAppeared ? 1.0 : 0.0)
                .offset(y: modalAppeared ? 0 : 20)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: modalAppeared)

            // 描述
            Text("subscription.success.message".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
                .opacity(modalAppeared ? 1.0 : 0.0)
                .offset(y: modalAppeared ? 0 : 20)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.7), value: modalAppeared)
        }
    }
    
    // MARK: - Confirm Button
    private var confirmButton: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            // 关闭弹窗
            withAnimation(.easeInOut(duration: 0.3)) {
                modalAppeared = false
            }
            
            // 延迟执行回调
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                onConfirm()
            }
        }) {
            HStack(spacing: 8) {
                Text("common.button.confirm".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)

                Image(systemName: "arrow.right")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#a9d051"),
                        Color(hex: "#8bc34a")
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(25)
            .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .scaleEffect(modalAppeared ? 1.0 : 0.8)
        .opacity(modalAppeared ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.8), value: modalAppeared)
    }
    
    // MARK: - Modal Background
    private var modalBackground: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.white,
                Color(hex: "#fafafa")
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

// MARK: - Preview
#Preview {
    SubscriptionSuccessModal(
        isPresented: .constant(true),
        onConfirm: {
            print("确认按钮被点击")
        }
    )
    .background(Color.gray.opacity(0.3))
}
