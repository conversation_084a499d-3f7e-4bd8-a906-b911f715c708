//
//  ProfileView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import SwiftUI
import RevenueCat

/**
 * 个人中心主视图
 * 包含个人信息、订阅标签和系统设置三个主要部分
 */
struct ProfileView: View {
    
    // MARK: - Dependencies
    @EnvironmentObject var authManager: AuthenticationManager
    
    // MARK: - State
    @State private var pageAppeared = false
    @State private var showSubscriptionView = false // 控制订阅页面显示
    @State private var showLanguageSelection = false // 控制语言选择页面显示
    @State private var showLogoutConfirmation = false // 控制退出登录确认弹窗

    @State private var showFeedbackAlert = false // 控制帮助与反馈弹窗
    @State private var showAboutView = false // 控制关于页面显示
    
    // 删除账号相关状态
    @State private var showDeleteAccountWarning = false // 控制删除账号警告弹窗
    @State private var showDeleteAccountConfirmation = false // 控制删除账号确认弹窗
    @State private var showDeleteAccountProgress = false // 控制删除进度显示
    @State private var deleteConfirmationText = "" // 用户输入的确认文本
    @State private var deletionError: Error? // 删除错误信息

    // 试用期提醒弹窗状态
    @State private var showTrialReminderModal = false // 控制试用期订阅提醒弹窗
    
    // 添加CloudKit订阅管理器
    @StateObject private var subscriptionManager = CloudKitSubscriptionManager.shared

    // 添加账号删除管理器
    @StateObject private var accountDeletionManager = AccountDeletionManager.shared

    // 添加试用管理器和订阅管理器
    @StateObject private var trialManager = TrialManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    
    // MARK: - Computed Properties
    private var userName: String {
        return authManager.currentUser?.name ?? "user_info.parent_nickname".localized
    }
    
    private var userID: String {
        return authManager.getUserEmail() ?? "profile.no_email".localized
    }
    
    private var membershipLevel: String {
        // 优先检查试用状态
        if trialManager.isTrialActive {
            return "subscription.membership.premium_trial".localized
        }

        // 检查RevenueCat订阅状态
        switch revenueCatManager.currentSubscriptionLevel {
        case .basic:
            return "subscription.membership.basic".localized
        case .premium:
            return "subscription.membership.premium".localized
        case .free:
            return "subscription.user_level_regular".localized
        }
    }

    private var expirationDate: String {
        // 优先检查试用到期时间
        if trialManager.isTrialActive, let trialExpiration = trialManager.trialExpirationDate {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年MM月dd日"
            return formatter.string(from: trialExpiration)
        }

        // 检查RevenueCat订阅到期时间
        if let revenueCatExpiration = revenueCatManager.expirationDate {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年MM月dd日"
            return formatter.string(from: revenueCatExpiration)
        }

        return "subscription.not_activated".localized
    }
    
    var body: some View {
        ZStack {
            // 美化背景渐变 - 与其他页面保持一致的风格
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)
            
            // 装饰性背景元素
            VStack {
                HStack {
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 100, height: 100)
                        .offset(x: -30, y: 20)
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -10)
                }
                Spacer()
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.02))
                        .frame(width: 80, height: 80)
                        .offset(x: 20, y: 30)
                }
            }
            
            // 主要内容区域 - 自适应布局
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 个人信息组件 - 紧贴屏幕顶部和两侧，使用屏幕高度百分比
                    UserInfoSection(
                        userName: userName,
                        userID: userID,
                        membershipLevel: membershipLevel,
                        expirationDate: expirationDate
                    )
                    .frame(height: geometry.size.height * 0.25) // 使用屏幕高度的35%
                    .frame(maxWidth: .infinity) // 紧贴屏幕两侧
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 50)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: pageAppeared)
                    
                    // 订阅标签组件 - 叠加在个人信息组件底部
                    SubscriptionBannerSection {
                        handleViewPlansPressed()
                    }
                    .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                    .offset(y: -30) // 叠加效果：向上偏移30pt
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: pageAppeared)
                    
                    // 可滚动内容区域
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(spacing: 0) {
                            // 顶部空间给订阅标签预留
                            Spacer()
                                .frame(height: 0)
                            
                            // 系统设置组件
                            SystemSettingsSection { settingType in
                                handleSettingItemPressed(settingType)
                            }
                            .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                            .padding(.top, 50) // 为订阅标签留出空间
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? -50 : -10) // 向上移动50个点
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: pageAppeared)
                            
                            // 底部空白区域，确保内容不被导航栏遮挡
                            Spacer()
                                .frame(height: DesignSystem.LiquidTabBar.height + 40)
                        }
                    }
                }
            }
        }
        .onAppear {
            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
            // 加载用户数据（为后续功能预留）
            loadUserData()
        }
        .fullScreenCover(isPresented: $showSubscriptionView, onDismiss: {
            // 从订阅页面返回时重新加载用户数据，确保显示最新的订阅状态
            loadUserData()
        }) {
            SubscriptionView()
        }
        .fullScreenCover(isPresented: $showLanguageSelection) {
            LanguageSelectionView()
        }
        .sheet(isPresented: $showAboutView) {
            AboutView()
        }
        .alert("logout.title".localized, isPresented: $showLogoutConfirmation) {
            Button("logout.cancel".localized, role: .cancel) {
                // 取消操作，不做任何事
            }
            Button("logout.confirm".localized, role: .destructive) {
                // 执行退出登录操作，添加触觉反馈
                let feedbackGenerator = UINotificationFeedbackGenerator()
                feedbackGenerator.notificationOccurred(.success)
                
                // 添加过渡效果
                withAnimation(.easeInOut(duration: 0.3)) {
                    authManager.logout()
                }
            }
        } message: {
            Text("logout.message".localized)
        }

        .alert("feedback.contact_email.title".localized, isPresented: $showFeedbackAlert) {
            Button("feedback.contact_email.confirm".localized, role: .cancel) {
                // 确定按钮，关闭弹窗
            }
        } message: {
            Text("feedback.contact_email.message".localized)
        }
        
        // 删除账号警告弹窗
        .alert("account_deletion.warning.title".localized, isPresented: $showDeleteAccountWarning) {
            Button("account_deletion.cancel_button".localized, role: .cancel) {
                // 取消删除
            }
            Button("account_deletion.confirm_button".localized, role: .destructive) {
                // 显示确认弹窗
                showDeleteAccountConfirmation = true
            }
        } message: {
            Text("account_deletion.warning.message".localized)
        }
        
        // 删除账号确认弹窗
        .sheet(isPresented: $showDeleteAccountConfirmation) {
            DeleteAccountConfirmationView(
                confirmationText: $deleteConfirmationText,
                onConfirm: {
                    startAccountDeletion()
                },
                onCancel: {
                    resetDeleteAccountState()
                }
            )
        }


        
        // 删除进度显示
        .overlay(
            showDeleteAccountProgress ? 
            AccountDeletionProgressView(
                deletionManager: accountDeletionManager,
                onCancel: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                },
                onComplete: {
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                    
                    // 删除完成后跳转到登录页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        authManager.logout()
                    }
                },
                onError: { error in
                    deletionError = error
                    showDeleteAccountProgress = false
                    resetDeleteAccountState()
                }
            ) : nil
        )

        // 试用期订阅提醒弹窗覆盖层
        .overlay(
            showTrialReminderModal ?
            TrialSubscriptionReminderModal(isPresented: $showTrialReminderModal) {
                // 用户点击"谢谢提醒"后，关闭弹窗并跳转到订阅页面
                print("🔔 用户确认试用期提醒，跳转到订阅页面")
                showSubscriptionView = true
            } : nil
        )

    }
    
    // MARK: - Action Handlers
    
    /**
     * 处理查看会员方案按钮点击
     */
    private func handleViewPlansPressed() {
        print("查看会员方案功能")

        // 检查用户是否处于试用期内
        if trialManager.isTrialActive {
            print("🔔 用户处于试用期内，显示温馨提醒")
            // 显示试用期提醒弹窗
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showTrialReminderModal = true
            }
        } else {
            // 直接跳转到订阅页面
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showSubscriptionView = true
            }
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 处理设置项点击
     */
    private func handleSettingItemPressed(_ settingType: SettingType) {
        print("设置项被点击: \(settingType.displayName)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }
        
        switch settingType {
        case .language:
            // 显示语言选择页面
            showLanguageSelection = true
            print("显示语言选择页面")
        case .feedback:
            // 显示帮助与反馈弹窗
            showFeedbackAlert = true
            print("显示帮助与反馈弹窗")
        case .about:
            // 显示关于页面
            showAboutView = true
            print("显示关于页面")
        case .deleteAccount:
            // 显示删除账号警告弹窗
            showDeleteAccountWarning = true
            print("显示删除账号警告弹窗")
        case .logout:
            // 显示退出登录确认弹窗
            showLogoutConfirmation = true
        }
    }
    
    /**
     * 处理退出登录
     * 可作为替代方案使用，当前在alert回调中直接调用logout方法
     */
    private func handleLogout() {
        print("用户确认退出登录")
        
        // 触觉反馈
        let feedbackGenerator = UINotificationFeedbackGenerator()
        feedbackGenerator.notificationOccurred(.success)
        
        // 添加过渡动画
        withAnimation(.easeInOut(duration: 0.3)) {
            authManager.logout()
        }
    }
    
    /**
     * 加载用户数据
     * 在页面出现时调用，用于加载和刷新用户信息
     */
    private func loadUserData() {
        // 此处可以添加额外的用户数据加载逻辑
        // 例如从网络或本地数据库获取最新的用户信息
        print("加载用户数据：\(userName)")

        // 刷新订阅信息 - 确保从订阅页面返回时显示最新状态
        trialManager.refreshTrialStatus()

        // 触发订阅状态检查
        Task {
            // 通过重新获取客户信息来刷新RevenueCat状态
            try? await Purchases.shared.customerInfo()
        }
    }
    
    /**
     * 开始账号删除流程
     */
    private func startAccountDeletion() {
        print("🗑️ 开始账号删除流程")
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        // 显示进度界面
        showDeleteAccountProgress = true
        
        // 开始删除操作
        accountDeletionManager.deleteCurrentUserAccount { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    print("✅ 账号删除成功")
                    // 成功删除后会自动退出登录
                case .failure(let error):
                    print("❌ 账号删除失败: \(error)")
                    self.deletionError = error
                }
            }
        }
    }
    
    /**
     * 重置删除账号状态
     */
    private func resetDeleteAccountState() {
        showDeleteAccountWarning = false
        showDeleteAccountConfirmation = false
        showDeleteAccountProgress = false
        deleteConfirmationText = ""
        deletionError = nil
    }
    

}

// MARK: - Preview
#Preview {
    ProfileView()
        .environmentObject(AuthenticationManager())
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}