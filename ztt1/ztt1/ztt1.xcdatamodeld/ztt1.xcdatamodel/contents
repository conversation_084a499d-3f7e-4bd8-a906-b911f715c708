<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="true" userDefinedModelVersionIdentifier="">
    <!-- User实体 -->
    <entity name="User" representedClassName="User" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="nickname" optional="YES" attributeType="String"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="appleUserID" optional="YES" attributeType="String"/>
        <attribute name="lastLoginAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="preferredDateRangeType" optional="YES" attributeType="String" defaultValueString="thisMonth"/>
        <attribute name="customStartDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="customEndDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="subscription" optional="YES" maxCount="1" destinationEntity="Subscription" inverseName="user" inverseEntity="Subscription" deletionRule="Cascade"/>
        <relationship name="classes" optional="YES" toMany="YES" destinationEntity="SchoolClass" inverseName="owner" inverseEntity="SchoolClass" deletionRule="Cascade"/>
        <relationship name="families" optional="YES" toMany="YES" destinationEntity="Family" inverseName="owner" inverseEntity="Family" deletionRule="Cascade"/>
    </entity>

    <!-- Subscription实体 -->
    <entity name="Subscription" representedClassName="Subscription" syncable="YES" codeGenerationType="none">
        <attribute name="level" optional="YES" attributeType="String" defaultValueString="free"/>
        <attribute name="maxClasses" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="expirationDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="hasReceivedTrial" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" destinationEntity="User" inverseName="subscription" inverseEntity="User" deletionRule="Nullify"/>
    </entity>

    <!-- SchoolClass实体 -->
    <entity name="SchoolClass" representedClassName="SchoolClass" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="status" optional="YES" attributeType="String" defaultValueString="active"/>
        <relationship name="owner" optional="YES" maxCount="1" destinationEntity="User" inverseName="classes" inverseEntity="User" deletionRule="Nullify"/>
        <relationship name="students" optional="YES" toMany="YES" destinationEntity="Student" inverseName="schoolClass" inverseEntity="Student" deletionRule="Cascade"/>
        <relationship name="rules" optional="YES" toMany="YES" destinationEntity="Rule" inverseName="schoolClass" inverseEntity="Rule" deletionRule="Cascade"/>
        <relationship name="prizes" optional="YES" toMany="YES" destinationEntity="Prize" inverseName="schoolClass" inverseEntity="Prize" deletionRule="Cascade"/>
        <relationship name="lotteryConfigs" optional="YES" toMany="YES" destinationEntity="LotteryToolConfig" inverseName="schoolClass" inverseEntity="LotteryToolConfig" deletionRule="Cascade"/>
    </entity>

    <!-- Student实体 -->
    <entity name="Student" representedClassName="Student" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="studentNumber" optional="YES" attributeType="String"/>
        <attribute name="gender" optional="YES" attributeType="String" defaultValueString="male"/>
        <attribute name="point" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="schoolClass" optional="YES" maxCount="1" destinationEntity="SchoolClass" inverseName="students" inverseEntity="SchoolClass" deletionRule="Nullify"/>
        <relationship name="pointRecords" optional="YES" toMany="YES" destinationEntity="PointRecord" inverseName="student" inverseEntity="PointRecord" deletionRule="Cascade"/>
        <relationship name="redemptionRecords" optional="YES" toMany="YES" destinationEntity="RedemptionRecord" inverseName="student" inverseEntity="RedemptionRecord" deletionRule="Cascade"/>
        <relationship name="lotteryRecords" optional="YES" toMany="YES" destinationEntity="LotteryRecord" inverseName="student" inverseEntity="LotteryRecord" deletionRule="Cascade"/>
        <relationship name="aiReports" optional="YES" toMany="YES" destinationEntity="AIReport" inverseName="student" inverseEntity="AIReport" deletionRule="Cascade"/>
    </entity>

    <!-- PointRecord实体 -->
    <entity name="PointRecord" representedClassName="PointRecord" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="reason" optional="YES" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="isReversed" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="student" optional="YES" maxCount="1" destinationEntity="Student" inverseName="pointRecords" inverseEntity="Student" deletionRule="Nullify"/>
    </entity>

    <!-- Prize实体 -->
    <entity name="Prize" representedClassName="Prize" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="cost" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String" defaultValueString="虚拟"/>
        <relationship name="schoolClass" optional="YES" maxCount="1" destinationEntity="SchoolClass" inverseName="prizes" inverseEntity="SchoolClass" deletionRule="Nullify"/>
    </entity>

    <!-- RedemptionRecord实体 -->
    <entity name="RedemptionRecord" representedClassName="RedemptionRecord" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="prizeName" optional="YES" attributeType="String"/>
        <attribute name="cost" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="source" optional="YES" attributeType="String" defaultValueString="redemption"/>
        <relationship name="student" optional="YES" maxCount="1" destinationEntity="Student" inverseName="redemptionRecords" inverseEntity="Student" deletionRule="Nullify"/>
    </entity>

    <!-- LotteryRecord实体 -->
    <entity name="LotteryRecord" representedClassName="LotteryRecord" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="toolType" optional="YES" attributeType="String" defaultValueString="大转盘"/>
        <attribute name="prizeResult" optional="YES" attributeType="String"/>
        <attribute name="cost" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="student" optional="YES" maxCount="1" destinationEntity="Student" inverseName="lotteryRecords" inverseEntity="Student" deletionRule="Nullify"/>
    </entity>

    <!-- Rule实体 -->
    <entity name="Rule" representedClassName="Rule" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String" defaultValueString="add"/>
        <attribute name="isFrequent" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="schoolClass" optional="YES" maxCount="1" destinationEntity="SchoolClass" inverseName="rules" inverseEntity="SchoolClass" deletionRule="Nullify"/>
    </entity>

    <!-- RuleTemplate实体 -->
    <entity name="RuleTemplate" representedClassName="RuleTemplate" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String" defaultValueString="add"/>
    </entity>

    <!-- PrizeTemplate实体 -->
    <entity name="PrizeTemplate" representedClassName="PrizeTemplate" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="cost" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String" defaultValueString="虚拟"/>
    </entity>

    <!-- LotteryToolConfig实体 -->
    <entity name="LotteryToolConfig" representedClassName="LotteryToolConfig" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="toolType" optional="YES" attributeType="String" defaultValueString="大转盘"/>
        <attribute name="itemCount" attributeType="Integer 32" defaultValueString="12" usesScalarValueType="YES"/>
        <attribute name="costPerPlay" attributeType="Integer 32" defaultValueString="5" usesScalarValueType="YES"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="schoolClass" optional="YES" maxCount="1" destinationEntity="SchoolClass" inverseName="lotteryConfigs" inverseEntity="SchoolClass" deletionRule="Nullify"/>
        <relationship name="items" optional="YES" toMany="YES" destinationEntity="LotteryToolItem" inverseName="lotteryConfig" inverseEntity="LotteryToolItem" deletionRule="Cascade"/>
    </entity>

    <!-- LotteryToolItem实体 -->
    <entity name="LotteryToolItem" representedClassName="LotteryToolItem" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="itemIndex" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="prizeName" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="lotteryConfig" optional="YES" maxCount="1" destinationEntity="LotteryToolConfig" inverseName="items" inverseEntity="LotteryToolConfig" deletionRule="Nullify"/>
    </entity>

    <!-- Family实体 - 家庭版新增 -->
    <entity name="Family" representedClassName="Family" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="totalPoints" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="owner" optional="YES" maxCount="1" destinationEntity="User" inverseName="families" inverseEntity="User" deletionRule="Nullify"/>
        <relationship name="members" optional="YES" toMany="YES" destinationEntity="FamilyMember" inverseName="family" inverseEntity="FamilyMember" deletionRule="Cascade"/>
        <relationship name="rules" optional="YES" toMany="YES" destinationEntity="FamilyRule" inverseName="family" inverseEntity="FamilyRule" deletionRule="Cascade"/>
        <relationship name="prizes" optional="YES" toMany="YES" destinationEntity="FamilyPrize" inverseName="family" inverseEntity="FamilyPrize" deletionRule="Cascade"/>
    </entity>

    <!-- FamilyMember实体 - 家庭成员 -->
    <entity name="FamilyMember" representedClassName="FamilyMember" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="role" optional="YES" attributeType="String" defaultValueString="other"/>
        <attribute name="gender" optional="YES" attributeType="String" defaultValueString="male"/>
        <attribute name="age" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="currentPoints" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="avatar" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="family" optional="YES" maxCount="1" destinationEntity="Family" inverseName="members" inverseEntity="Family" deletionRule="Nullify"/>
        <relationship name="pointRecords" optional="YES" toMany="YES" destinationEntity="FamilyPointRecord" inverseName="member" inverseEntity="FamilyPointRecord" deletionRule="Cascade"/>
        <relationship name="diaryEntries" optional="YES" toMany="YES" destinationEntity="DiaryEntry" inverseName="member" inverseEntity="DiaryEntry" deletionRule="Cascade"/>
        <relationship name="redemptionRecords" optional="YES" toMany="YES" destinationEntity="FamilyRedemptionRecord" inverseName="member" inverseEntity="FamilyRedemptionRecord" deletionRule="Cascade"/>
        <relationship name="lotteryRecords" optional="YES" toMany="YES" destinationEntity="FamilyLotteryRecord" inverseName="member" inverseEntity="FamilyLotteryRecord" deletionRule="Cascade"/>
        <relationship name="aiReports" optional="YES" toMany="YES" destinationEntity="AIReport" inverseName="member" inverseEntity="AIReport" deletionRule="Cascade"/>
        <relationship name="memberRules" optional="YES" toMany="YES" destinationEntity="FamilyMemberRule" inverseName="member" inverseEntity="FamilyMemberRule" deletionRule="Cascade"/>
    </entity>

    <!-- DiaryEntry实体 - 成长日记 -->
    <entity name="DiaryEntry" representedClassName="DiaryEntry" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="content" optional="YES" attributeType="String"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="isVisible" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" destinationEntity="FamilyMember" inverseName="diaryEntries" inverseEntity="FamilyMember" deletionRule="Nullify"/>
    </entity>

    <!-- AIReport实体 - AI分析报告 -->
    <entity name="AIReport" representedClassName="AIReport" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="content" optional="YES" attributeType="String"/>
        <attribute name="reportType" optional="YES" attributeType="String" defaultValueString="professional"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="inputDataSummary" optional="YES" attributeType="String"/>
        <attribute name="studentName" optional="YES" attributeType="String"/>
        <attribute name="totalRecords" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="positiveRecords" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="negativeRecords" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="member" optional="YES" maxCount="1" destinationEntity="FamilyMember" inverseName="aiReports" inverseEntity="FamilyMember" deletionRule="Nullify"/>
        <relationship name="student" optional="YES" maxCount="1" destinationEntity="Student" inverseName="aiReports" inverseEntity="Student" deletionRule="Nullify"/>
    </entity>

    <!-- FamilyPointRecord实体 - 家庭成员积分记录 -->
    <entity name="FamilyPointRecord" representedClassName="FamilyPointRecord" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="reason" optional="YES" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="isReversed" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="member" optional="YES" maxCount="1" destinationEntity="FamilyMember" inverseName="pointRecords" inverseEntity="FamilyMember" deletionRule="Nullify"/>
    </entity>

    <!-- FamilyRule实体 - 家庭规则 -->
    <entity name="FamilyRule" representedClassName="FamilyRule" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String" defaultValueString="add"/>
        <attribute name="isFrequent" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="family" optional="YES" maxCount="1" destinationEntity="Family" inverseName="rules" inverseEntity="Family" deletionRule="Nullify"/>
    </entity>

    <!-- FamilyPrize实体 - 家庭奖品 -->
    <entity name="FamilyPrize" representedClassName="FamilyPrize" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="cost" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String" defaultValueString="虚拟"/>
        <relationship name="family" optional="YES" maxCount="1" destinationEntity="Family" inverseName="prizes" inverseEntity="Family" deletionRule="Nullify"/>
    </entity>

    <!-- FamilyRedemptionRecord实体 - 家庭兑换记录 -->
    <entity name="FamilyRedemptionRecord" representedClassName="FamilyRedemptionRecord" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="prizeName" optional="YES" attributeType="String"/>
        <attribute name="cost" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="source" optional="YES" attributeType="String" defaultValueString="redemption"/>
        <relationship name="member" optional="YES" maxCount="1" destinationEntity="FamilyMember" inverseName="redemptionRecords" inverseEntity="FamilyMember" deletionRule="Nullify"/>
    </entity>

    <!-- FamilyLotteryRecord实体 - 家庭抽奖记录 -->
    <entity name="FamilyLotteryRecord" representedClassName="FamilyLotteryRecord" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="toolType" optional="YES" attributeType="String" defaultValueString="大转盘"/>
        <attribute name="prizeResult" optional="YES" attributeType="String"/>
        <attribute name="cost" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" destinationEntity="FamilyMember" inverseName="lotteryRecords" inverseEntity="FamilyMember" deletionRule="Nullify"/>
    </entity>

    <!-- FamilyMemberRule实体 - 家庭成员独立规则 -->
    <entity name="FamilyMemberRule" representedClassName="FamilyMemberRule" syncable="YES" codeGenerationType="none">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="value" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String" defaultValueString="add"/>
        <attribute name="isFrequent" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" destinationEntity="FamilyMember" inverseName="memberRules" inverseEntity="FamilyMember" deletionRule="Nullify"/>
    </entity>

    <elements>
        <element name="User" positionX="-63" positionY="-18" width="128" height="119"/>
        <element name="Subscription" positionX="-54" positionY="-9" width="128" height="89"/>
        <element name="SchoolClass" positionX="-36" positionY="9" width="128" height="149"/>
        <element name="Student" positionX="-18" positionY="27" width="128" height="209"/>
        <element name="PointRecord" positionX="0" positionY="45" width="128" height="134"/>
        <element name="Prize" positionX="18" positionY="63" width="128" height="104"/>
        <element name="RedemptionRecord" positionX="36" positionY="81" width="128" height="104"/>
        <element name="LotteryRecord" positionX="54" positionY="99" width="128" height="119"/>
        <element name="Rule" positionX="72" positionY="117" width="128" height="119"/>
        <element name="RuleTemplate" positionX="90" positionY="135" width="128" height="89"/>
        <element name="PrizeTemplate" positionX="108" positionY="153" width="128" height="89"/>
        <element name="LotteryToolConfig" positionX="126" positionY="171" width="128" height="149"/>
        <element name="LotteryToolItem" positionX="144" positionY="189" width="128" height="104"/>
        <element name="Family" positionX="200" positionY="0" width="128" height="149"/>
        <element name="FamilyMember" positionX="220" positionY="20" width="128" height="209"/>
        <element name="DiaryEntry" positionX="240" positionY="40" width="128" height="134"/>
        <element name="AIReport" positionX="260" positionY="60" width="128" height="179"/>
        <element name="FamilyPointRecord" positionX="280" positionY="80" width="128" height="134"/>
        <element name="FamilyRule" positionX="300" positionY="100" width="128" height="119"/>
        <element name="FamilyPrize" positionX="320" positionY="120" width="128" height="104"/>
        <element name="FamilyRedemptionRecord" positionX="340" positionY="140" width="128" height="134"/>
        <element name="FamilyLotteryRecord" positionX="360" positionY="160" width="128" height="134"/>
        <element name="FamilyMemberRule" positionX="380" positionY="180" width="128" height="149"/>
    </elements>
</model>