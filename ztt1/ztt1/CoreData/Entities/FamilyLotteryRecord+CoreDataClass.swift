//
//  FamilyLotteryRecord+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(FamilyLotteryRecord)
public class FamilyLotteryRecord: NSManagedObject {
    
    // MARK: - Enums
    
    enum LotteryToolType: String, CaseIterable {
        case wheel = "大转盘"
        case blindBox = "盲盒"
        case scratchCard = "刮刮卡"
        
        var displayName: String {
            switch self {
            case .wheel:
                return "family.lottery.tool.wheel".localized
            case .blindBox:
                return "family.lottery.tool.blind_box".localized
            case .scratchCard:
                return "family.lottery.tool.scratch_card".localized
            }
        }
        
        var icon: String {
            switch self {
            case .wheel:
                return "circle.grid.cross.fill"
            case .blindBox:
                return "shippingbox.fill"
            case .scratchCard:
                return "rectangle.fill"
            }
        }
        
        var color: String {
            switch self {
            case .wheel:
                return "#FF9500"
            case .blindBox:
                return "#007AFF"
            case .scratchCard:
                return "#FF2D92"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取抽奖工具类型枚举
     */
    var lotteryToolType: LotteryToolType {
        return LotteryToolType(rawValue: toolType ?? "大转盘") ?? .wheel
    }
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示日期
     */
    var formattedDate: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示积分消耗
     */
    var formattedCost: String {
        return "\(cost) 积分"
    }
    
    /**
     * 抽奖记录描述
     */
    var recordDescription: String {
        let toolDesc = lotteryToolType.displayName
        let result = prizeResult ?? "未知奖品"
        return "\(toolDesc): \(result) (\(formattedCost))"
    }
    
    /**
     * 是否中奖
     */
    var isWinning: Bool {
        guard let result = prizeResult else { return false }
        // 简单判断：如果结果不是"谢谢参与"或"再来一次"等，就算中奖
        let noWinResults = ["谢谢参与", "再来一次", "未中奖", "空奖"]
        return !noWinResults.contains(result)
    }
    
    /**
     * 是否为今天的记录
     */
    var isToday: Bool {
        guard let timestamp = timestamp else { return false }
        return Calendar.current.isDateInToday(timestamp)
    }
    
    /**
     * 是否为本周的记录
     */
    var isThisWeek: Bool {
        guard let timestamp = timestamp else { return false }
        let calendar = Calendar.current
        let now = Date()
        let weekOfYear = calendar.component(.weekOfYear, from: now)
        let year = calendar.component(.year, from: now)
        let recordWeekOfYear = calendar.component(.weekOfYear, from: timestamp)
        let recordYear = calendar.component(.year, from: timestamp)
        
        return weekOfYear == recordWeekOfYear && year == recordYear
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新抽奖记录的便利方法
     */
    @discardableResult
    static func create(
        toolType: String,
        prizeResult: String,
        cost: Int,
        for member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> FamilyLotteryRecord {
        let record = FamilyLotteryRecord(context: context)
        record.id = UUID()
        record.toolType = toolType
        record.prizeResult = prizeResult
        record.cost = Int32(cost)
        record.timestamp = Date()
        record.member = member
        return record
    }
    
    /**
     * 检查是否可以查看
     */
    func canView(by currentMember: FamilyMember?) -> Bool {
        // 所有家庭成员都可以查看抽奖记录
        return true
    }
    
    /**
     * 检查是否可以删除
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 记录者本人可以删除
        if currentMember.id == member?.id {
            return true
        }
        
        // 父母可以删除所有抽奖记录
        if currentMember.isParent {
            return true
        }
        
        return false
    }
    
    /**
     * 删除抽奖记录并返还积分
     */
    func deleteAndRefund(in context: NSManagedObjectContext) {
        // 返还积分给成员
        if let member = member {
            member.currentPoints += cost
            member.updatedAt = Date()
            
            // 更新家庭总积分
            member.family?.updateTotalPoints()
        }
        
        // 删除记录
        context.delete(self)
    }
    
    /**
     * 获取中奖状态描述
     */
    var winningStatusDescription: String {
        if isWinning {
            return "🎉 中奖"
        } else {
            return "😔 未中奖"
        }
    }
}

// MARK: - CoreData Properties Extension

extension FamilyLotteryRecord {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<FamilyLotteryRecord> {
        return NSFetchRequest<FamilyLotteryRecord>(entityName: "FamilyLotteryRecord")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var toolType: String?
    @NSManaged public var prizeResult: String?
    @NSManaged public var cost: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var member: FamilyMember?
}

// MARK: - HistoryRecordProtocol Implementation

extension FamilyLotteryRecord: HistoryRecordProtocol {
    
    /// 记录唯一标识
    var recordId: UUID? {
        return id
    }
    
    /// 记录显示名称
    var displayName: String {
        return "\(lotteryToolType.displayName): \(prizeResult ?? "未知结果")"
    }
    
    /// 积分数值（抽奖记录为负值）
    var pointsValue: Int {
        return -Int(cost)
    }
    
    /// 记录类型
    var recordType: HistoryRecordType {
        switch lotteryToolType {
        case .wheel:
            return .wheelLottery
        case .blindBox:
            return .boxLottery
        case .scratchCard:
            return .scratchLottery
        }
    }
    
    /// 记录时间戳
    var recordTimestamp: Date? {
        return timestamp
    }
    
    /// 是否可以删除
    var canDelete: Bool {
        return true
    }
    
    /// 删除记录的积分影响描述
    var deleteImpactDescription: String {
        return "删除后将返还 \(cost) 积分"
    }
}
