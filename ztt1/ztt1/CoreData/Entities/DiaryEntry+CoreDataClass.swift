//
//  DiaryEntry+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(DiaryEntry)
public class DiaryEntry: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        formatter.locale = Locale.current
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示日期
     */
    var formattedDate: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter.string(from: timestamp)
    }
    
    /**
     * 日记内容预览（限制字数）
     */
    var contentPreview: String {
        guard let content = content else { return "" }
        let maxLength = 50
        if content.count <= maxLength {
            return content
        }
        let index = content.index(content.startIndex, offsetBy: maxLength)
        return String(content[..<index]) + "..."
    }
    
    /**
     * 日记字数统计
     */
    var wordCount: Int {
        return content?.count ?? 0
    }
    
    /**
     * 是否为今天的日记
     */
    var isToday: Bool {
        guard let timestamp = timestamp else { return false }
        return Calendar.current.isDateInToday(timestamp)
    }
    
    /**
     * 是否为本周的日记
     */
    var isThisWeek: Bool {
        guard let timestamp = timestamp else { return false }
        let calendar = Calendar.current
        let now = Date()
        let weekOfYear = calendar.component(.weekOfYear, from: now)
        let year = calendar.component(.year, from: now)
        let entryWeekOfYear = calendar.component(.weekOfYear, from: timestamp)
        let entryYear = calendar.component(.year, from: timestamp)
        
        return weekOfYear == entryWeekOfYear && year == entryYear
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新日记条目的便利方法
     * 注意：根据需求，成长日记默认对所有人可见，但仅记录者可删除
     */
    @discardableResult
    static func create(
        content: String,
        timestamp: Date = Date(),
        isVisible: Bool = true, // 默认所有人可见
        for member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> DiaryEntry {
        let entry = DiaryEntry(context: context)
        entry.id = UUID()
        entry.content = content
        entry.timestamp = timestamp
        entry.isVisible = true // 强制设置为所有人可见
        entry.createdAt = Date()
        entry.updatedAt = Date()
        entry.member = member
        return entry
    }
    
    /**
     * 更新日记内容
     */
    func updateContent(_ newContent: String) {
        content = newContent
        updatedAt = Date()
    }
    
    /**
     * 切换可见性
     */
    func toggleVisibility() {
        isVisible.toggle()
        updatedAt = Date()
    }
    
    /**
     * 检查是否可以编辑
     * 只有记录者本人或父母可以编辑
     */
    func canEdit(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 记录者本人可以编辑
        if currentMember.id == member?.id {
            return true
        }
        
        // 父母可以编辑所有日记
        if currentMember.isParent {
            return true
        }
        
        return false
    }
    
    /**
     * 检查是否可以查看
     * 注意：根据需求，成长日记对所有人可见
     */
    func canView(by currentMember: FamilyMember?) -> Bool {
        // 成长日记对所有家庭成员可见
        return true
    }

    /**
     * 检查是否可以删除
     * 注意：根据需求，仅记录者可删除日记
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }

        // 只有记录者本人可以删除
        return currentMember.id == member?.id
    }
    
    /**
     * 获取情绪关键词（简单的关键词检测）
     */
    func getEmotionKeywords() -> [String] {
        guard let content = content else { return [] }
        
        let positiveKeywords = ["开心", "高兴", "快乐", "兴奋", "满意", "骄傲", "感谢", "爱"]
        let negativeKeywords = ["难过", "伤心", "生气", "愤怒", "焦虑", "担心", "害怕", "失望"]
        
        var foundKeywords: [String] = []
        
        for keyword in positiveKeywords + negativeKeywords {
            if content.contains(keyword) {
                foundKeywords.append(keyword)
            }
        }
        
        return foundKeywords
    }
    
    /**
     * 获取日记的情绪倾向
     */
    func getEmotionTrend() -> EmotionTrend {
        let keywords = getEmotionKeywords()
        let positiveKeywords = ["开心", "高兴", "快乐", "兴奋", "满意", "骄傲", "感谢", "爱"]
        let negativeKeywords = ["难过", "伤心", "生气", "愤怒", "焦虑", "担心", "害怕", "失望"]
        
        let positiveCount = keywords.filter { positiveKeywords.contains($0) }.count
        let negativeCount = keywords.filter { negativeKeywords.contains($0) }.count
        
        if positiveCount > negativeCount {
            return .positive
        } else if negativeCount > positiveCount {
            return .negative
        } else {
            return .neutral
        }
    }
}

// MARK: - CoreData Properties Extension

extension DiaryEntry {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<DiaryEntry> {
        return NSFetchRequest<DiaryEntry>(entityName: "DiaryEntry")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var content: String?
    @NSManaged public var timestamp: Date?
    @NSManaged public var isVisible: Bool
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var member: FamilyMember?
}

// MARK: - Emotion Trend Enum

enum EmotionTrend {
    case positive
    case negative
    case neutral
    
    var displayName: String {
        switch self {
        case .positive:
            return "积极"
        case .negative:
            return "消极"
        case .neutral:
            return "中性"
        }
    }
    
    var color: String {
        switch self {
        case .positive:
            return "#26C34B"
        case .negative:
            return "#FF5B5B"
        case .neutral:
            return "#8E8E93"
        }
    }
}
