//
//  Prize+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(Prize)
public class Prize: NSManagedObject {
    
    // MARK: - Enums
    
    enum PrizeType: String, CaseIterable {
        case physical = "实物"
        case virtual = "虚拟"
        
        var displayName: String {
            return self.rawValue
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取奖品类型枚举
     */
    var prizeType: PrizeType {
        return PrizeType(rawValue: type ?? "虚拟") ?? .virtual
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新奖品的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        cost: Int,
        type: String,
        in schoolClass: SchoolClass,
        context: NSManagedObjectContext
    ) -> Prize {
        let prize = Prize(context: context)
        prize.id = UUID()
        prize.name = name
        prize.cost = Int32(cost)
        prize.type = type
        prize.schoolClass = schoolClass
        return prize
    }
}

extension Prize {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Prize> {
        return NSFetchRequest<Prize>(entityName: "Prize")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var cost: Int32
    @NSManaged public var type: String?
    @NSManaged public var schoolClass: SchoolClass?
    
} 