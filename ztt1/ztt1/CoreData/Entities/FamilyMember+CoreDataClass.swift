//
//  FamilyMember+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(FamilyMember)
public class FamilyMember: NSManagedObject, Identifiable {
    
    // MARK: - 注意：角色和性别枚举已移至独立文件
    // 请使用 FamilyRole.swift 和 Gender 枚举
    
    // MARK: - Computed Properties
    
    /**
     * 获取角色枚举
     */
    var roleEnum: FamilyRole {
        return FamilyRole(rawValue: role ?? "other") ?? .other
    }
    
    /**
     * 获取性别枚举
     */
    var genderEnum: Gender {
        return Gender(rawValue: gender ?? "male") ?? .male
    }
    
    /**
     * 获取头像图片名称
     */
    var avatarImageName: String {
        if let customAvatar = avatar, !customAvatar.isEmpty {
            return customAvatar
        }
        return FamilyAvatarSystem.getDefaultAvatarName(for: roleEnum)
    }
    
    /**
     * 是否为父母角色
     */
    var isParent: Bool {
        return roleEnum.isParent
    }
    
    /**
     * 是否为孩子角色
     */
    var isChild: Bool {
        return roleEnum.isChild
    }
    
    /**
     * 获取排序后的积分记录
     */
    var sortedPointRecords: [FamilyPointRecord] {
        guard let recordsSet = pointRecords as? Set<FamilyPointRecord> else { return [] }
        return recordsSet.sorted { ($0.timestamp ?? Date()) > ($1.timestamp ?? Date()) }
    }
    
    /**
     * 获取有效的积分记录（未撤销的）
     */
    var validPointRecords: [FamilyPointRecord] {
        return sortedPointRecords.filter { !$0.isReversed }
    }
    
    /**
     * 获取排序后的日记条目
     */
    var sortedDiaryEntries: [DiaryEntry] {
        guard let entriesSet = diaryEntries as? Set<DiaryEntry> else { return [] }
        return entriesSet.sorted { ($0.timestamp ?? Date()) > ($1.timestamp ?? Date()) }
    }
    
    /**
     * 获取可见的日记条目
     */
    var visibleDiaryEntries: [DiaryEntry] {
        return sortedDiaryEntries.filter { $0.isVisible }
    }
    
    /**
     * 获取排序后的AI报告
     */
    var sortedAIReports: [AIReport] {
        guard let reportsSet = aiReports as? Set<AIReport> else { return [] }
        return reportsSet.sorted { ($0.createdAt ?? Date()) > ($1.createdAt ?? Date()) }
    }
    
    /**
     * 积分记录统计
     */
    var pointRecordsCount: Int {
        return validPointRecords.count
    }
    
    var positivePointRecordsCount: Int {
        return validPointRecords.filter { $0.value > 0 }.count
    }
    
    var negativePointRecordsCount: Int {
        return validPointRecords.filter { $0.value < 0 }.count
    }

    /**
     * 获取排序后的成员规则
     */
    var sortedMemberRules: [FamilyMemberRule] {
        guard let rulesSet = memberRules as? Set<FamilyMemberRule> else { return [] }
        return rulesSet.sorted { rule1, rule2 in
            // 先按类型排序（加分在前），再按创建时间排序
            if rule1.type != rule2.type {
                return (rule1.type ?? "add") < (rule2.type ?? "add")
            }
            return (rule1.createdAt ?? Date()) < (rule2.createdAt ?? Date())
        }
    }

    /**
     * 获取常用规则
     */
    var frequentMemberRules: [FamilyMemberRule] {
        return sortedMemberRules.filter { $0.isFrequent }
    }

    /**
     * 获取加分规则
     */
    var addMemberRules: [FamilyMemberRule] {
        return sortedMemberRules.filter { $0.ruleType == .add }
    }

    /**
     * 获取扣分规则
     */
    var deductMemberRules: [FamilyMemberRule] {
        return sortedMemberRules.filter { $0.ruleType == .deduct }
    }

    /**
     * 获取常用加分规则
     */
    var frequentAddMemberRules: [FamilyMemberRule] {
        return addMemberRules.filter { $0.isFrequent }
    }

    /**
     * 获取常用扣分规则
     */
    var frequentDeductMemberRules: [FamilyMemberRule] {
        return deductMemberRules.filter { $0.isFrequent }
    }

    /**
     * 成员规则统计
     */
    var memberRulesCount: Int {
        return sortedMemberRules.count
    }

    var addRulesCount: Int {
        return addMemberRules.count
    }

    var deductRulesCount: Int {
        return deductMemberRules.count
    }

    // MARK: - Convenience Methods
    
    /**
     * 创建新家庭成员的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        role: String,
        gender: String,
        age: Int16 = 0,
        in family: Family,
        context: NSManagedObjectContext
    ) -> FamilyMember {
        let member = FamilyMember(context: context)
        member.id = UUID()
        member.name = name
        member.role = role
        member.gender = gender
        member.age = age
        member.currentPoints = 0
        member.createdAt = Date()
        member.updatedAt = Date()
        member.family = family
        
        // 不设置avatar字段，让avatarImageName属性动态计算
        // 这样可以确保始终使用最新的头像名称格式
        
        return member
    }
    
    /**
     * 添加积分记录
     */
    @discardableResult
    func addPoints(_ points: Int, reason: String, in context: NSManagedObjectContext) -> FamilyPointRecord {
        let record = FamilyPointRecord.create(
            reason: reason,
            value: points,
            for: self,
            in: context
        )
        
        // 更新成员积分
        self.currentPoints += Int32(points)
        self.updatedAt = Date()
        
        // 更新家庭总积分
        family?.updateTotalPoints()
        
        return record
    }
    
    /**
     * 添加日记条目
     * 注意：根据需求，成长日记默认对所有人可见
     */
    @discardableResult
    func addDiaryEntry(
        content: String,
        timestamp: Date = Date(),
        isVisible: Bool = true, // 参数保留但会被强制设为true
        in context: NSManagedObjectContext
    ) -> DiaryEntry {
        let entry = DiaryEntry.create(
            content: content,
            timestamp: timestamp,
            isVisible: true, // 强制设置为所有人可见
            for: self,
            in: context
        )

        self.updatedAt = Date()
        return entry
    }

    /**
     * 添加成员规则
     */
    @discardableResult
    func addMemberRule(
        name: String,
        value: Int32,
        type: String,
        isFrequent: Bool = false,
        in context: NSManagedObjectContext
    ) -> FamilyMemberRule {
        let rule = FamilyMemberRule.create(
            name: name,
            value: value,
            type: type,
            isFrequent: isFrequent,
            for: self,
            in: context
        )

        self.updatedAt = Date()
        return rule
    }

    /**
     * 删除成员规则
     */
    func removeMemberRule(_ rule: FamilyMemberRule, in context: NSManagedObjectContext) {
        context.delete(rule)
        self.updatedAt = Date()
    }

    /**
     * 应用成员规则
     */
    @discardableResult
    func applyMemberRule(_ rule: FamilyMemberRule, in context: NSManagedObjectContext) -> FamilyPointRecord {
        let record = rule.applyTo(member: self, in: context)
        return record
    }

    /**
     * 检查是否可以使用AI分析功能
     */
    func canUseAIAnalysis() -> Bool {
        // 需要至少10条有效积分记录
        return validPointRecords.count >= 10
    }
    
    /**
     * 检查是否可以生成成长报告
     */
    func canGenerateGrowthReport() -> Bool {
        // 需要至少有日记条目
        return !visibleDiaryEntries.isEmpty
    }
    
    /**
     * 获取成员统计信息
     */
    func getStatistics() -> FamilyMemberStatistics {
        let totalRecords = pointRecordsCount
        let positiveRecords = positivePointRecordsCount
        let negativeRecords = negativePointRecordsCount
        let totalDiaryEntries = visibleDiaryEntries.count
        let totalAIReports = sortedAIReports.count

        return FamilyMemberStatistics(
            currentPoints: Int(currentPoints),
            totalPointRecords: totalRecords,
            positiveRecords: positiveRecords,
            negativeRecords: negativeRecords,
            totalDiaryEntries: totalDiaryEntries,
            totalAIReports: totalAIReports
        )
    }

    /**
     * 修复头像数据 - 清除旧的avatar字段值，让系统使用新的头像名称格式
     */
    func fixAvatarData() {
        // 如果avatar字段包含旧格式的头像名称，清除它
        if let currentAvatar = avatar,
           currentAvatar.hasPrefix("avatar_") && currentAvatar.contains("_cartoon") {
            print("🔧 修复FamilyMember头像数据: \(name ?? "未知") - 清除旧格式: \(currentAvatar)")
            avatar = nil
        }
    }
}

// MARK: - CoreData Properties Extension

extension FamilyMember {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<FamilyMember> {
        return NSFetchRequest<FamilyMember>(entityName: "FamilyMember")
    }

    @nonobjc public override class func entity() -> NSEntityDescription {
        return NSEntityDescription.entity(forEntityName: "FamilyMember", in: CoreDataManager.shared.viewContext)!
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var role: String?
    @NSManaged public var gender: String?
    @NSManaged public var age: Int16
    @NSManaged public var currentPoints: Int32
    @NSManaged public var avatar: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var family: Family?
    @NSManaged public var pointRecords: NSSet?
    @NSManaged public var diaryEntries: NSSet?
    @NSManaged public var redemptionRecords: NSSet?
    @NSManaged public var lotteryRecords: NSSet?
    @NSManaged public var aiReports: NSSet?
    @NSManaged public var memberRules: NSSet?
}

// MARK: - Family Member Statistics

struct FamilyMemberStatistics {
    let currentPoints: Int
    let totalPointRecords: Int
    let positiveRecords: Int
    let negativeRecords: Int
    let totalDiaryEntries: Int
    let totalAIReports: Int
    
    var positiveRatio: Double {
        guard totalPointRecords > 0 else { return 0.0 }
        return Double(positiveRecords) / Double(totalPointRecords)
    }
}
