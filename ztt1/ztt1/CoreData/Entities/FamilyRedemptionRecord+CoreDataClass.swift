//
//  FamilyRedemptionRecord+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(FamilyRedemptionRecord)
public class FamilyRedemptionRecord: NSManagedObject {
    
    // MARK: - Enums
    
    enum RedemptionSource: String, CaseIterable {
        case redemption = "redemption"
        case lottery = "lottery"
        case gift = "gift"
        
        var displayName: String {
            switch self {
            case .redemption:
                return "family.redemption.source.redemption".localized
            case .lottery:
                return "family.redemption.source.lottery".localized
            case .gift:
                return "family.redemption.source.gift".localized
            }
        }
        
        var icon: String {
            switch self {
            case .redemption:
                return "cart.fill"
            case .lottery:
                return "dice.fill"
            case .gift:
                return "gift.fill"
            }
        }
        
        var color: String {
            switch self {
            case .redemption:
                return "#007AFF"
            case .lottery:
                return "#FF9500"
            case .gift:
                return "#FF2D92"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取兑换来源枚举
     */
    var redemptionSource: RedemptionSource {
        return RedemptionSource(rawValue: source ?? "redemption") ?? .redemption
    }
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示日期
     */
    var formattedDate: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示积分消耗
     */
    var formattedCost: String {
        return "\(cost) 积分"
    }
    
    /**
     * 兑换记录描述
     */
    var recordDescription: String {
        let sourceDesc = redemptionSource.displayName
        return "\(prizeName ?? "未知奖品") (\(sourceDesc), \(formattedCost))"
    }
    
    /**
     * 是否为今天的记录
     */
    var isToday: Bool {
        guard let timestamp = timestamp else { return false }
        return Calendar.current.isDateInToday(timestamp)
    }
    
    /**
     * 是否为本周的记录
     */
    var isThisWeek: Bool {
        guard let timestamp = timestamp else { return false }
        let calendar = Calendar.current
        let now = Date()
        let weekOfYear = calendar.component(.weekOfYear, from: now)
        let year = calendar.component(.year, from: now)
        let recordWeekOfYear = calendar.component(.weekOfYear, from: timestamp)
        let recordYear = calendar.component(.year, from: timestamp)
        
        return weekOfYear == recordWeekOfYear && year == recordYear
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新兑换记录的便利方法
     */
    @discardableResult
    static func create(
        prizeName: String,
        cost: Int,
        source: String = "redemption",
        for member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> FamilyRedemptionRecord {
        let record = FamilyRedemptionRecord(context: context)
        record.id = UUID()
        record.prizeName = prizeName
        record.cost = Int32(cost)
        record.timestamp = Date()
        record.source = source
        record.member = member
        return record
    }
    
    /**
     * 检查是否可以查看
     */
    func canView(by currentMember: FamilyMember?) -> Bool {
        // 所有家庭成员都可以查看兑换记录
        return true
    }
    
    /**
     * 检查是否可以删除
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 记录者本人可以删除
        if currentMember.id == member?.id {
            return true
        }
        
        // 父母可以删除所有兑换记录
        if currentMember.isParent {
            return true
        }
        
        return false
    }
    
    /**
     * 删除兑换记录并返还积分
     */
    func deleteAndRefund(in context: NSManagedObjectContext) {
        // 返还积分给成员
        if let member = member {
            member.currentPoints += cost
            member.updatedAt = Date()
            
            // 更新家庭总积分
            member.family?.updateTotalPoints()
        }
        
        // 删除记录
        context.delete(self)
    }
}

// MARK: - CoreData Properties Extension

extension FamilyRedemptionRecord {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<FamilyRedemptionRecord> {
        return NSFetchRequest<FamilyRedemptionRecord>(entityName: "FamilyRedemptionRecord")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var prizeName: String?
    @NSManaged public var cost: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var source: String?
    @NSManaged public var member: FamilyMember?
}

// MARK: - HistoryRecordProtocol Implementation

extension FamilyRedemptionRecord: HistoryRecordProtocol {
    
    /// 记录唯一标识
    var recordId: UUID? {
        return id
    }
    
    /// 记录显示名称
    var displayName: String {
        return prizeName ?? "family.redemption.default_prize".localized
    }
    
    /// 积分数值（兑换记录为负值）
    var pointsValue: Int {
        return -Int(cost)
    }
    
    /// 记录类型
    var recordType: HistoryRecordType {
        return .redemption
    }
    
    /// 记录时间戳
    var recordTimestamp: Date? {
        return timestamp
    }
    
    /// 是否可以删除
    var canDelete: Bool {
        return true
    }
    
    /// 删除记录的积分影响描述
    var deleteImpactDescription: String {
        return "删除后将返还 \(cost) 积分"
    }
}
