//
//  FamilyPointRecord+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(FamilyPointRecord)
public class FamilyPointRecord: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示分值
     */
    var formattedPoints: String {
        if value > 0 {
            return "+\(value)"
        } else {
            return "\(value)"
        }
    }
    
    /**
     * 分值颜色（十六进制字符串）
     */
    var pointsColorHex: String {
        return value > 0 ? "#26C34B" : "#FF5B5B"
    }
    
    /**
     * 是否为加分记录
     */
    var isPositive: Bool {
        return value > 0
    }
    
    /**
     * 是否为扣分记录
     */
    var isNegative: Bool {
        return value < 0
    }
    
    /**
     * 记录类型描述
     */
    var typeDescription: String {
        if isPositive {
            return "family.point_record.type.positive".localized
        } else if isNegative {
            return "family.point_record.type.negative".localized
        } else {
            return "family.point_record.type.neutral".localized
        }
    }
    
    /**
     * 是否为今天的记录
     */
    var isToday: Bool {
        guard let timestamp = timestamp else { return false }
        return Calendar.current.isDateInToday(timestamp)
    }
    
    /**
     * 是否为本周的记录
     */
    var isThisWeek: Bool {
        guard let timestamp = timestamp else { return false }
        let calendar = Calendar.current
        let now = Date()
        let weekOfYear = calendar.component(.weekOfYear, from: now)
        let year = calendar.component(.year, from: now)
        let recordWeekOfYear = calendar.component(.weekOfYear, from: timestamp)
        let recordYear = calendar.component(.year, from: timestamp)
        
        return weekOfYear == recordWeekOfYear && year == recordYear
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新积分记录的便利方法
     */
    @discardableResult
    static func create(
        reason: String,
        value: Int,
        for member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> FamilyPointRecord {
        let record = FamilyPointRecord(context: context)
        record.id = UUID()
        record.reason = reason
        record.value = Int32(value)
        record.timestamp = Date()
        record.isReversed = false
        record.member = member
        return record
    }
    
    /**
     * 撤销积分记录
     */
    func reverse() {
        guard !isReversed else { return }
        
        isReversed = true
        
        // 从成员积分中减去这个记录的分值
        if let member = member {
            member.currentPoints -= value
            member.updatedAt = Date()
            
            // 更新家庭总积分
            member.family?.updateTotalPoints()
        }
    }
    
    /**
     * 恢复积分记录
     */
    func restore() {
        guard isReversed else { return }
        
        isReversed = false
        
        // 将这个记录的分值重新加到成员积分中
        if let member = member {
            member.currentPoints += value
            member.updatedAt = Date()
            
            // 更新家庭总积分
            member.family?.updateTotalPoints()
        }
    }
    
    /**
     * 检查是否可以撤销
     */
    var canReverse: Bool {
        return !isReversed
    }
    
    /**
     * 检查是否可以恢复
     */
    var canRestore: Bool {
        return isReversed
    }
    
    /**
     * 获取撤销/恢复操作的影响描述
     */
    var operationImpactDescription: String {
        if canReverse {
            if value > 0 {
                return "撤销后将扣除 \(value) 积分"
            } else if value < 0 {
                return "撤销后将返还 \(abs(value)) 积分"
            } else {
                return "撤销此记录不会影响积分"
            }
        } else if canRestore {
            if value > 0 {
                return "恢复后将增加 \(value) 积分"
            } else if value < 0 {
                return "恢复后将扣除 \(abs(value)) 积分"
            } else {
                return "恢复此记录不会影响积分"
            }
        } else {
            return ""
        }
    }
    
    /**
     * 检查是否可以编辑
     */
    func canEdit(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 只有父母可以编辑积分记录
        return currentMember.isParent
    }
    
    /**
     * 检查是否可以删除
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 只有父母可以删除积分记录，且记录未被撤销
        return currentMember.isParent && !isReversed
    }
}

// MARK: - CoreData Properties Extension

extension FamilyPointRecord {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<FamilyPointRecord> {
        return NSFetchRequest<FamilyPointRecord>(entityName: "FamilyPointRecord")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var reason: String?
    @NSManaged public var value: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var isReversed: Bool
    @NSManaged public var member: FamilyMember?
}

// MARK: - HistoryRecordProtocol Implementation

extension FamilyPointRecord: HistoryRecordProtocol {
    
    /// 记录唯一标识
    var recordId: UUID? {
        return id
    }
    
    /// 记录显示名称
    var displayName: String {
        return reason ?? "family.point_record.default_reason".localized
    }
    
    /// 积分数值
    var pointsValue: Int {
        return Int(value)
    }
    
    /// 记录类型
    var recordType: HistoryRecordType {
        return .points
    }
    
    /// 记录时间戳
    var recordTimestamp: Date? {
        return timestamp
    }
    
    /// 是否可以删除
    var canDelete: Bool {
        // 已撤销的记录不能删除
        return !isReversed
    }
    
    /// 删除记录的积分影响描述
    var deleteImpactDescription: String {
        return operationImpactDescription
    }
}
