//
//  FamilyPrize+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(FamilyPrize)
public class FamilyPrize: NSManagedObject {
    
    // MARK: - Enums
    
    enum PrizeType: String, CaseIterable {
        case virtual = "虚拟"
        case physical = "实物"
        case experience = "体验"
        
        var displayName: String {
            switch self {
            case .virtual:
                return "family.prize.type.virtual".localized
            case .physical:
                return "family.prize.type.physical".localized
            case .experience:
                return "family.prize.type.experience".localized
            }
        }
        
        var icon: String {
            switch self {
            case .virtual:
                return "star.circle.fill"
            case .physical:
                return "gift.fill"
            case .experience:
                return "heart.circle.fill"
            }
        }
        
        var color: String {
            switch self {
            case .virtual:
                return "#007AFF"
            case .physical:
                return "#FF9500"
            case .experience:
                return "#FF2D92"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取奖品类型枚举
     */
    var prizeType: PrizeType {
        return PrizeType(rawValue: type ?? "虚拟") ?? .virtual
    }
    
    /**
     * 格式化显示积分消耗
     */
    var formattedCost: String {
        return "\(cost) 积分"
    }
    
    /**
     * 奖品描述
     */
    var prizeDescription: String {
        let typeDesc = prizeType.displayName
        return "\(name ?? "未知奖品") (\(typeDesc), \(formattedCost))"
    }
    
    /**
     * 是否可以兑换（检查积分是否足够）
     */
    func canRedeem(by member: FamilyMember) -> Bool {
        return member.currentPoints >= cost
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新家庭奖品的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        cost: Int32,
        type: String,
        for family: Family,
        in context: NSManagedObjectContext
    ) -> FamilyPrize {
        let prize = FamilyPrize(context: context)
        prize.id = UUID()
        prize.name = name
        prize.cost = cost
        prize.type = type
        prize.family = family
        return prize
    }
    
    /**
     * 兑换奖品
     */
    @discardableResult
    func redeem(
        by member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> FamilyRedemptionRecord? {
        // 检查积分是否足够
        guard canRedeem(by: member) else {
            return nil
        }
        
        // 扣除积分
        member.currentPoints -= cost
        member.updatedAt = Date()
        
        // 创建兑换记录
        let record = FamilyRedemptionRecord.create(
            prizeName: name ?? "未知奖品",
            cost: Int(cost),
            for: member,
            in: context
        )
        
        // 更新家庭总积分
        member.family?.updateTotalPoints()
        
        return record
    }
    
    /**
     * 更新奖品信息
     */
    func update(name: String, cost: Int32, type: String) {
        self.name = name
        self.cost = cost
        self.type = type
    }
    
    /**
     * 检查是否可以编辑
     */
    func canEdit(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 只有父母可以编辑家庭奖品
        return currentMember.isParent
    }
    
    /**
     * 检查是否可以删除
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 只有父母可以删除家庭奖品
        return currentMember.isParent
    }
    
    /**
     * 检查是否可以兑换
     */
    func canRedeem(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 所有家庭成员都可以兑换奖品（如果积分足够）
        return canRedeem(by: currentMember)
    }
}

// MARK: - CoreData Properties Extension

extension FamilyPrize {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<FamilyPrize> {
        return NSFetchRequest<FamilyPrize>(entityName: "FamilyPrize")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var cost: Int32
    @NSManaged public var type: String?
    @NSManaged public var family: Family?
}

// MARK: - Default Family Prizes

extension FamilyPrize {
    
    /**
     * 创建默认家庭奖品
     */
    static func createDefaultPrizes(for family: Family, in context: NSManagedObjectContext) {
        let defaultPrizes = [
            ("看电视30分钟", 10, "体验"),
            ("选择今天的晚餐", 15, "体验"),
            ("晚睡30分钟", 20, "体验"),
            ("小玩具", 25, "实物"),
            ("去公园玩", 30, "体验"),
            ("买喜欢的零食", 35, "实物"),
            ("看电影", 40, "体验"),
            ("新文具", 45, "实物"),
            ("去游乐场", 50, "体验"),
            ("新书", 60, "实物"),
            ("外出用餐", 80, "体验"),
            ("新衣服", 100, "实物")
        ]
        
        for (name, cost, type) in defaultPrizes {
            FamilyPrize.create(
                name: name,
                cost: Int32(cost),
                type: type,
                for: family,
                in: context
            )
        }
    }
}
