//
//  LotteryRecord+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(LotteryRecord)
public class LotteryRecord: NSManagedObject {
    
    // MARK: - Enums
    
    enum ToolType: String, CaseIterable {
        case wheel = "大转盘"
        case box = "盲盒"
        case scratch = "刮刮卡"
        
        var displayName: String {
            return self.rawValue
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取抽奖工具类型枚举
     */
    var lotteryToolType: ToolType {
        return ToolType(rawValue: toolType ?? "大转盘") ?? .wheel
    }
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新抽奖记录的便利方法
     */
    @discardableResult
    static func create(
        toolType: String,
        prizeResult: String,
        cost: Int,
        for student: Student,
        in context: NSManagedObjectContext
    ) -> LotteryRecord {
        let record = LotteryRecord(context: context)
        record.id = UUID()
        record.toolType = toolType
        record.prizeResult = prizeResult
        record.cost = Int32(cost)
        record.timestamp = Date()
        record.student = student
        return record
    }
}

extension LotteryRecord {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<LotteryRecord> {
        return NSFetchRequest<LotteryRecord>(entityName: "LotteryRecord")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var toolType: String?
    @NSManaged public var prizeResult: String?
    @NSManaged public var cost: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var student: Student?
    
}

// MARK: - HistoryRecordProtocol Implementation

extension LotteryRecord: HistoryRecordProtocol {
    
    /// 记录唯一标识
    var recordId: UUID? {
        return id
    }
    
    /// 记录显示名称
    var displayName: String {
        if let result = prizeResult, !result.isEmpty {
            return result
        }
        return lotteryToolType.displayName
    }
    
    /// 积分数值（抽奖记录为负值，表示消耗积分）
    var pointsValue: Int {
        return -Int(cost)
    }
    
    /// 记录类型（根据工具类型映射）
    var recordType: HistoryRecordType {
        switch lotteryToolType {
        case .wheel:
            return .wheelLottery
        case .box:
            return .boxLottery
        case .scratch:
            return .scratchLottery
        }
    }
    
    /// 记录时间戳
    var recordTimestamp: Date? {
        return timestamp
    }
    
    /// 删除记录的积分影响描述
    var deleteImpactDescription: String {
        return "删除后将返还 \(cost) 积分"
    }
} 