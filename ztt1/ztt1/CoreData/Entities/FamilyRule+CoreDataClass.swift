//
//  FamilyRule+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(FamilyRule)
public class FamilyRule: NSManagedObject {
    
    // MARK: - Enums
    
    enum RuleType: String, CaseIterable {
        case add = "add"
        case deduct = "deduct"
        
        var displayName: String {
            switch self {
            case .add:
                return "family.rule.type.add".localized
            case .deduct:
                return "family.rule.type.deduct".localized
            }
        }
        
        var icon: String {
            switch self {
            case .add:
                return "plus.circle.fill"
            case .deduct:
                return "minus.circle.fill"
            }
        }
        
        var color: String {
            switch self {
            case .add:
                return "#26C34B"
            case .deduct:
                return "#FF5B5B"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取规则类型枚举
     */
    var ruleType: RuleType {
        return RuleType(rawValue: type ?? "add") ?? .add
    }
    
    /**
     * 获取实际分值（扣分规则返回负值）
     */
    var actualValue: Int32 {
        switch ruleType {
        case .add:
            return value
        case .deduct:
            return -abs(value)
        }
    }
    
    /**
     * 格式化显示分值
     */
    var formattedValue: String {
        let actualVal = actualValue
        if actualVal > 0 {
            return "+\(actualVal)"
        } else {
            return "\(actualVal)"
        }
    }
    
    /**
     * 规则描述
     */
    var ruleDescription: String {
        let typeDesc = ruleType.displayName
        return "\(name ?? "未知规则") (\(typeDesc) \(formattedValue)分)"
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新家庭规则的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        value: Int32,
        type: String,
        isFrequent: Bool = false,
        for family: Family,
        in context: NSManagedObjectContext
    ) -> FamilyRule {
        let rule = FamilyRule(context: context)
        rule.id = UUID()
        rule.name = name
        rule.value = abs(value) // 存储绝对值
        rule.type = type
        rule.isFrequent = isFrequent
        rule.family = family
        return rule
    }
    
    /**
     * 应用规则到家庭成员
     */
    @discardableResult
    func applyTo(
        member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> FamilyPointRecord {
        let record = member.addPoints(
            Int(actualValue),
            reason: name ?? "未知规则",
            in: context
        )
        return record
    }
    
    /**
     * 应用规则到所有家庭成员
     */
    func applyToAllMembers(in context: NSManagedObjectContext) -> [FamilyPointRecord] {
        guard let family = family else { return [] }
        
        var records: [FamilyPointRecord] = []
        for member in family.sortedMembers {
            let record = applyTo(member: member, in: context)
            records.append(record)
        }
        
        return records
    }
    
    /**
     * 切换常用状态
     */
    func toggleFrequent() {
        isFrequent.toggle()
    }
    
    /**
     * 更新规则信息
     */
    func update(name: String, value: Int32, type: String) {
        self.name = name
        self.value = abs(value)
        self.type = type
    }
    
    /**
     * 检查是否可以编辑
     */
    func canEdit(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 只有父母可以编辑家庭规则
        return currentMember.isParent
    }
    
    /**
     * 检查是否可以删除
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 只有父母可以删除家庭规则
        return currentMember.isParent
    }
}

// MARK: - CoreData Properties Extension

extension FamilyRule {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<FamilyRule> {
        return NSFetchRequest<FamilyRule>(entityName: "FamilyRule")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var value: Int32
    @NSManaged public var type: String?
    @NSManaged public var isFrequent: Bool
    @NSManaged public var family: Family?
}

// MARK: - Default Family Rules

extension FamilyRule {
    
    /**
     * 创建默认家庭规则
     */
    static func createDefaultRules(for family: Family, in context: NSManagedObjectContext) {
        let defaultRules = [
            ("完成作业", 5, "add"),
            ("帮助家务", 3, "add"),
            ("主动学习", 4, "add"),
            ("礼貌待人", 2, "add"),
            ("按时睡觉", 2, "add"),
            ("迟到", 3, "deduct"),
            ("不完成作业", 5, "deduct"),
            ("说谎", 4, "deduct"),
            ("不听话", 2, "deduct"),
            ("浪费食物", 3, "deduct")
        ]
        
        for (name, value, type) in defaultRules {
            FamilyRule.create(
                name: name,
                value: Int32(value),
                type: type,
                isFrequent: true,
                for: family,
                in: context
            )
        }
    }
}
