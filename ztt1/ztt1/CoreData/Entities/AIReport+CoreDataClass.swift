//
//  AIReport+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(AIReport)
public class AIReport: NSManagedObject {
    
    // MARK: - Enums
    
    enum ReportType: String, CaseIterable {
        case professional = "professional"
        case parentFeedback = "parentFeedback"
        case growth = "growth"  // 基于日记的成长报告
        
        var displayName: String {
            switch self {
            case .professional:
                return "ai_report.type.professional".localized
            case .parentFeedback:
                return "ai_report.type.parent_feedback".localized
            case .growth:
                return "ai_report.type.growth".localized
            }
        }
        
        var icon: String {
            switch self {
            case .professional:
                return "chart.bar.doc.horizontal"
            case .parentFeedback:
                return "person.2.circle"
            case .growth:
                return "heart.text.square"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取报告类型枚举
     */
    var reportTypeEnum: ReportType {
        return ReportType(rawValue: reportType ?? "professional") ?? .professional
    }
    
    /**
     * 格式化生成时间
     */
    var formattedCreatedTime: String {
        guard let createdAt = createdAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter.string(from: createdAt)
    }
    
    /**
     * 格式化生成日期
     */
    var formattedCreatedDate: String {
        guard let createdAt = createdAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter.string(from: createdAt)
    }
    
    /**
     * 报告内容预览
     */
    var contentPreview: String {
        guard let content = content else { return "" }
        let maxLength = 100
        if content.count <= maxLength {
            return content
        }
        let index = content.index(content.startIndex, offsetBy: maxLength)
        return String(content[..<index]) + "..."
    }
    
    /**
     * 报告字数统计
     */
    var wordCount: Int {
        return content?.count ?? 0
    }
    
    /**
     * 积极行为比例
     */
    var positiveRatio: Double {
        guard totalRecords > 0 else { return 0.0 }
        return Double(positiveRecords) / Double(totalRecords) * 100
    }
    
    /**
     * 格式化的积极行为比例文本
     */
    var formattedPositiveRatio: String {
        return String(format: "%.1f%%", positiveRatio)
    }
    
    /**
     * 是否为今天生成的报告
     */
    var isToday: Bool {
        guard let createdAt = createdAt else { return false }
        return Calendar.current.isDateInToday(createdAt)
    }
    
    /**
     * 是否为本周生成的报告
     */
    var isThisWeek: Bool {
        guard let createdAt = createdAt else { return false }
        let calendar = Calendar.current
        let now = Date()
        let weekOfYear = calendar.component(.weekOfYear, from: now)
        let year = calendar.component(.year, from: now)
        let reportWeekOfYear = calendar.component(.weekOfYear, from: createdAt)
        let reportYear = calendar.component(.year, from: createdAt)
        
        return weekOfYear == reportWeekOfYear && year == reportYear
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 从AIAnalysisReport创建CoreData实体
     */
    @discardableResult
    static func create(
        from analysisReport: AIAnalysisReport,
        for member: FamilyMember? = nil,
        for student: Student? = nil,
        in context: NSManagedObjectContext
    ) -> AIReport {
        let report = AIReport(context: context)
        report.id = UUID()
        report.title = generateTitle(from: analysisReport)
        report.content = analysisReport.analysisContent
        report.reportType = analysisReport.reportType.rawValue
        report.createdAt = analysisReport.generatedAt
        report.inputDataSummary = generateInputSummary(from: analysisReport)
        report.studentName = analysisReport.studentName
        report.totalRecords = Int32(analysisReport.totalRecords)
        report.positiveRecords = Int32(analysisReport.positiveRecords)
        report.negativeRecords = Int32(analysisReport.negativeRecords)
        
        // 关联家庭成员或学生
        if let member = member {
            report.member = member
        }
        if let student = student {
            report.student = student
        }
        
        return report
    }
    
    /**
     * 生成报告标题
     */
    private static func generateTitle(from analysisReport: AIAnalysisReport) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        formatter.locale = Locale(identifier: "zh_CN")
        let dateString = formatter.string(from: analysisReport.generatedAt)
        
        let typeString = analysisReport.reportType == .professional ? "行为分析报告" : "家长反馈报告"
        return "\(analysisReport.studentName)的\(typeString) - \(dateString)"
    }
    
    /**
     * 生成输入数据摘要
     */
    private static func generateInputSummary(from analysisReport: AIAnalysisReport) -> String {
        return """
        数据基础：共\(analysisReport.totalRecords)条记录
        加分记录：\(analysisReport.positiveRecords)次
        扣分记录：\(analysisReport.negativeRecords)次
        积极行为比例：\(String(format: "%.1f%%", analysisReport.positiveRatio))
        """
    }
    
    /**
     * 更新报告内容
     */
    func updateContent(_ newContent: String) {
        content = newContent
    }
    
    /**
     * 获取完整报告文本（用于分享）
     */
    func getFullReportText() -> String {
        let typeString = reportTypeEnum.displayName
        let memberName = member?.name ?? student?.name ?? studentName ?? "未知"
        
        return """
        【\(typeString)】
        
        成员：\(memberName)
        生成时间：\(formattedCreatedTime)
        
        \(inputDataSummary ?? "")
        
        【详细分析】
        \(content ?? "")
        
        ---
        本报告由转团团AI分析系统生成
        """
    }
    
    /**
     * 检查是否可以删除
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 报告关联的成员本人可以删除
        if currentMember.id == member?.id {
            return true
        }
        
        // 父母可以删除所有报告
        if currentMember.isParent {
            return true
        }
        
        return false
    }
    
    /**
     * 检查是否可以查看
     */
    func canView(by currentMember: FamilyMember?) -> Bool {
        // 所有家庭成员都可以查看AI报告
        return true
    }
}

// MARK: - CoreData Properties Extension

extension AIReport {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<AIReport> {
        return NSFetchRequest<AIReport>(entityName: "AIReport")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var title: String?
    @NSManaged public var content: String?
    @NSManaged public var reportType: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var inputDataSummary: String?
    @NSManaged public var studentName: String?
    @NSManaged public var totalRecords: Int32
    @NSManaged public var positiveRecords: Int32
    @NSManaged public var negativeRecords: Int32
    @NSManaged public var member: FamilyMember?
    @NSManaged public var student: Student?
}

// MARK: - AIAnalysisReport Extension for Compatibility

extension AIAnalysisReport {
    /**
     * 保存到CoreData
     */
    func saveToDatabase(
        for member: FamilyMember? = nil,
        for student: Student? = nil,
        in context: NSManagedObjectContext
    ) -> AIReport {
        return AIReport.create(
            from: self,
            for: member,
            for: student,
            in: context
        )
    }
}
