//
//  LotteryToolItem+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/16.
//

import Foundation
import CoreData

@objc(LotteryToolItem)
public class LotteryToolItem: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /**
     * 获取格式化的奖品名称（处理空值）
     */
    var formattedPrizeName: String {
        return prizeName?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
    }
    
    /**
     * 获取显示用的项目标题
     */
    var displayTitle: String {
        let toolType = lotteryConfig?.lotteryToolType ?? .wheel
        switch toolType {
        case .wheel:
            return "分区 \(itemIndex)"
        case .box:
            return "盲盒 \(itemIndex)"
        case .scratch:
            return "刮刮卡 \(itemIndex)"
        }
    }
    
    /**
     * 格式化显示时间
     */
    var formattedCreatedTime: String {
        guard let createdAt = createdAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: createdAt)
    }
    
    /**
     * 检查奖品名称是否有效
     */
    var hasValidPrizeName: Bool {
        return !formattedPrizeName.isEmpty && formattedPrizeName.count <= 20
    }
    
    /**
     * 获取所属道具类型的显示名称
     */
    var toolTypeDisplayName: String {
        return lotteryConfig?.lotteryToolType.displayName ?? ""
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新抽奖道具项目的便利方法
     */
    @discardableResult
    static func create(
        index: Int,
        prizeName: String,
        for lotteryConfig: LotteryToolConfig,
        in context: NSManagedObjectContext
    ) -> LotteryToolItem {
        let item = LotteryToolItem(context: context)
        item.id = UUID()
        item.itemIndex = Int32(index)
        item.prizeName = prizeName
        item.createdAt = Date()
        item.lotteryConfig = lotteryConfig
        return item
    }
    
    /**
     * 更新奖品名称
     */
    func updatePrizeName(_ newPrizeName: String) {
        self.prizeName = newPrizeName.trimmingCharacters(in: .whitespacesAndNewlines)
        self.lotteryConfig?.updatedAt = Date()
    }
    
    /**
     * 验证奖品名称格式
     */
    func validatePrizeName() -> (isValid: Bool, errorMessage: String?) {
        let trimmedName = formattedPrizeName
        
        if trimmedName.isEmpty {
            return (false, "奖品名称不能为空")
        }
        
        if trimmedName.count > 20 {
            return (false, "奖品名称不能超过20个字符")
        }
        
        return (true, nil)
    }
    
    /**
     * 检查与同一配置下其他项目的重复性
     */
    func checkDuplicatePrizeName() -> (isDuplicate: Bool, duplicateIndex: Int?) {
        guard let config = lotteryConfig else { return (false, nil) }
        
        let otherItems = config.sortedItems.filter { $0.itemIndex != self.itemIndex }
        let duplicateItem = otherItems.first { item in
            item.formattedPrizeName.lowercased() == self.formattedPrizeName.lowercased()
        }
        
        if let duplicate = duplicateItem {
            return (true, Int(duplicate.itemIndex))
        }
        
        return (false, nil)
    }
    
    /**
     * 获取项目在配置中的位置信息
     */
    func getPositionInfo() -> (current: Int, total: Int) {
        guard let config = lotteryConfig else { return (0, 0) }
        return (Int(itemIndex), Int(config.itemCount))
    }
}

extension LotteryToolItem {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<LotteryToolItem> {
        return NSFetchRequest<LotteryToolItem>(entityName: "LotteryToolItem")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var itemIndex: Int32
    @NSManaged public var prizeName: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var lotteryConfig: LotteryToolConfig?
} 