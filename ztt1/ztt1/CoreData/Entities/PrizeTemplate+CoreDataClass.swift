//
//  PrizeTemplate+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(PrizeTemplate)
public class PrizeTemplate: NSManagedObject {
    
    // MARK: - Enums
    
    enum PrizeType: String, CaseIterable {
        case physical = "实物"
        case virtual = "虚拟"
        
        var displayName: String {
            return self.rawValue
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取奖品类型枚举
     */
    var prizeType: PrizeType {
        return PrizeType(rawValue: type ?? "虚拟") ?? .virtual
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新奖品模板的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        cost: Int,
        type: String,
        in context: NSManagedObjectContext
    ) -> PrizeTemplate {
        let template = PrizeTemplate(context: context)
        template.id = UUID()
        template.name = name
        template.cost = Int32(cost)
        template.type = type
        return template
    }
    
    /**
     * 转换为班级奖品
     */
    func toPrize(in schoolClass: SchoolClass, context: NSManagedObjectContext) -> Prize {
        return Prize.create(
            name: name ?? "",
            cost: Int(cost),
            type: type ?? "虚拟",
            in: schoolClass,
            context: context
        )
    }
}

extension PrizeTemplate {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<PrizeTemplate> {
        return NSFetchRequest<PrizeTemplate>(entityName: "PrizeTemplate")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var cost: Int32
    @NSManaged public var type: String?
    
} 