//
//  Student+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(Student)
public class Student: NSManagedObject, Identifiable {
    
    // MARK: - Enums
    
    enum Gender: String, CaseIterable {
        case male = "male"
        case female = "female"
        
        var displayName: String {
            switch self {
            case .male:
                return "gender.male".localized
            case .female:
                return "gender.female".localized
            }
        }
        
        var defaultAvatar: String {
            switch self {
            case .male:
                return "gender.avatar.male".localized
            case .female:
                return "gender.avatar.female".localized
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取性别枚举
     */
    var genderEnum: Gender {
        return Gender(rawValue: gender ?? "male") ?? .male
    }
    
    /**
     * 获取头像图片名称
     */
    var avatarImageName: String {
        // 检查是否为FamilyMember适配对象（通过学号等于姓名来判断）
        if let studentNumber = studentNumber,
           let name = name,
           studentNumber == name,
           let studentId = id {
            // 尝试查找对应的FamilyMember
            if let familyMember = CoreDataManager.shared.getFamilyMember(by: studentId) {
                return familyMember.avatarImageName
            }
        }

        // 默认使用性别对应的头像
        return genderEnum.defaultAvatar
    }

    /**
     * 获取角色信息（如果是FamilyMember适配对象）
     */
    var roleDisplayName: String? {
        // 检查是否为FamilyMember适配对象
        if let studentNumber = studentNumber,
           let name = name,
           studentNumber == name,
           let studentId = id {
            // 尝试查找对应的FamilyMember
            if let familyMember = CoreDataManager.shared.getFamilyMember(by: studentId) {
                let role = FamilyRole(rawValue: familyMember.role ?? "other") ?? .other
                return role.shortName
            }
        }
        return nil
    }

    /**
     * 获取年龄信息（如果是FamilyMember适配对象）
     */
    var ageValue: Int? {
        // 检查是否为FamilyMember适配对象
        if let studentNumber = studentNumber,
           let name = name,
           studentNumber == name,
           let studentId = id {
            // 尝试查找对应的FamilyMember
            if let familyMember = CoreDataManager.shared.getFamilyMember(by: studentId) {
                return Int(familyMember.age)
            }
        }
        return nil
    }

    /**
     * 是否为FamilyMember适配对象
     */
    var isFamilyMemberAdapter: Bool {
        if let studentNumber = studentNumber,
           let name = name,
           studentNumber == name {
            return true
        }
        return false
    }
    
    /**
     * 获取排序后的积分记录
     */
    var sortedPointRecords: [PointRecord] {
        guard let recordsSet = pointRecords as? Set<PointRecord> else { return [] }
        return recordsSet.sorted { 
            ($0.timestamp ?? Date()) > ($1.timestamp ?? Date())
        }
    }
    
    /**
     * 获取排序后的兑换记录
     */
    var sortedRedemptionRecords: [RedemptionRecord] {
        guard let recordsSet = redemptionRecords as? Set<RedemptionRecord> else { return [] }
        return recordsSet.sorted { 
            ($0.timestamp ?? Date()) > ($1.timestamp ?? Date())
        }
    }
    
    /**
     * 获取排序后的抽奖记录
     */
    var sortedLotteryRecords: [LotteryRecord] {
        guard let recordsSet = lotteryRecords as? Set<LotteryRecord> else { return [] }
        return recordsSet.sorted { 
            ($0.timestamp ?? Date()) > ($1.timestamp ?? Date())
        }
    }
    
    /**
     * 获取总积分记录数
     */
    var totalPointRecordsCount: Int {
        return pointRecords?.count ?? 0
    }
    
    /**
     * 获取正积分记录数
     */
    var positivePointRecordsCount: Int {
        guard let recordsSet = pointRecords as? Set<PointRecord> else { return 0 }
        return recordsSet.filter { $0.value > 0 && !$0.isReversed }.count
    }
    
    /**
     * 获取负积分记录数
     */
    var negativePointRecordsCount: Int {
        guard let recordsSet = pointRecords as? Set<PointRecord> else { return 0 }
        return recordsSet.filter { $0.value < 0 && !$0.isReversed }.count
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新学生的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        studentNumber: String,
        gender: String,
        in schoolClass: SchoolClass,
        context: NSManagedObjectContext
    ) -> Student {
        let student = Student(context: context)
        student.id = UUID()
        student.name = name
        student.studentNumber = studentNumber
        student.gender = gender
        student.point = 0
        student.createdAt = Date()
        student.schoolClass = schoolClass
        return student
    }
    
    /**
     * 添加积分记录
     */
    @discardableResult
    func addPoints(_ points: Int, reason: String, in context: NSManagedObjectContext) -> PointRecord {
        let record = PointRecord.create(
            reason: reason,
            value: points,
            for: self,
            in: context
        )
        
        // 更新学生积分
        self.point += Int32(points)
        
        return record
    }
    
    /**
     * 扣除积分记录
     */
    @discardableResult
    func deductPoints(_ points: Int, reason: String, in context: NSManagedObjectContext) -> PointRecord {
        let finalPoints = max(0, Int(self.point) - points)
        let actualDeduction = Int(self.point) - finalPoints
        
        let record = PointRecord.create(
            reason: reason,
            value: -actualDeduction,
            for: self,
            in: context
        )
        
        // 更新学生积分（不能低于0）
        self.point = Int32(finalPoints)
        
        return record
    }
    
    /**
     * 兑换奖品
     */
    @discardableResult
    func redeemPrize(
        prizeName: String,
        cost: Int,
        in context: NSManagedObjectContext
    ) -> RedemptionRecord? {
        // 检查积分是否足够
        guard Int(self.point) >= cost else { return nil }
        
        let record = RedemptionRecord.create(
            prizeName: prizeName,
            cost: cost,
            for: self,
            in: context
        )
        
        // 扣除积分
        self.point -= Int32(cost)
        
        return record
    }
    
    /**
     * 抽奖记录
     */
    @discardableResult
    func addLotteryRecord(
        toolType: String,
        prizeResult: String,
        cost: Int,
        in context: NSManagedObjectContext
    ) -> LotteryRecord? {
        // 检查积分是否足够
        guard Int(self.point) >= cost else { return nil }
        
        let record = LotteryRecord.create(
            toolType: toolType,
            prizeResult: prizeResult,
            cost: cost,
            for: self,
            in: context
        )
        
        // 扣除积分
        self.point -= Int32(cost)
        
        return record
    }
    
    /**
     * 撤销最近的积分记录
     */
    func reverseLastPointRecord(in context: NSManagedObjectContext) -> Bool {
        let unreversedRecords = sortedPointRecords.filter { !$0.isReversed }
        guard let lastRecord = unreversedRecords.first else { return false }
        
        // 标记为已撤销
        lastRecord.isReversed = true
        
        // 撤销积分变化
        self.point -= lastRecord.value
        
        // 确保积分不为负数
        if self.point < 0 {
            self.point = 0
        }
        
        return true
    }
    
    /**
     * 获取学生统计信息
     */
    func getStatistics() -> StudentStatistics {
        let totalRecords = totalPointRecordsCount
        let positiveRecords = positivePointRecordsCount
        let negativeRecords = negativePointRecordsCount
        let totalRedemptions = redemptionRecords?.count ?? 0
        let totalLotteries = lotteryRecords?.count ?? 0
        
        return StudentStatistics(
            currentPoints: Int(point),
            totalPointRecords: totalRecords,
            positiveRecords: positiveRecords,
            negativeRecords: negativeRecords,
            totalRedemptions: totalRedemptions,
            totalLotteries: totalLotteries
        )
    }
    
    struct StudentStatistics {
        let currentPoints: Int
        let totalPointRecords: Int
        let positiveRecords: Int
        let negativeRecords: Int
        let totalRedemptions: Int
        let totalLotteries: Int
    }
}

extension Student {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Student> {
        return NSFetchRequest<Student>(entityName: "Student")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var studentNumber: String?
    @NSManaged public var gender: String?
    @NSManaged public var point: Int32
    @NSManaged public var createdAt: Date?
    @NSManaged public var schoolClass: SchoolClass?
    @NSManaged public var pointRecords: NSSet?
    @NSManaged public var redemptionRecords: NSSet?
    @NSManaged public var lotteryRecords: NSSet?
    
}

// MARK: Generated accessors for pointRecords
extension Student {
    
    @objc(addPointRecordsObject:)
    @NSManaged public func addToPointRecords(_ value: PointRecord)
    
    @objc(removePointRecordsObject:)
    @NSManaged public func removeFromPointRecords(_ value: PointRecord)
    
    @objc(addPointRecords:)
    @NSManaged public func addToPointRecords(_ values: NSSet)
    
    @objc(removePointRecords:)
    @NSManaged public func removeFromPointRecords(_ values: NSSet)
    
}

// MARK: Generated accessors for redemptionRecords
extension Student {
    
    @objc(addRedemptionRecordsObject:)
    @NSManaged public func addToRedemptionRecords(_ value: RedemptionRecord)
    
    @objc(removeRedemptionRecordsObject:)
    @NSManaged public func removeFromRedemptionRecords(_ value: RedemptionRecord)
    
    @objc(addRedemptionRecords:)
    @NSManaged public func addToRedemptionRecords(_ values: NSSet)
    
    @objc(removeRedemptionRecords:)
    @NSManaged public func removeFromRedemptionRecords(_ values: NSSet)
    
}

// MARK: Generated accessors for lotteryRecords
extension Student {
    
    @objc(addLotteryRecordsObject:)
    @NSManaged public func addToLotteryRecords(_ value: LotteryRecord)
    
    @objc(removeLotteryRecordsObject:)
    @NSManaged public func removeFromLotteryRecords(_ value: LotteryRecord)
    
    @objc(addLotteryRecords:)
    @NSManaged public func addToLotteryRecords(_ values: NSSet)
    
    @objc(removeLotteryRecords:)
    @NSManaged public func removeFromLotteryRecords(_ values: NSSet)
    
} 