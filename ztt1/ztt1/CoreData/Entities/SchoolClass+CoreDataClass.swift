//
//  SchoolClass+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData

@objc(SchoolClass)
public class SchoolClass: NSManagedObject, Identifiable {
    
    // MARK: - Enums
    
    /**
     * 班级状态枚举
     * - active: 活跃状态，可正常使用
     * - frozen: 冻结状态，数据保留但不可操作
     */
    enum Status: String {
        case active = "active"
        case frozen = "frozen"
        
        var displayName: String {
            switch self {
            case .active:
                return "正常"
            case .frozen:
                return "已冻结"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取班级状态枚举
     */
    var classStatus: Status {
        return Status(rawValue: status ?? "active") ?? .active
    }
    
    /**
     * 检查班级是否处于活跃状态
     */
    var isActive: Bool {
        return classStatus == .active
    }
    
    /**
     * 检查班级是否被冻结
     */
    var isFrozen: Bool {
        return classStatus == .frozen
    }
    
    /**
     * 获取班级学生数量
     */
    var studentCount: Int {
        return students?.count ?? 0
    }
    
    /**
     * 获取班级总积分
     */
    var totalPoints: Int {
        guard let studentsSet = students as? Set<Student> else { return 0 }
        return studentsSet.reduce(0) { $0 + Int($1.point) }
    }
    
    /**
     * 获取排序后的学生列表
     */
    var sortedStudents: [Student] {
        guard let studentsSet = students as? Set<Student> else { return [] }
        return studentsSet.sorted { 
            $0.studentNumber ?? "" < $1.studentNumber ?? ""
        }
    }
    
    /**
     * 获取按积分排序的学生列表（从高到低）
     */
    var studentsByPoints: [Student] {
        guard let studentsSet = students as? Set<Student> else { return [] }
        return studentsSet.sorted { $0.point > $1.point }
    }
    
    /**
     * 获取班级的常用规则
     */
    var frequentRules: [Rule] {
        guard let rulesSet = rules as? Set<Rule> else { return [] }
        return rulesSet.filter { $0.isFrequent }.sorted { $0.name ?? "" < $1.name ?? "" }
    }
    
    /**
     * 获取班级奖品列表
     */
    var sortedPrizes: [Prize] {
        guard let prizesSet = prizes as? Set<Prize> else { return [] }
        return prizesSet.sorted { $0.cost < $1.cost }
    }
    
    /**
     * 获取班级抽奖道具配置列表
     */
    var sortedLotteryConfigs: [LotteryToolConfig] {
        guard let configsSet = lotteryConfigs as? Set<LotteryToolConfig> else { return [] }
        return configsSet.sorted { config1, config2 in
            // 按道具类型排序：大转盘 -> 盲盒 -> 刮刮卡
            let type1 = config1.lotteryToolType
            let type2 = config2.lotteryToolType
            if type1 != type2 {
                return type1.rawValue < type2.rawValue
            }
            return (config1.createdAt ?? Date()) < (config2.createdAt ?? Date())
        }
    }
    
    /**
     * 根据道具类型获取配置
     */
    func getLotteryConfig(for toolType: LotteryToolConfig.ToolType) -> LotteryToolConfig? {
        return sortedLotteryConfigs.first { $0.lotteryToolType == toolType }
    }
    
    /**
     * 检查是否已配置指定道具类型
     */
    func hasLotteryConfig(for toolType: LotteryToolConfig.ToolType) -> Bool {
        return getLotteryConfig(for: toolType) != nil
    }
    
    // MARK: - Status Management Methods
    
    /**
     * 冻结班级
     * 将班级状态设置为冻结状态，保留数据但不可操作
     */
    func freeze(in context: NSManagedObjectContext) {
        status = Status.frozen.rawValue
        try? context.save()
        print("❄️ 班级已冻结: \(name ?? "未知班级")")
    }
    
    /**
     * 解冻班级
     * 将班级状态设置为活跃状态，恢复正常操作
     */
    func unfreeze(in context: NSManagedObjectContext) {
        status = Status.active.rawValue
        try? context.save()
        print("🔥 班级已解冻: \(name ?? "未知班级")")
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新班级的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        owner: User,
        in context: NSManagedObjectContext
    ) -> SchoolClass {
        let schoolClass = SchoolClass(context: context)
        schoolClass.id = UUID()
        schoolClass.name = name
        schoolClass.createdAt = Date()
        schoolClass.status = Status.active.rawValue
        schoolClass.owner = owner
        return schoolClass
    }
    
    /**
     * 添加学生到班级
     */
    func addStudent(
        name: String,
        studentNumber: String,
        gender: String,
        in context: NSManagedObjectContext
    ) -> Student {
        let student = Student.create(
            name: name,
            studentNumber: studentNumber,
            gender: gender,
            in: self,
            context: context
        )
        return student
    }
    
    /**
     * 根据搜索条件过滤学生
     */
    func filteredStudents(searchText: String) -> [Student] {
        let allStudents = sortedStudents
        
        if searchText.isEmpty {
            return allStudents
        } else {
            return allStudents.filter { student in
                let name = student.name ?? ""
                let number = student.studentNumber ?? ""
                return name.localizedCaseInsensitiveContains(searchText) ||
                       number.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    /**
     * 为全班学生加分
     */
    func addPointsToAllStudents(_ points: Int, reason: String, in context: NSManagedObjectContext) {
        guard let studentsSet = students as? Set<Student> else { return }
        
        for student in studentsSet {
            student.addPoints(points, reason: reason, in: context)
        }
    }
    
    /**
     * 为全班学生扣分
     */
    func deductPointsFromAllStudents(_ points: Int, reason: String, in context: NSManagedObjectContext) {
        guard let studentsSet = students as? Set<Student> else { return }
        
        for student in studentsSet {
            student.deductPoints(points, reason: reason, in: context)
        }
    }
    
    /**
     * 获取班级统计信息
     */
    func getStatistics() -> ClassStatistics {
        let allStudents = sortedStudents
        let totalStudents = allStudents.count
        let totalPoints = allStudents.reduce(0) { $0 + Int($1.point) }
        let averagePoints = totalStudents > 0 ? totalPoints / totalStudents : 0
        let highestPoints = allStudents.map { Int($0.point) }.max() ?? 0
        let lowestPoints = allStudents.map { Int($0.point) }.min() ?? 0
        
        return ClassStatistics(
            totalStudents: totalStudents,
            totalPoints: totalPoints,
            averagePoints: averagePoints,
            highestPoints: highestPoints,
            lowestPoints: lowestPoints
        )
    }
    
    struct ClassStatistics {
        let totalStudents: Int
        let totalPoints: Int
        let averagePoints: Int
        let highestPoints: Int
        let lowestPoints: Int
    }
    
    /**
     * 重置全班学生积分到指定值
     */
    func resetAllStudentsPoints(to points: Int, in context: NSManagedObjectContext) -> Bool {
        guard let studentsSet = students as? Set<Student> else { return false }
        
        do {
            for student in studentsSet {
                student.point = Int32(points)
            }
            
            try context.save()
            print("✅ 成功重置 \(studentsSet.count) 名学生的积分为 \(points) 分")
            return true
        } catch {
            print("❌ 重置学生积分失败: \(error.localizedDescription)")
            context.rollback()
            return false
        }
    }
    
    /**
     * 清除全班学生历史记录
     */
    func clearAllStudentsHistory(in context: NSManagedObjectContext) -> (success: Bool, deletedCount: Int) {
        guard let studentsSet = students as? Set<Student> else { 
            return (false, 0) 
        }
        
        var deletedCount = 0
        
        do {
            for student in studentsSet {
                // 删除积分记录
                if let pointRecords = student.pointRecords as? Set<PointRecord> {
                    for record in pointRecords {
                        context.delete(record)
                        deletedCount += 1
                    }
                }
                
                // 删除兑换记录
                if let redemptionRecords = student.redemptionRecords as? Set<RedemptionRecord> {
                    for record in redemptionRecords {
                        context.delete(record)
                        deletedCount += 1
                    }
                }
                
                // 删除抽奖记录
                if let lotteryRecords = student.lotteryRecords as? Set<LotteryRecord> {
                    for record in lotteryRecords {
                        context.delete(record)
                        deletedCount += 1
                    }
                }
            }
            
            try context.save()
            print("✅ 成功清除 \(studentsSet.count) 名学生的 \(deletedCount) 条历史记录")
            return (true, deletedCount)
        } catch {
            print("❌ 清除历史记录失败: \(error.localizedDescription)")
            context.rollback()
            return (false, 0)
        }
    }
    
    /**
     * 获取班级历史记录统计
     */
    func getHistoryStatistics() -> (totalRecords: Int, pointRecords: Int, redemptionRecords: Int, lotteryRecords: Int) {
        guard let studentsSet = students as? Set<Student> else { 
            return (0, 0, 0, 0) 
        }
        
        var pointRecordsCount = 0
        var redemptionRecordsCount = 0
        var lotteryRecordsCount = 0
        
        for student in studentsSet {
            pointRecordsCount += student.pointRecords?.count ?? 0
            redemptionRecordsCount += student.redemptionRecords?.count ?? 0
            lotteryRecordsCount += student.lotteryRecords?.count ?? 0
        }
        
        let totalRecords = pointRecordsCount + redemptionRecordsCount + lotteryRecordsCount
        
        return (
            totalRecords: totalRecords,
            pointRecords: pointRecordsCount,
            redemptionRecords: redemptionRecordsCount,
            lotteryRecords: lotteryRecordsCount
        )
    }
}

extension SchoolClass {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<SchoolClass> {
        return NSFetchRequest<SchoolClass>(entityName: "SchoolClass")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var status: String?
    @NSManaged public var owner: User?
    @NSManaged public var students: NSSet?
    @NSManaged public var rules: NSSet?
    @NSManaged public var prizes: NSSet?
    @NSManaged public var lotteryConfigs: NSSet?
    
}

// MARK: Generated accessors for students
extension SchoolClass {
    
    @objc(addStudentsObject:)
    @NSManaged public func addToStudents(_ value: Student)
    
    @objc(removeStudentsObject:)
    @NSManaged public func removeFromStudents(_ value: Student)
    
    @objc(addStudents:)
    @NSManaged public func addToStudents(_ values: NSSet)
    
    @objc(removeStudents:)
    @NSManaged public func removeFromStudents(_ values: NSSet)
    
}

// MARK: Generated accessors for rules
extension SchoolClass {
    
    @objc(addRulesObject:)
    @NSManaged public func addToRules(_ value: Rule)
    
    @objc(removeRulesObject:)
    @NSManaged public func removeFromRules(_ value: Rule)
    
    @objc(addRules:)
    @NSManaged public func addToRules(_ values: NSSet)
    
    @objc(removeRules:)
    @NSManaged public func removeFromRules(_ values: NSSet)
    
}

// MARK: Generated accessors for prizes
extension SchoolClass {
    
    @objc(addPrizesObject:)
    @NSManaged public func addToPrizes(_ value: Prize)
    
    @objc(removePrizesObject:)
    @NSManaged public func removeFromPrizes(_ value: Prize)
    
    @objc(addPrizes:)
    @NSManaged public func addToPrizes(_ values: NSSet)
    
    @objc(removePrizes:)
    @NSManaged public func removeFromPrizes(_ values: NSSet)
    
}

// MARK: Generated accessors for lotteryConfigs
extension SchoolClass {
    
    @objc(addLotteryConfigsObject:)
    @NSManaged public func addToLotteryConfigs(_ value: LotteryToolConfig)
    
    @objc(removeLotteryConfigsObject:)
    @NSManaged public func removeFromLotteryConfigs(_ value: LotteryToolConfig)
    
    @objc(addLotteryConfigs:)
    @NSManaged public func addToLotteryConfigs(_ values: NSSet)
    
    @objc(removeLotteryConfigs:)
    @NSManaged public func removeFromLotteryConfigs(_ values: NSSet)
    
} 