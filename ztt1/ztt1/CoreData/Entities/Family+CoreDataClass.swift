//
//  Family+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import CoreData

@objc(Family)
public class Family: NSManagedObject {
    
    // MARK: - Computed Properties
    
    /**
     * 获取家庭成员数组（排序）
     */
    var sortedMembers: [FamilyMember] {
        guard let membersSet = members as? Set<FamilyMember> else { return [] }
        return membersSet.sorted { member1, member2 in
            // 按角色排序：父母优先，然后是孩子
            let roleOrder = ["father": 0, "mother": 1, "son": 2, "daughter": 3, "other": 4]
            let order1 = roleOrder[member1.role ?? "other"] ?? 4
            let order2 = roleOrder[member2.role ?? "other"] ?? 4
            
            if order1 != order2 {
                return order1 < order2
            }
            
            // 同角色按创建时间排序
            return (member1.createdAt ?? Date()) < (member2.createdAt ?? Date())
        }
    }
    
    /**
     * 获取父母成员
     */
    var parents: [FamilyMember] {
        return sortedMembers.filter { $0.role == "father" || $0.role == "mother" }
    }
    
    /**
     * 获取孩子成员
     */
    var children: [FamilyMember] {
        return sortedMembers.filter { $0.role == "son" || $0.role == "daughter" }
    }
    
    /**
     * 获取其他家庭成员
     */
    var otherMembers: [FamilyMember] {
        return sortedMembers.filter { $0.role == "other" }
    }
    
    /**
     * 计算家庭总积分
     */
    var calculatedTotalPoints: Int32 {
        return sortedMembers.reduce(0) { $0 + $1.currentPoints }
    }
    
    /**
     * 更新家庭总积分
     */
    func updateTotalPoints() {
        totalPoints = calculatedTotalPoints
        updatedAt = Date()
    }
    
    /**
     * 获取家庭统计信息
     */
    func getStatistics() -> FamilyStatistics {
        let members = sortedMembers
        let totalMembers = members.count
        let totalPoints = Int(calculatedTotalPoints)
        
        // 计算所有积分记录
        let allPointRecords = members.flatMap { $0.sortedPointRecords }
        let totalRecords = allPointRecords.count
        let positiveRecords = allPointRecords.filter { $0.value > 0 }.count
        let negativeRecords = allPointRecords.filter { $0.value < 0 }.count
        
        return FamilyStatistics(
            totalMembers: totalMembers,
            totalPoints: totalPoints,
            totalRecords: totalRecords,
            positiveRecords: positiveRecords,
            negativeRecords: negativeRecords
        )
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新家庭的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        owner: User,
        in context: NSManagedObjectContext
    ) -> Family {
        let family = Family(context: context)
        family.id = UUID()
        family.name = name
        family.createdAt = Date()
        family.updatedAt = Date()
        family.totalPoints = 0
        family.owner = owner
        return family
    }
    
    /**
     * 添加家庭成员
     */
    @discardableResult
    func addMember(
        name: String,
        role: String,
        gender: String,
        age: Int16 = 0,
        in context: NSManagedObjectContext
    ) -> FamilyMember {
        let member = FamilyMember.create(
            name: name,
            role: role,
            gender: gender,
            age: age,
            in: self,
            context: context
        )
        
        updateTotalPoints()
        return member
    }
    
    /**
     * 删除家庭成员
     */
    func removeMember(_ member: FamilyMember, in context: NSManagedObjectContext) {
        context.delete(member)
        updateTotalPoints()
    }
    
    /**
     * 检查角色是否已存在（父母角色唯一性检查）
     */
    func hasRole(_ role: String) -> Bool {
        return sortedMembers.contains { $0.role == role }
    }
    
    /**
     * 获取指定角色的成员
     */
    func getMember(withRole role: String) -> FamilyMember? {
        return sortedMembers.first { $0.role == role }
    }
}

// MARK: - CoreData Properties Extension

extension Family {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Family> {
        return NSFetchRequest<Family>(entityName: "Family")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var totalPoints: Int32
    @NSManaged public var updatedAt: Date?
    @NSManaged public var owner: User?
    @NSManaged public var members: NSSet?
    @NSManaged public var rules: NSSet?
    @NSManaged public var prizes: NSSet?
}

// MARK: - Generated accessors for members
extension Family {
    
    @objc(addMembersObject:)
    @NSManaged public func addToMembers(_ value: FamilyMember)
    
    @objc(removeMembersObject:)
    @NSManaged public func removeFromMembers(_ value: FamilyMember)
    
    @objc(addMembers:)
    @NSManaged public func addToMembers(_ values: NSSet)
    
    @objc(removeMembers:)
    @NSManaged public func removeFromMembers(_ values: NSSet)
}

// MARK: - Generated accessors for rules
extension Family {
    
    @objc(addRulesObject:)
    @NSManaged public func addToRules(_ value: FamilyRule)
    
    @objc(removeRulesObject:)
    @NSManaged public func removeFromRules(_ value: FamilyRule)
    
    @objc(addRules:)
    @NSManaged public func addToRules(_ values: NSSet)
    
    @objc(removeRules:)
    @NSManaged public func removeFromRules(_ values: NSSet)
}

// MARK: - Generated accessors for prizes
extension Family {
    
    @objc(addPrizesObject:)
    @NSManaged public func addToPrizes(_ value: FamilyPrize)
    
    @objc(removePrizesObject:)
    @NSManaged public func removeFromPrizes(_ value: FamilyPrize)
    
    @objc(addPrizes:)
    @NSManaged public func addToPrizes(_ values: NSSet)
    
    @objc(removePrizes:)
    @NSManaged public func removeFromPrizes(_ values: NSSet)
}

// MARK: - Family Statistics

struct FamilyStatistics {
    let totalMembers: Int
    let totalPoints: Int
    let totalRecords: Int
    let positiveRecords: Int
    let negativeRecords: Int
    
    var averagePointsPerMember: Double {
        guard totalMembers > 0 else { return 0.0 }
        return Double(totalPoints) / Double(totalMembers)
    }
    
    var positiveRatio: Double {
        guard totalRecords > 0 else { return 0.0 }
        return Double(positiveRecords) / Double(totalRecords)
    }
}
