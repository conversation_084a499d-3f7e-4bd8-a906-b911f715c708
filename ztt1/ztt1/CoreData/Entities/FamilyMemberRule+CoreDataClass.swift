//
//  FamilyMemberRule+CoreDataClass.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/28.
//

import Foundation
import CoreData

@objc(FamilyMemberRule)
public class FamilyMemberRule: NSManagedObject {
    
    // MARK: - Enums
    
    enum RuleType: String, CaseIterable {
        case add = "add"
        case deduct = "deduct"
        
        var displayName: String {
            switch self {
            case .add:
                return "加分"
            case .deduct:
                return "扣分"
            }
        }
        
        var iconName: String {
            switch self {
            case .add:
                return "plus"
            case .deduct:
                return "minus"
            }
        }
        
        var colorHex: String {
            switch self {
            case .add:
                return "#26C34B"
            case .deduct:
                return "#FF5B5B"
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取规则类型枚举
     */
    var ruleType: RuleType {
        return RuleType(rawValue: type ?? "add") ?? .add
    }
    
    /**
     * 是否为加分规则
     */
    var isAddRule: Bool {
        return ruleType == .add
    }
    
    /**
     * 是否为扣分规则
     */
    var isDeductRule: Bool {
        return ruleType == .deduct
    }
    
    /**
     * 获取实际分值（扣分规则返回负值）
     */
    var actualValue: Int32 {
        return ruleType == .add ? value : -value
    }
    
    /**
     * 格式化显示分值
     */
    var formattedValue: String {
        let prefix = ruleType == .add ? "+" : "-"
        return "\(prefix)\(value)"
    }
    
    /**
     * 获取规则显示颜色
     */
    var displayColor: String {
        return ruleType.colorHex
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 创建新家庭成员规则的便利方法
     */
    @discardableResult
    static func create(
        name: String,
        value: Int32,
        type: String,
        isFrequent: Bool = false,
        for member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> FamilyMemberRule {
        let rule = FamilyMemberRule(context: context)
        rule.id = UUID()
        rule.name = name
        rule.value = abs(value) // 存储绝对值
        rule.type = type
        rule.isFrequent = isFrequent
        rule.createdAt = Date()
        rule.updatedAt = Date()
        rule.member = member
        return rule
    }
    
    /**
     * 应用规则到家庭成员
     */
    @discardableResult
    func applyTo(
        member: FamilyMember,
        in context: NSManagedObjectContext
    ) -> FamilyPointRecord {
        let record = member.addPoints(
            Int(actualValue),
            reason: name ?? "未知规则",
            in: context
        )
        return record
    }
    
    /**
     * 切换常用状态
     */
    func toggleFrequent() {
        isFrequent.toggle()
        updatedAt = Date()
    }
    
    /**
     * 更新规则信息
     */
    func update(name: String, value: Int32, type: String) {
        self.name = name
        self.value = abs(value)
        self.type = type
        self.updatedAt = Date()
    }
    
    /**
     * 检查是否可以编辑
     */
    func canEdit(by currentMember: FamilyMember?) -> Bool {
        guard let currentMember = currentMember else { return false }
        
        // 只有父母可以编辑家庭成员规则
        return currentMember.isParent
    }
    
    /**
     * 检查是否可以删除
     */
    func canDelete(by currentMember: FamilyMember?) -> Bool {
        return canEdit(by: currentMember)
    }
    
    /**
     * 获取规则统计信息
     */
    func getUsageStatistics() -> RuleUsageStatistics {
        // 这里可以添加规则使用统计的逻辑
        // 比如统计该规则被使用的次数等
        return RuleUsageStatistics(
            usageCount: 0, // 暂时返回0，后续可以实现
            lastUsedAt: nil
        )
    }

    /**
     * 转换为StudentPointsOperation
     */
    func toStudentPointsOperation() -> StudentPointsOperation? {
        return StudentPointsOperation.fromMemberRule(self)
    }
}

// MARK: - CoreData Properties Extension

extension FamilyMemberRule {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<FamilyMemberRule> {
        return NSFetchRequest<FamilyMemberRule>(entityName: "FamilyMemberRule")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var value: Int32
    @NSManaged public var type: String?
    @NSManaged public var isFrequent: Bool
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var member: FamilyMember?
}

// MARK: - Rule Usage Statistics

struct RuleUsageStatistics {
    let usageCount: Int
    let lastUsedAt: Date?
}

// MARK: - Default Member Rules

extension FamilyMemberRule {
    
    /**
     * 为新成员创建默认规则
     */
    static func createDefaultRules(for member: FamilyMember, in context: NSManagedObjectContext) {
        let defaultRules = [
            ("完成作业", 5, "add"),
            ("帮助家务", 3, "add"),
            ("主动学习", 4, "add"),
            ("礼貌待人", 2, "add"),
            ("按时睡觉", 2, "add"),
            ("迟到", 3, "deduct"),
            ("不完成作业", 5, "deduct"),
            ("说谎", 4, "deduct"),
            ("不听话", 2, "deduct"),
            ("浪费食物", 3, "deduct")
        ]
        
        for (name, value, type) in defaultRules {
            FamilyMemberRule.create(
                name: name,
                value: Int32(value),
                type: type,
                isFrequent: true,
                for: member,
                in: context
            )
        }
    }
}
