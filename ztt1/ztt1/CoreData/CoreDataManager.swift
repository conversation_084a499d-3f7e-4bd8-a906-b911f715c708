//
//  CoreDataManager.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/15.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI

/**
 * CoreData管理器
 * 统一管理所有实体的CRUD操作，支持CloudKit多设备同步
 * 兼容iOS 15.6+
 */
class CoreDataManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CoreDataManager()
    
    // MARK: - Properties
    private let persistenceController: PersistenceController
    var viewContext: NSManagedObjectContext
    
    // MARK: - Published Properties
    @Published var isInitialized: Bool = false
    @Published var isSyncing: Bool = false
    @Published var syncError: Error?
    @Published var cloudKitStatus: CloudKitStatus = .unknown
    @Published var lastSyncDate: Date?

    // MARK: - CloudKit Status Enum
    enum CloudKitStatus: Equatable {
        case unknown
        case available
        case noAccount
        case restricted
        case couldNotDetermine
        case syncInProgress
        case syncCompleted
        case syncFailed(Error)
        
        var displayText: String {
            switch self {
            case .unknown: return "未知状态"
            case .available: return "CloudKit可用"
            case .noAccount: return "未登录iCloud"
            case .restricted: return "iCloud受限"
            case .couldNotDetermine: return "无法确定状态"
            case .syncInProgress: return "同步中..."
            case .syncCompleted: return "同步完成"
            case .syncFailed(let error): return "同步失败: \(error.localizedDescription)"
            }
        }
        
        static func == (lhs: CloudKitStatus, rhs: CloudKitStatus) -> Bool {
            switch (lhs, rhs) {
            case (.unknown, .unknown),
                 (.available, .available),
                 (.noAccount, .noAccount),
                 (.restricted, .restricted),
                 (.couldNotDetermine, .couldNotDetermine),
                 (.syncInProgress, .syncInProgress),
                 (.syncCompleted, .syncCompleted):
                return true
            case (.syncFailed(let lhsError), .syncFailed(let rhsError)):
                return lhsError.localizedDescription == rhsError.localizedDescription
            default:
                return false
            }
        }
    }
    
    // MARK: - Initialization
    
    /**
     * 初始化CoreData管理器
     * - Parameter persistenceController: 持久化控制器
     */
    init(persistenceController: PersistenceController = PersistenceController.shared) {
        self.persistenceController = persistenceController
        self.viewContext = persistenceController.container.viewContext
        
        print("✅ CoreDataManager初始化完成 - CloudKit同步已启用")
        
        // 设置CloudKit通知监听
        setupCloudKitNotifications()
        
        // 检查CloudKit可用性
        checkCloudKitAvailability()
        
        // 检查并同步订阅状态
        checkAndSyncSubscriptionStatus()
    }
    
    // MARK: - CloudKit Availability Check
    
    /**
     * 检查CloudKit可用性
     */
    func checkCloudKitAvailability() {
        print("🔍 检查CloudKit可用性...")
        
        CKContainer.default().accountStatus { (status, error) in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ CloudKit状态检查失败: \(error)")
                    self.cloudKitStatus = .syncFailed(error)
                    return
                }
                
                switch status {
                case .available:
                    print("✅ CloudKit账户可用")
                    self.cloudKitStatus = .available
                    // 依赖CoreData-CloudKit的自动同步，不再手动查询数据
                    // CoreData已经配置了NSPersistentCloudKitContainer，会自动处理同步
                    print("📡 CloudKit同步已准备就绪")
                    
                    // 触发一次保存操作，确保任何未同步的更改开始同步
                    if self.viewContext.hasChanges {
                        self.save()
                    }
                case .noAccount:
                    print("❌ 未登录iCloud账户")
                    self.cloudKitStatus = .noAccount
                case .restricted:
                    print("❌ iCloud账户受限")
                    self.cloudKitStatus = .restricted
                case .couldNotDetermine:
                    print("❌ 无法确定iCloud状态")
                    self.cloudKitStatus = .couldNotDetermine
                case .temporarilyUnavailable:
                    print("❌ iCloud暂时不可用")
                    self.cloudKitStatus = .couldNotDetermine
                @unknown default:
                    print("❌ 未知iCloud状态")
                    self.cloudKitStatus = .unknown
                }
            }
        }
    }
    
    // MARK: - CloudKit Sync Management
    
    /**
     * 设置CloudKit通知监听
     */
    private func setupCloudKitNotifications() {
        print("🔔 设置CloudKit同步通知监听...")
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCloudKitImport),
            name: .NSPersistentStoreRemoteChange,
            object: persistenceController.container.persistentStoreCoordinator
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCloudKitExport),
            name: NSNotification.Name("NSPersistentStoreRemoteChangeNotification"),
            object: nil
        )
        
        // 监听同步暂停和恢复通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSyncPaused),
            name: NSNotification.Name("CloudKitSyncPaused"),
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSyncResumed),
            name: NSNotification.Name("CloudKitSyncResumed"),
            object: nil
        )
        
        // 设置后台自动同步定时器（每15分钟同步一次）
        setupBackgroundSync()
    }
    
    /**
     * 处理同步暂停通知
     */
    @objc private func handleSyncPaused() {
        print("⏸️ 收到CloudKit同步暂停通知")
        DispatchQueue.main.async {
            self.isSyncing = false
            self.cloudKitStatus = .available
        }
    }
    
    /**
     * 处理同步恢复通知
     */
    @objc private func handleSyncResumed() {
        print("▶️ 收到CloudKit同步恢复通知")
        DispatchQueue.main.async {
            // 恢复同步后，检查CloudKit可用性
            self.checkCloudKitAvailability()
        }
    }
    
    /**
     * 设置后台自动同步机制
     */
    private func setupBackgroundSync() {
        // 避免在预览环境中启动定时器
        #if !DEBUG
        // 使用GCD timer确保即使在后台也能触发
        let timer = DispatchSource.makeTimerSource(queue: DispatchQueue.global())
        timer.schedule(deadline: .now() + 900, repeating: 900) // 每15分钟
        timer.setEventHandler { [weak self] in
            guard let self = self else { return }
            
            // 在主线程上执行同步
            DispatchQueue.main.async {
                self.checkCloudKitAvailability()
                
                // 如果CloudKit可用，触发同步
                if self.cloudKitStatus == .available {
                    self.triggerCloudKitSync()
                }
            }
        }
        timer.resume()
        #endif
    }
    
    /**
     * 处理CloudKit导入通知
     */
    @objc private func handleCloudKitImport() {
        // 检查是否暂停了CloudKit通知处理
        if UserDefaults.standard.bool(forKey: "cloudkit_notifications_disabled") {
            print("⏸️ CloudKit通知处理已暂停，跳过导入处理")
            return
        }

        print("📥 CloudKit数据导入中...")

        DispatchQueue.main.async {
            self.cloudKitStatus = .syncInProgress
            self.isSyncing = true

            self.viewContext.perform {
                self.viewContext.refreshAllObjects()

                // 执行数据一致性检查和修复
                    self.performDataConsistencyCheck()

                DispatchQueue.main.async {
                    self.cloudKitStatus = .syncCompleted
                    self.isSyncing = false
                    self.lastSyncDate = Date()
                    print("✅ CloudKit数据导入完成")

                    // 发送同步完成通知
                    NotificationCenter.default.post(
                        name: NSNotification.Name("CloudKitSyncCompleted"),
                        object: nil
                    )
                }
            }
        }
    }
    
    /**
     * 处理CloudKit导出通知
     */
    @objc private func handleCloudKitExport() {
        // 检查是否暂停了CloudKit通知处理
        if UserDefaults.standard.bool(forKey: "cloudkit_notifications_disabled") {
            print("⏸️ CloudKit通知处理已暂停，跳过导出处理")
            return
        }
        
        print("📤 CloudKit数据导出中...")
        
        DispatchQueue.main.async {
            self.cloudKitStatus = .syncInProgress
            self.isSyncing = true
        }
    }
    
    // MARK: - Subscription & CloudKit Management
    
    /**
     * 检查并同步订阅状态
     */
    private func checkAndSyncSubscriptionStatus() {
        // 这里可以添加检查并同步订阅状态的逻辑
        // 例如：从服务器获取订阅状态并更新本地数据
        
        // 这里暂时保留一个空的实现
        print("✅ 订阅状态检查完成")
    }
    
    /**
     * 执行待处理的数据迁移（简化版）
     * 由于统一使用CloudKit，大部分迁移逻辑已不再需要
     */
    static func performPendingMigrationIfNeeded() {
        print("ℹ️ 迁移检查：所有用户已统一使用CloudKit同步，无需迁移")
    }
    
    // MARK: - CloudKit Sync Status
    
    /**
     * 获取CloudKit同步状态
     */
    var cloudKitSyncEnabled: Bool {
        return persistenceController.isCloudKitEnabled
    }
    
    /**
     * 手动触发CloudKit同步（强制同步版本）
     */
    func triggerCloudKitSync() {
        guard cloudKitSyncEnabled else {
            print("⚠️ CloudKit同步未启用")
            return
        }
        
        // 检查是否暂停了CloudKit同步
        if UserDefaults.standard.bool(forKey: "cloudkit_sync_paused") {
            print("⏸️ CloudKit同步已暂停，跳过触发同步")
            return
        }
        
        print("🔄 强制触发CloudKit同步...")
        
        DispatchQueue.main.async {
            self.isSyncing = true
            self.cloudKitStatus = .syncInProgress
        }
        
        // 强制保存到CloudKit
        let container = persistenceController.container
        
        container.viewContext.perform {
            do {
                // 如果有未保存的更改，先保存
                if self.viewContext.hasChanges {
                    try self.viewContext.save()
                    print("✅ 本地数据已保存")
                }
                
                // 强制刷新所有对象以触发CloudKit同步
                self.viewContext.refreshAllObjects()
                
                // 触发导出操作
                try self.viewContext.save()
                
                DispatchQueue.main.async {
                    self.cloudKitStatus = .syncCompleted
                    self.isSyncing = false
                    self.lastSyncDate = Date()
                    print("✅ CloudKit同步触发完成")
                }
                
            } catch {
                print("❌ CloudKit同步失败: \(error)")
                
                if let ckError = error as? CKError {
                    print("CloudKit错误详情: \(ckError.localizedDescription)")
                    print("错误代码: \(ckError.code)")
                    
                    // 处理特定的CloudKit错误
                    switch ckError.code {
                    case .networkUnavailable:
                        print("网络不可用，请检查网络连接")
                    case .notAuthenticated:
                        print("未登录iCloud账户")
                    case .quotaExceeded:
                        print("iCloud存储空间不足")
                    case .zoneBusy:
                        print("CloudKit区域繁忙，稍后重试")
                    default:
                        print("其他CloudKit错误")
                    }
                }
                
                DispatchQueue.main.async {
                    self.cloudKitStatus = .syncFailed(error)
                    self.isSyncing = false
                    self.syncError = error
                }
            }
        }
    }
    
    // MARK: - User Management
    
    /**
     * 创建用户
     */
    @discardableResult
    func createUser(nickname: String, email: String) -> User {
        let user = User.create(nickname: nickname, email: email, in: viewContext)
        saveWithSync()
        return user
    }
    
    /**
     * 获取当前用户（优先根据Apple ID，兼容旧逻辑）
     */
    func getCurrentUser() -> User? {
        // 首先尝试从AuthenticationManager获取当前登录用户
        if let authManager = getAuthenticationManager(),
           let currentUser = authManager.currentUser {
            print("🔗 获取登录用户: \(currentUser.name ?? "Unknown") (Apple ID: \(currentUser.appleUserID ?? "N/A"))")
            return currentUser
        }
        
        // 如果没有登录用户，优先选择有Apple ID的用户
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID != nil AND appleUserID != ''")
        request.sortDescriptors = [NSSortDescriptor(key: "lastLoginAt", ascending: false)]
        request.fetchLimit = 1
        
        do {
            let usersWithAppleID = try viewContext.fetch(request)
            if let appleUser = usersWithAppleID.first {
                print("🔗 使用Apple ID用户: \(appleUser.name ?? "Unknown") (Apple ID: \(appleUser.appleUserID ?? "N/A"))")
                
                // 尝试重新设置到AuthenticationManager
                if let authManager = getAuthenticationManager() {
                    authManager.currentUser = appleUser
                    authManager.isLoggedIn = true
                    print("🔄 已同步用户到AuthenticationManager")
                }
                
                return appleUser
            }
            
            // 如果没有Apple ID用户，获取第一个用户（兼容旧逻辑）
            let allUsersRequest: NSFetchRequest<User> = User.fetchRequest()
            allUsersRequest.fetchLimit = 1
            let allUsers = try viewContext.fetch(allUsersRequest)
            let firstUser = allUsers.first
            
            if let firstUser = firstUser {
                print("⚠️ 使用第一个用户: \(firstUser.name ?? "Unknown") (Apple ID: \(firstUser.appleUserID ?? "N/A"))")
            }
            return firstUser
            
        } catch {
            print("❌ 获取当前用户失败: \(error)")
            return nil
        }
    }
    
    /**
     * 获取或创建当前用户（统一用户关联逻辑）
     */
    func getOrCreateDefaultUser() -> User {
        // 优先获取当前登录的用户
        if let authManager = getAuthenticationManager(),
           let currentUser = authManager.currentUser {
            print("🔗 使用登录用户: \(currentUser.name ?? "Unknown") (Apple ID: \(currentUser.appleUserID ?? "N/A"))")
            return currentUser
        }
        
        // 如果没有登录用户，按旧逻辑处理
        if let existingUser = getCurrentUser() {
            print("⚠️ 使用第一个用户: \(existingUser.name ?? "Unknown") (Apple ID: \(existingUser.appleUserID ?? "N/A"))")
            return existingUser
        } else {
            let newUser = createUser(nickname: "默认用户", email: "<EMAIL>")
            print("🆕 创建新的默认用户: \(newUser.nickname ?? "Unknown")")
            return newUser
        }
    }
    
    /**
     * 获取AuthenticationManager实例（避免循环依赖）
     */
    private func getAuthenticationManager() -> AuthenticationManager? {
        // 通过Registry获取AuthenticationManager
        return AuthenticationManagerRegistry.shared.currentManager
    }
    
    /**
     * 设置当前用户（用于登录后同步用户状态）
     */
    func setCurrentUser(_ user: User) {
        // 确保用户数据一致性
        if user.managedObjectContext != viewContext {
            print("⚠️ 用户对象的上下文不匹配，尝试同步...")
            
            // 如果用户来自不同的上下文，需要在当前上下文中查找对应用户
            if let appleUserID = user.appleUserID {
                if let localUser = getUserByAppleID(appleUserID) {
                    print("✅ 找到本地对应用户: \(localUser.name ?? "Unknown")")
                    
                    // 通知AuthenticationManager更新当前用户
                    if let authManager = getAuthenticationManager() {
                        authManager.currentUser = localUser
                        print("🔄 已同步AuthenticationManager当前用户")
                    }
                    return
                }
            }
        }
        
        print("🔄 设置当前用户: \(user.name ?? "Unknown") (Apple ID: \(user.appleUserID ?? "N/A"))")
        
        // 通知AuthenticationManager更新当前用户
        if let authManager = getAuthenticationManager() {
            authManager.currentUser = user
        }
    }

    /**
     * 验证用户是否可以进行CloudKit同步
     */
    private func validateUserForSync() -> Bool {
        guard let currentUser = getCurrentUser() else {
            print("❌ 没有当前用户")
            return false
        }
        
        if currentUser.appleUserID == nil {
            print("❌ 用户未绑定Apple ID，无法进行CloudKit同步")
            return false
        }
        
        return true
    }
    
    // MARK: - Class Management
    
    /**
     * 创建班级（增强版本，包含同步验证）
     */
    @discardableResult
    func createClass(name: String, for user: User? = nil) -> SchoolClass {
        let owner = user ?? getOrCreateDefaultUser()
        
        // 验证用户是否可以进行CloudKit同步
        if !validateUserForSync() {
            print("⚠️ 用户验证失败，数据可能无法同步到其他设备")
        }
        
        let schoolClass = SchoolClass.create(name: name, owner: owner, in: viewContext)
        
        // 不再为新班级添加默认规则和奖品
        
        // 使用增强的保存方法
        saveWithSync()
        
        print("✅ 班级 '\(name)' 创建成功，已触发CloudKit同步")
        
        return schoolClass
    }
    
    /**
     * 获取用户的所有班级
     */
    func getClasses(for user: User? = nil) -> [SchoolClass] {
        let owner = user ?? getOrCreateDefaultUser()
        return owner.sortedClasses
    }
    
    /**
     * 删除班级
     */
    func deleteClass(_ schoolClass: SchoolClass) {
        viewContext.delete(schoolClass)
        saveWithSync()
    }
    
    /**
     * 获取用户的第一个班级（默认班级）
     */
    func getDefaultClass(for user: User? = nil) -> SchoolClass? {
        let classes = getClasses(for: user)
        return classes.first
    }
    
    // MARK: - Student Management
    
    /**
     * 创建学生
     */
    @discardableResult
    func createStudent(
        name: String,
        number: String,
        gender: String,
        in schoolClass: SchoolClass
    ) -> Student {
        let student = Student.create(
            name: name,
            studentNumber: number,
            gender: gender,
            in: schoolClass,
            context: viewContext
        )
        saveWithSync()
        return student
    }
    
    /**
     * 创建学生（支持初始积分）
     */
    @discardableResult
    func createStudent(
        name: String,
        number: String,
        gender: String,
        initialPoints: Int = 0,
        in schoolClass: SchoolClass
    ) -> Student {
        print("📝 CoreDataManager.createStudent - 姓名: \(name), 初始积分: \(initialPoints)")

        let student = Student.create(
            name: name,
            studentNumber: number,
            gender: gender,
            in: schoolClass,
            context: viewContext
        )

        print("📝 Student.create完成 - 当前积分: \(student.point)")

        // 设置初始积分
        if initialPoints > 0 {
            print("📝 设置初始积分: \(initialPoints)")
            student.point = Int32(initialPoints)
            print("📝 积分设置完成 - 当前积分: \(student.point)")
        } else {
            print("📝 初始积分为0或负数，跳过设置")
        }

        saveWithSync()
        print("📝 数据保存完成 - 最终积分: \(student.point)")
        return student
    }
    
    /**
     * 检查学号是否已存在
     */
    func isStudentNumberExists(_ studentNumber: String, in schoolClass: SchoolClass) -> Bool {
        let request: NSFetchRequest<Student> = Student.fetchRequest()
        request.predicate = NSPredicate(
            format: "studentNumber == %@ AND schoolClass == %@",
            studentNumber,
            schoolClass
        )
        request.fetchLimit = 1
        
        do {
            let count = try viewContext.count(for: request)
            return count > 0
        } catch {
            print("检查学号重复失败: \(error)")
            return false
        }
    }
    
    /**
     * 获取班级的所有学生
     */
    func getStudents(in schoolClass: SchoolClass) -> [Student] {
        return schoolClass.sortedStudents
    }
    
    /**
     * 删除学生
     */
    func deleteStudent(_ student: Student) {
        viewContext.delete(student)
        saveWithSync()
    }
    
    /**
     * 更新学生积分
     */
    func updateStudentPoints(_ student: Student, points: Int) {
        student.point = Int32(points)
        saveWithSync()
    }
    
    /**
     * 根据ID查找学生
     */
    func getStudent(by id: UUID) -> Student? {
        let request: NSFetchRequest<Student> = Student.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", id as CVarArg)
        request.fetchLimit = 1

        do {
            let students = try viewContext.fetch(request)
            return students.first
        } catch {
            print("查找学生失败: \(error)")
            return nil
        }
    }

    /**
     * 根据ID查找家庭成员
     */
    func getFamilyMember(by id: UUID) -> FamilyMember? {
        let request: NSFetchRequest<FamilyMember> = FamilyMember.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", id as CVarArg)
        request.fetchLimit = 1

        do {
            let members = try viewContext.fetch(request)
            return members.first
        } catch {
            print("查找家庭成员失败: \(error)")
            return nil
        }
    }
    
    // MARK: - Family Management

    /**
     * 创建家庭
     */
    @discardableResult
    func createFamily(name: String, for user: User? = nil) -> Family {
        let owner = user ?? getOrCreateDefaultUser()

        // 验证用户是否可以进行CloudKit同步
        if !validateUserForSync() {
            print("⚠️ 用户验证失败，数据可能无法同步到其他设备")
        }

        let family = Family.create(name: name, owner: owner, in: viewContext)
        saveWithSync()

        print("✅ 家庭 '\(name)' 创建成功，已触发CloudKit同步")
        return family
    }

    /**
     * 获取用户的所有家庭
     */
    func getFamilies(for user: User? = nil) -> [Family] {
        let owner = user ?? getOrCreateDefaultUser()
        return owner.sortedFamilies
    }

    /**
     * 删除家庭
     */
    func deleteFamily(_ family: Family) {
        viewContext.delete(family)
        saveWithSync()
    }

    /**
     * 获取用户的第一个家庭（默认家庭）
     */
    func getDefaultFamily(for user: User? = nil) -> Family? {
        let families = getFamilies(for: user)
        return families.first
    }

    // MARK: - Family Member Management

    /**
     * 修复所有FamilyMember的头像数据
     */
    func fixAllFamilyMemberAvatars() {
        let request: NSFetchRequest<FamilyMember> = FamilyMember.fetchRequest()

        do {
            let allMembers = try viewContext.fetch(request)
            var fixedCount = 0

            for member in allMembers {
                let oldAvatar = member.avatar
                member.fixAvatarData()
                if oldAvatar != member.avatar {
                    fixedCount += 1
                }
            }

            if fixedCount > 0 {
                saveWithSync()
                print("✅ 修复了 \(fixedCount) 个FamilyMember的头像数据")
            } else {
                print("ℹ️ 没有需要修复的FamilyMember头像数据")
            }
        } catch {
            print("❌ 修复FamilyMember头像数据失败: \(error)")
        }
    }

    /**
     * 创建家庭成员
     */
    @discardableResult
    func createFamilyMember(
        name: String,
        role: String,
        gender: String,
        age: Int16 = 0,
        in family: Family
    ) -> FamilyMember {
        let member = family.addMember(
            name: name,
            role: role,
            gender: gender,
            age: age,
            in: viewContext
        )
        saveWithSync()
        return member
    }

    /**
     * 创建家庭成员（支持初始积分）
     */
    @discardableResult
    func createFamilyMember(
        name: String,
        role: String,
        gender: String,
        age: Int16 = 0,
        initialPoints: Int = 0,
        in family: Family
    ) -> FamilyMember {
        print("👨‍👩‍👧‍👦 CoreDataManager.createFamilyMember - 姓名: \(name), 角色: \(role), 初始积分: \(initialPoints)")

        let member = family.addMember(
            name: name,
            role: role,
            gender: gender,
            age: age,
            in: viewContext
        )

        print("👨‍👩‍👧‍👦 FamilyMember.create完成 - 当前积分: \(member.currentPoints)")

        // 设置初始积分
        if initialPoints > 0 {
            print("👨‍👩‍👧‍👦 设置初始积分: \(initialPoints)")
            member.currentPoints = Int32(initialPoints)
            print("👨‍👩‍👧‍👦 积分设置完成 - 当前积分: \(member.currentPoints)")
        } else {
            print("👨‍👩‍👧‍👦 初始积分为0或负数，跳过设置")
        }

        saveWithSync()
        print("👨‍👩‍👧‍👦 数据保存完成 - 最终积分: \(member.currentPoints)")
        return member
    }

    /**
     * 获取家庭的所有成员
     */
    func getFamilyMembers(in family: Family) -> [FamilyMember] {
        return family.sortedMembers
    }

    /**
     * 删除家庭成员
     */
    func deleteFamilyMember(_ member: FamilyMember) {
        viewContext.delete(member)
        saveWithSync()
    }

    /**
     * 更新家庭成员积分
     */
    func updateFamilyMemberPoints(_ member: FamilyMember, points: Int) {
        member.currentPoints = Int32(points)
        member.updatedAt = Date()
        member.family?.updateTotalPoints()
        saveWithSync()
    }



    // MARK: - Family Point Record Management

    /**
     * 添加家庭成员积分记录
     */
    @discardableResult
    func addFamilyPointRecord(to member: FamilyMember, reason: String, value: Int) -> FamilyPointRecord {
        let record = member.addPoints(value, reason: reason, in: viewContext)
        saveWithSync()
        return record
    }

    /**
     * 添加家庭成员兑换记录
     */
    @discardableResult
    func addFamilyRedemptionRecord(
        to member: FamilyMember,
        prizeName: String,
        cost: Int
    ) -> FamilyRedemptionRecord? {
        // 检查积分是否足够
        guard Int(member.currentPoints) >= cost else { return nil }

        let record = FamilyRedemptionRecord.create(
            prizeName: prizeName,
            cost: cost,
            for: member,
            in: viewContext
        )

        // 扣除积分
        member.currentPoints -= Int32(cost)
        member.updatedAt = Date()
        member.family?.updateTotalPoints()

        saveWithSync()
        return record
    }

    /**
     * 添加家庭成员抽奖记录
     */
    @discardableResult
    func addFamilyLotteryRecord(
        to member: FamilyMember,
        toolType: String,
        prizeResult: String,
        cost: Int
    ) -> FamilyLotteryRecord? {
        // 检查积分是否足够
        guard Int(member.currentPoints) >= cost else { return nil }

        let record = FamilyLotteryRecord.create(
            toolType: toolType,
            prizeResult: prizeResult,
            cost: cost,
            for: member,
            in: viewContext
        )

        // 扣除积分
        member.currentPoints -= Int32(cost)
        member.updatedAt = Date()
        member.family?.updateTotalPoints()

        saveWithSync()
        return record
    }

    // MARK: - Family Member Rule Management

    /**
     * 为家庭成员添加规则
     */
    @discardableResult
    func addFamilyMemberRule(
        to member: FamilyMember,
        name: String,
        value: Int32,
        type: String,
        isFrequent: Bool = false
    ) -> FamilyMemberRule {
        let rule = member.addMemberRule(
            name: name,
            value: value,
            type: type,
            isFrequent: isFrequent,
            in: viewContext
        )
        saveWithSync()
        return rule
    }

    /**
     * 删除家庭成员规则
     */
    func deleteFamilyMemberRule(_ rule: FamilyMemberRule) {
        guard let member = rule.member else { return }
        member.removeMemberRule(rule, in: viewContext)
        saveWithSync()
    }

    /**
     * 获取家庭成员的常用规则
     */
    func getFrequentMemberRules(for member: FamilyMember, type: String) -> [FamilyMemberRule] {
        let request: NSFetchRequest<FamilyMemberRule> = FamilyMemberRule.fetchRequest()
        request.predicate = NSPredicate(
            format: "member == %@ AND type == %@ AND isFrequent == %@",
            member, type, NSNumber(value: true)
        )
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \FamilyMemberRule.name, ascending: true)
        ]

        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取成员常用规则失败: \(error)")
            return []
        }
    }

    /**
     * 获取家庭成员的所有规则
     */
    func getAllMemberRules(for member: FamilyMember) -> [FamilyMemberRule] {
        return member.sortedMemberRules
    }

    /**
     * 应用成员规则
     */
    @discardableResult
    func applyMemberRule(_ rule: FamilyMemberRule) -> FamilyPointRecord? {
        guard let member = rule.member else { return nil }
        let record = member.applyMemberRule(rule, in: viewContext)
        saveWithSync()
        return record
    }

    /**
     * 批量创建成员规则
     */
    func createMemberRules(for member: FamilyMember, rules: [(name: String, value: Int32, type: String, isFrequent: Bool)]) {
        for ruleData in rules {
            _ = addFamilyMemberRule(
                to: member,
                name: ruleData.name,
                value: ruleData.value,
                type: ruleData.type,
                isFrequent: ruleData.isFrequent
            )
        }
    }

    /**
     * 根据ID查找家庭成员规则
     */
    func getFamilyMemberRule(by id: UUID) -> FamilyMemberRule? {
        let request: NSFetchRequest<FamilyMemberRule> = FamilyMemberRule.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", id as CVarArg)
        request.fetchLimit = 1

        do {
            let rules = try viewContext.fetch(request)
            return rules.first
        } catch {
            print("查找成员规则失败: \(error)")
            return nil
        }
    }

    /**
     * 更新家庭成员规则
     */
    func updateFamilyMemberRule(_ rule: FamilyMemberRule, name: String, value: Int32, type: String) {
        rule.update(name: name, value: value, type: type)
        saveWithSync()
    }

    /**
     * 切换家庭成员规则的常用状态
     */
    func toggleFamilyMemberRuleFrequent(_ rule: FamilyMemberRule) {
        rule.toggleFrequent()
        saveWithSync()
    }

    // MARK: - Record Management

    /**
     * 添加积分记录
     */
    @discardableResult
    func addPointRecord(to student: Student, reason: String, value: Int) -> PointRecord {
        let record = student.addPoints(value, reason: reason, in: viewContext)
        saveWithSync()
        return record
    }
    
    /**
     * 获取学生的积分记录
     */
    func getPointRecords(for student: Student) -> [PointRecord] {
        return student.sortedPointRecords
    }
    
    /**
     * 添加兑换记录
     */
    @discardableResult
    func addRedemptionRecord(
        to student: Student,
        prizeName: String,
        cost: Int
    ) -> RedemptionRecord? {
        guard let record = student.redeemPrize(prizeName: prizeName, cost: cost, in: viewContext) else {
            return nil
        }
        saveWithSync()
        return record
    }
    
    /**
     * 添加抽奖记录
     */
    @discardableResult
    func addLotteryRecord(
        to student: Student,
        toolType: String,
        prizeResult: String,
        cost: Int
    ) -> LotteryRecord? {
        guard let record = student.addLotteryRecord(
            toolType: toolType,
            prizeResult: prizeResult,
            cost: cost,
            in: viewContext
        ) else {
            return nil
        }
        saveWithSync()
        return record
    }
    
    // MARK: - Rule and Prize Management
    
    /**
     * 为班级添加规则
     */
    @discardableResult
    func addRule(
        to schoolClass: SchoolClass,
        name: String,
        value: Int,
        type: String,
        isFrequent: Bool = false
    ) -> Rule {
        let rule = Rule.create(
            name: name,
            value: value,
            type: type,
            isFrequent: isFrequent,
            in: schoolClass,
            context: viewContext
        )
        saveWithSync()
        return rule
    }
    
    /**
     * 获取班级的常用规则
     */
    func getFrequentRules(for schoolClass: SchoolClass, type: String) -> [Rule] {
        let request: NSFetchRequest<Rule> = Rule.fetchRequest()
        request.predicate = NSPredicate(
            format: "schoolClass == %@ AND type == %@ AND isFrequent == %@",
            schoolClass, type, NSNumber(value: true)
        )
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Rule.name, ascending: true)
        ]
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取常用规则失败: \(error)")
            return []
        }
    }
    
    /**
     * 清理超出订阅级别限制的规则
     * 当用户订阅级别降级时调用
     */
    func cleanupExcessRules(for schoolClass: SchoolClass, userSubscriptionLevel: Subscription.Level) {
        let maxRulesCount = userSubscriptionLevel == .free ? 5 : 10
        
        // 清理加分规则
        cleanupRulesOfType("add", for: schoolClass, maxCount: maxRulesCount)
        
        // 清理扣分规则
        cleanupRulesOfType("deduct", for: schoolClass, maxCount: maxRulesCount)
        
        saveWithSync()
    }
    
    /**
     * 清理指定类型的超出限制的规则
     */
    private func cleanupRulesOfType(_ type: String, for schoolClass: SchoolClass, maxCount: Int) {
        let allRules = getFrequentRules(for: schoolClass, type: type)
        
        if allRules.count > maxCount {
            // 删除超出限制的规则（保留前maxCount条，删除后面的）
            let excessRules = Array(allRules.suffix(allRules.count - maxCount))
            
            for rule in excessRules {
                viewContext.delete(rule)
                print("删除超出限制的\(type == "add" ? "加分" : "扣分")规则: \(rule.name ?? "")")
            }
            
            print("已清理\(type == "add" ? "加分" : "扣分")规则，从\(allRules.count)条减少到\(maxCount)条")
        }
    }
    
    /**
     * 为班级添加奖品
     */
    @discardableResult
    func addPrize(
        to schoolClass: SchoolClass,
        name: String,
        cost: Int,
        type: String
    ) -> Prize {
        let prize = Prize.create(
            name: name,
            cost: cost,
            type: type,
            in: schoolClass,
            context: viewContext
        )
        saveWithSync()
        return prize
    }
    
    // MARK: - Template Management
    
    /**
     * 创建规则模板
     */
    @discardableResult
    func createRuleTemplate(name: String, value: Int, type: String) -> RuleTemplate {
        let template = RuleTemplate.create(name: name, value: value, type: type, in: viewContext)
        saveWithSync()
        return template
    }
    
    /**
     * 创建奖品模板
     */
    @discardableResult
    func createPrizeTemplate(name: String, cost: Int, type: String) -> PrizeTemplate {
        let template = PrizeTemplate.create(name: name, cost: cost, type: type, in: viewContext)
        saveWithSync()
        return template
    }
    
    /**
     * 获取所有规则模板
     */
    func getAllRuleTemplates() -> [RuleTemplate] {
        let request: NSFetchRequest<RuleTemplate> = RuleTemplate.fetchRequest()
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取规则模板失败: \(error)")
            return []
        }
    }
    
    /**
     * 获取所有奖品模板
     */
    func getAllPrizeTemplates() -> [PrizeTemplate] {
        let request: NSFetchRequest<PrizeTemplate> = PrizeTemplate.fetchRequest()
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取奖品模板失败: \(error)")
            return []
        }
    }
    
    // MARK: - Lottery Tool Config Management
    
    /**
     * 创建抽奖道具配置
     */
    @discardableResult
    func createLotteryToolConfig(
        for schoolClass: SchoolClass,
        toolType: String,
        itemCount: Int,
        costPerPlay: Int
    ) -> LotteryToolConfig {
        let config = LotteryToolConfig.create(
            toolType: toolType,
            itemCount: itemCount,
            costPerPlay: costPerPlay,
            for: schoolClass,
            in: viewContext
        )
        saveWithSync()
        return config
    }
    
    /**
     * 更新抽奖道具配置
     */
    func updateLotteryToolConfig(
        _ config: LotteryToolConfig,
        itemCount: Int,
        costPerPlay: Int
    ) {
        config.updateConfig(itemCount: itemCount, costPerPlay: costPerPlay)
        saveWithSync()
    }
    
    /**
     * 删除抽奖道具配置
     */
    func deleteLotteryToolConfig(_ config: LotteryToolConfig) {
        viewContext.delete(config)
        saveWithSync()
    }
    
    /**
     * 获取班级的抽奖道具配置列表
     */
    func getLotteryToolConfigs(for schoolClass: SchoolClass) -> [LotteryToolConfig] {
        let request: NSFetchRequest<LotteryToolConfig> = LotteryToolConfig.fetchRequest()
        request.predicate = NSPredicate(format: "schoolClass == %@", schoolClass)
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \LotteryToolConfig.toolType, ascending: true),
            NSSortDescriptor(keyPath: \LotteryToolConfig.createdAt, ascending: true)
        ]
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取抽奖道具配置失败: \(error)")
            return []
        }
    }
    
    /**
     * 根据道具类型获取配置
     */
    func getLotteryToolConfig(
        for schoolClass: SchoolClass,
        toolType: String
    ) -> LotteryToolConfig? {
        let request: NSFetchRequest<LotteryToolConfig> = LotteryToolConfig.fetchRequest()
        request.predicate = NSPredicate(
            format: "schoolClass == %@ AND toolType == %@",
            schoolClass, toolType
        )
        request.fetchLimit = 1
        
        do {
            return try viewContext.fetch(request).first
        } catch {
            print("获取抽奖道具配置失败: \(error)")
            return nil
        }
    }
    
    /**
     * 创建抽奖道具项目
     */
    @discardableResult
    func createLotteryToolItem(
        for config: LotteryToolConfig,
        index: Int,
        prizeName: String
    ) -> LotteryToolItem {
        let item = LotteryToolItem.create(
            index: index,
            prizeName: prizeName,
            for: config,
            in: viewContext
        )
        saveWithSync()
        return item
    }
    
    /**
     * 更新抽奖道具项目
     */
    func updateLotteryToolItem(
        _ item: LotteryToolItem,
        prizeName: String
    ) {
        item.updatePrizeName(prizeName)
        saveWithSync()
    }
    
    /**
     * 删除抽奖道具项目
     */
    func deleteLotteryToolItem(_ item: LotteryToolItem) {
        viewContext.delete(item)
        saveWithSync()
    }
    
    /**
     * 批量创建抽奖道具项目
     */
    func createLotteryToolItems(
        for config: LotteryToolConfig,
        prizeNames: [String]
    ) {
        config.createDefaultItems(prizeNames: prizeNames, in: viewContext)
        saveWithSync()
    }
    
    /**
     * 获取指定配置的所有项目
     */
    func getLotteryToolItems(for config: LotteryToolConfig) -> [LotteryToolItem] {
        let request: NSFetchRequest<LotteryToolItem> = LotteryToolItem.fetchRequest()
        request.predicate = NSPredicate(format: "lotteryConfig == %@", config)
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \LotteryToolItem.itemIndex, ascending: true)
        ]
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取抽奖道具项目失败: \(error)")
            return []
        }
    }
    
    /**
     * 检查班级是否已配置指定道具类型
     */
    func hasLotteryToolConfig(
        for schoolClass: SchoolClass,
        toolType: String
    ) -> Bool {
        return getLotteryToolConfig(for: schoolClass, toolType: toolType) != nil
    }
    
    // MARK: - Core Operations
    
    /**
     * 保存上下文（增强版本，包含详细日志）
     */
    func save() {
        guard viewContext.hasChanges else { 
            print("ℹ️ 没有需要保存的数据变更")
            return 
        }
        
        print("💾 准备保存数据到CloudKit...")
        
        do {
            try viewContext.save()
            print("✅ 数据已保存到本地，CloudKit同步中...")
            
            DispatchQueue.main.async {
                self.lastSyncDate = Date()
            }
            
        } catch {
            print("❌ 数据保存失败: \(error)")
            
            if let ckError = error as? CKError {
                print("CloudKit错误详情: \(ckError.localizedDescription)")
                print("错误代码: \(ckError.code)")
                
                DispatchQueue.main.async {
                    self.syncError = error
                    self.cloudKitStatus = .syncFailed(error)
                }
            }
            
            // 在非调试模式下，可以尝试重试保存
            #if !DEBUG
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.save()
            }
            #endif
        }
    }
    
    /**
     * 保存并触发同步
     */
    func saveWithSync() {
        save()
        
        // 在保存后延迟触发同步，确保数据已写入
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.triggerCloudKitSync()
        }
    }
    
    /**
     * 删除对象
     */
    func delete<T: NSManagedObject>(_ object: T) {
        viewContext.delete(object)
        saveWithSync()
    }
    
    /**
     * 获取CloudKit同步状态文本
     */
    func getCloudKitStatusText() -> String {
        return cloudKitStatus.displayText
    }
    
    /**
     * 获取上次同步时间文本
     */
    func getLastSyncTimeText() -> String {
        guard let lastSync = lastSyncDate else {
            return "从未同步"
        }
        
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return "上次同步: \(formatter.string(from: lastSync))"
    }
    
    /**
     * 执行数据一致性检查和修复
     */
    func performDataConsistencyCheck() {
        print("🔍 开始数据一致性检查...")

        do {
            // 1. 检查用户数据一致性
            checkUserDataConsistency()

            // 2. 检查班级-用户关联
            checkClassOwnershipConsistency()

            // 3. 检查重复数据
            removeDuplicateData()

            // 4. 同步AuthenticationManager状态
            syncAuthenticationManagerState()

            // 5. 保存修复结果
            if viewContext.hasChanges {
                try viewContext.save()
                print("✅ 数据一致性修复完成")
            } else {
                print("ℹ️ 数据一致性检查通过，无需修复")
            }

        } catch {
            print("❌ 数据一致性检查失败: \(error)")
        }
    }
    
    /**
     * 同步AuthenticationManager状态
     * 注意：此方法不再自动设置登录状态，避免用户退出登录后自动重新登录
     */
    private func syncAuthenticationManagerState() {
        guard let authManager = getAuthenticationManager() else {
            print("⚠️ AuthenticationManager未注册")
            return
        }
        
        // 检查是否有明确的登录状态标记
        let isLoggedIn = UserDefaults.standard.bool(forKey: "user_is_logged_in")
        let appleUserID = UserDefaults.standard.string(forKey: "apple_user_id")
        
        // 只有在UserDefaults中明确标记为已登录且有Apple用户ID时，才同步用户数据
        if isLoggedIn, let userID = appleUserID, !userID.isEmpty, authManager.currentUser == nil {
            let request: NSFetchRequest<User> = User.fetchRequest()
            request.predicate = NSPredicate(format: "appleUserID == %@", userID)
            request.fetchLimit = 1
            
            do {
                let users = try viewContext.fetch(request)
                if let user = users.first {
                    print("🔄 根据UserDefaults同步用户到AuthenticationManager: \(user.name ?? "Unknown")")
                    authManager.currentUser = user
                    authManager.isLoggedIn = true
                } else {
                    print("⚠️ UserDefaults中的Apple用户ID在数据库中未找到，清除登录状态")
                    // 如果UserDefaults中的用户ID在数据库中不存在，清除登录状态
                    authManager.clearInvalidLoginState()
                }
            } catch {
                print("❌ 同步AuthenticationManager状态失败: \(error)")
            }
        } else if !isLoggedIn {
            // 如果UserDefaults中明确标记为未登录，确保AuthenticationManager状态一致
            if authManager.isLoggedIn {
                print("🔄 根据UserDefaults清除AuthenticationManager登录状态")
                authManager.currentUser = nil
                authManager.isLoggedIn = false
            }
        }
    }
    
    /**
     * 检查用户数据一致性
     */
    private func checkUserDataConsistency() {
        let request: NSFetchRequest<User> = User.fetchRequest()
        
        do {
            let users = try viewContext.fetch(request)
            var appleIDUsers: [String: User] = [:]
            var usersToDelete: [User] = []
            
            for user in users {
                if let appleUserID = user.appleUserID, !appleUserID.isEmpty {
                    if let existingUser = appleIDUsers[appleUserID] {
                        // 发现重复的Apple ID用户，合并数据
                        print("⚠️ 发现重复Apple ID用户: \(appleUserID)")
                        mergeUserData(from: user, to: existingUser)
                        usersToDelete.append(user)
                    } else {
                        appleIDUsers[appleUserID] = user
                    }
                }
            }
            
            // 删除重复用户
            for user in usersToDelete {
                viewContext.delete(user)
                print("🗑️ 删除重复用户: \(user.name ?? "Unknown")")
            }
            
        } catch {
            print("❌ 用户数据一致性检查失败: \(error)")
        }
    }
    
    /**
     * 检查班级所有权一致性
     */
    private func checkClassOwnershipConsistency() {
        let request: NSFetchRequest<SchoolClass> = SchoolClass.fetchRequest()
        
        do {
            let classes = try viewContext.fetch(request)
            
            for schoolClass in classes {
                if schoolClass.owner == nil {
                    // 找一个合适的用户作为所有者
                    if let user = getCurrentUser() {
                        schoolClass.owner = user
                        print("🔧 修复班级所有权: \(schoolClass.name ?? "Unknown") -> \(user.name ?? "Unknown")")
                    }
                }
            }
            
        } catch {
            print("❌ 班级所有权检查失败: \(error)")
        }
    }
    
    /**
     * 移除重复数据
     */
    private func removeDuplicateData() {
        // 检查重复班级
        let classRequest: NSFetchRequest<SchoolClass> = SchoolClass.fetchRequest()
        
        do {
            let classes = try viewContext.fetch(classRequest)
            var classNames: [String: SchoolClass] = [:]
            var classesToDelete: [SchoolClass] = []
            
            for schoolClass in classes {
                let key = "\(schoolClass.name ?? "")_\(schoolClass.owner?.id?.uuidString ?? "")"
                if let existingClass = classNames[key] {
                    // 发现重复班级
                    print("⚠️ 发现重复班级: \(schoolClass.name ?? "Unknown")")
                    mergeClassData(from: schoolClass, to: existingClass)
                    classesToDelete.append(schoolClass)
                } else {
                    classNames[key] = schoolClass
                }
            }
            
            // 删除重复班级
            for schoolClass in classesToDelete {
                viewContext.delete(schoolClass)
                print("🗑️ 删除重复班级: \(schoolClass.name ?? "Unknown")")
            }
            
        } catch {
            print("❌ 重复数据检查失败: \(error)")
        }
    }
    
    /**
     * 合并用户数据
     */
    private func mergeUserData(from sourceUser: User, to targetUser: User) {
        // 合并班级
        if let sourceClasses = sourceUser.classes?.allObjects as? [SchoolClass] {
            for schoolClass in sourceClasses {
                schoolClass.owner = targetUser
            }
        }
        
        // 合并订阅信息（保留更高级别的）
        if let sourceSubscription = sourceUser.subscription,
           let targetSubscription = targetUser.subscription {
            let sourceLevelRank = Subscription.Level(rawValue: sourceSubscription.level ?? "free")?.rank ?? 0
            let targetLevelRank = Subscription.Level(rawValue: targetSubscription.level ?? "free")?.rank ?? 0
            
            if sourceLevelRank > targetLevelRank {
                targetUser.subscription = sourceSubscription
                sourceSubscription.user = targetUser
            }
        } else if let sourceSubscription = sourceUser.subscription {
            targetUser.subscription = sourceSubscription
            sourceSubscription.user = targetUser
        }
        
        print("🔄 用户数据合并完成: \(sourceUser.name ?? "Unknown") -> \(targetUser.name ?? "Unknown")")
    }
    
    /**
     * 合并班级数据
     */
    private func mergeClassData(from sourceClass: SchoolClass, to targetClass: SchoolClass) {
        // 合并学生数据
        if let sourceStudents = sourceClass.students?.allObjects as? [Student] {
            for student in sourceStudents {
                student.schoolClass = targetClass
            }
        }
        
        // 合并规则数据
        if let sourceRules = sourceClass.rules?.allObjects as? [Rule] {
            for rule in sourceRules {
                rule.schoolClass = targetClass
            }
        }
        
        print("🔄 班级数据合并完成: \(sourceClass.name ?? "Unknown") -> \(targetClass.name ?? "Unknown")")
    }
    
}