//
//  ClassConfigFormData.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation

/**
 * 班级配置表单数据模型
 * 统一管理班级的加分规则、扣分规则和奖品配置
 * 支持基于用户订阅级别的动态限制
 */
struct ClassConfigFormData {
    
    // MARK: - 配置类型枚举
    enum ConfigType: String, CaseIterable {
        case addRules = "addRules"
        case deductRules = "deductRules"
        case prizes = "prizes"
        
        var displayName: String {
            switch self {
            case .addRules:
                return "add points rules".localized
            case .deductRules:
                return "deduct rules".localized
            case .prizes:
                return "prizes config".localized
            }
        }
        
        /**
         * 获取最大数量限制（基于用户订阅级别）
         * @param subscriptionLevel 用户订阅级别
         * @return 该配置类型允许的最大数量
         */
        func maxCount(for subscriptionLevel: Subscription.Level) -> Int {
            switch self {
            case .addRules, .deductRules:
                // 免费用户：5条，付费用户：10条
                return subscriptionLevel == .free ? 5 : 10
            case .prizes:
                // 奖品配置对所有用户都是10条
                return 10
            }
        }
    }
    
    // MARK: - 属性
    var configType: ConfigType = .addRules
    var addRuleForms: [RuleFormData] = []
    var deductRuleForms: [RuleFormData] = []
    var prizeForms: [PrizeFormData] = []
    
    /// 用户订阅级别，用于动态计算限制数量
    var userSubscriptionLevel: Subscription.Level = .free
    
    // MARK: - 初始化
    init(userSubscriptionLevel: Subscription.Level = .free) {
        self.userSubscriptionLevel = userSubscriptionLevel
        // 初始化时为每种类型创建一个空表单
        addRuleForms = [RuleFormData(type: "add")]
        deductRuleForms = [RuleFormData(type: "deduct")]
        prizeForms = [PrizeFormData()]
    }
    
    // MARK: - 计算属性
    
    /**
     * 获取当前配置类型对应的表单数据
     */
    var currentForms: [Any] {
        switch configType {
        case .addRules:
            return addRuleForms
        case .deductRules:
            return deductRuleForms
        case .prizes:
            return prizeForms
        }
    }
    
    /**
     * 获取当前配置类型的表单数量
     */
    var currentFormsCount: Int {
        switch configType {
        case .addRules:
            return addRuleForms.count
        case .deductRules:
            return deductRuleForms.count
        case .prizes:
            return prizeForms.count
        }
    }
    
    /**
     * 获取当前配置类型的最大数量限制
     */
    var maxFormsCount: Int {
        return configType.maxCount(for: userSubscriptionLevel)
    }
    
    /**
     * 检查当前配置类型是否可以添加更多表单
     */
    var canAddMoreForms: Bool {
        return currentFormsCount < maxFormsCount
    }
    
    /**
     * 检查当前配置类型是否可以删除表单
     */
    var canDeleteForms: Bool {
        return currentFormsCount > 1
    }
    
    /**
     * 获取所有有效的表单数据
     */
    var validAddRules: [RuleFormData] {
        return addRuleForms.filter { $0.isValid }
    }
    
    var validDeductRules: [RuleFormData] {
        return deductRuleForms.filter { $0.isValid }
    }
    
    var validPrizes: [PrizeFormData] {
        return prizeForms.filter { $0.isValid }
    }
    
    /**
     * 检查是否有任何有效的配置数据
     */
    var hasValidData: Bool {
        return !validAddRules.isEmpty || !validDeductRules.isEmpty || !validPrizes.isEmpty
    }
    
    // MARK: - 表单操作方法
    
    /**
     * 为当前配置类型添加新表单
     */
    mutating func addNewForm() {
        guard canAddMoreForms else { return }
        
        switch configType {
        case .addRules:
            addRuleForms.append(RuleFormData(type: "add"))
        case .deductRules:
            deductRuleForms.append(RuleFormData(type: "deduct"))
        case .prizes:
            prizeForms.append(PrizeFormData())
        }
    }
    
    /**
     * 删除指定索引的表单
     */
    mutating func removeForm(at index: Int) {
        guard canDeleteForms else { return }
        
        switch configType {
        case .addRules:
            if index < addRuleForms.count {
                addRuleForms.remove(at: index)
            }
        case .deductRules:
            if index < deductRuleForms.count {
                deductRuleForms.remove(at: index)
            }
        case .prizes:
            if index < prizeForms.count {
                prizeForms.remove(at: index)
            }
        }
    }
    
    /**
     * 从模板导入规则
     */
    mutating func importRules(_ templates: [RuleTemplate], type: String) {
        let newRules = templates.map { template in
            var ruleForm = RuleFormData(type: type)
            ruleForm.name = template.name ?? ""
            ruleForm.value = String(template.value)
            return ruleForm
        }
        
        if type == "add" {
            // 替换现有的加分规则，但不超过最大数量
            let maxCount = ConfigType.addRules.maxCount(for: userSubscriptionLevel)
            addRuleForms = Array(newRules.prefix(maxCount))
            // 如果导入的数量少于最大数量，补齐空表单
            while addRuleForms.count < 1 {
                addRuleForms.append(RuleFormData(type: "add"))
            }
        } else {
            // 替换现有的扣分规则，但不超过最大数量
            let maxCount = ConfigType.deductRules.maxCount(for: userSubscriptionLevel)
            deductRuleForms = Array(newRules.prefix(maxCount))
            // 如果导入的数量少于最大数量，补齐空表单
            while deductRuleForms.count < 1 {
                deductRuleForms.append(RuleFormData(type: "deduct"))
            }
        }
    }
    
    /**
     * 从模板导入奖品
     */
    mutating func importPrizes(_ templates: [PrizeTemplate]) {
        let newPrizes = templates.map { template in
            var prizeForm = PrizeFormData()
            prizeForm.name = template.name ?? ""
            prizeForm.cost = String(template.cost)
            prizeForm.type = template.type ?? "虚拟"
            return prizeForm
        }
        
        // 替换现有的奖品，但不超过最大数量
        let maxCount = ConfigType.prizes.maxCount(for: userSubscriptionLevel)
        prizeForms = Array(newPrizes.prefix(maxCount))
        // 如果导入的数量少于最大数量，补齐空表单
        while prizeForms.count < 1 {
            prizeForms.append(PrizeFormData())
        }
    }
    
    // MARK: - 验证方法
    
    /**
     * 验证当前配置类型的表单数据
     */
    func validateCurrentForms() -> ClassConfigValidationResult {
        switch configType {
        case .addRules:
            let result = addRuleForms.validateBatch()
            return ClassConfigValidationResult(
                isValid: result.isValid,
                errorMessages: result.errorMessages,
                configType: configType
            )
        case .deductRules:
            let result = deductRuleForms.validateBatch()
            return ClassConfigValidationResult(
                isValid: result.isValid,
                errorMessages: result.errorMessages,
                configType: configType
            )
        case .prizes:
            let result = prizeForms.validateBatch()
            return ClassConfigValidationResult(
                isValid: result.isValid,
                errorMessages: result.errorMessages,
                configType: configType
            )
        }
    }
    
    // MARK: - 订阅级别管理方法
    
    /**
     * 更新用户订阅级别并处理规则数量变化
     * @param newLevel 新的订阅级别
     */
    mutating func updateSubscriptionLevel(to newLevel: Subscription.Level) {
        let oldLevel = userSubscriptionLevel
        userSubscriptionLevel = newLevel
        
        // 如果从付费用户降级到免费用户，需要裁剪规则
        if oldLevel != .free && newLevel == .free {
            trimRulesForDowngrade()
        }
    }
    
    /**
     * 降级时裁剪超出限制的规则
     * 保留前5条有效规则，移除超出部分
     */
    private mutating func trimRulesForDowngrade() {
        let freeUserLimit = 5
        
        // 裁剪加分规则
        if addRuleForms.count > freeUserLimit {
            addRuleForms = Array(addRuleForms.prefix(freeUserLimit))
        }
        
        // 裁剪扣分规则
        if deductRuleForms.count > freeUserLimit {
            deductRuleForms = Array(deductRuleForms.prefix(freeUserLimit))
        }
        
        // 奖品配置不受影响，保持10条限制
    }
}

// MARK: - 班级配置验证结果
struct ClassConfigValidationResult {
    let isValid: Bool
    let errorMessages: [String]
    let configType: ClassConfigFormData.ConfigType
    
    var errorSummary: String {
        return errorMessages.joined(separator: "\n")
    }
} 