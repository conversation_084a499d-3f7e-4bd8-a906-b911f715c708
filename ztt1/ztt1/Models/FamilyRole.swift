//
//  FamilyRole.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import SwiftUI

/**
 * 家庭成员角色枚举
 * 定义所有支持的家庭成员角色及其属性
 */
enum FamilyRole: String, CaseIterable, Identifiable {
    case father = "father"
    case mother = "mother"
    case son = "son"
    case daughter = "daughter"
    case other = "other"
    
    var id: String { rawValue }
    
    // MARK: - 显示属性
    
    /**
     * 角色显示名称
     */
    var displayName: String {
        switch self {
        case .father:
            return "family.role.father".localized
        case .mother:
            return "family.role.mother".localized
        case .son:
            return "family.role.son".localized
        case .daughter:
            return "family.role.daughter".localized
        case .other:
            return "family.role.other".localized
        }
    }
    
    /**
     * 角色简称
     */
    var shortName: String {
        switch self {
        case .father:
            return "爸爸"
        case .mother:
            return "妈妈"
        case .son:
            return "儿子"
        case .daughter:
            return "女儿"
        case .other:
            return "成员"
        }
    }
    
    /**
     * 角色描述
     */
    var description: String {
        switch self {
        case .father:
            return "family.role.father.description".localized
        case .mother:
            return "family.role.mother.description".localized
        case .son:
            return "family.role.son.description".localized
        case .daughter:
            return "family.role.daughter.description".localized
        case .other:
            return "family.role.other.description".localized
        }
    }
    
    // MARK: - 头像系统
    
    /**
     * 默认头像名称
     */
    var defaultAvatarName: String {
        switch self {
        case .father:
            return "爸爸头像"
        case .mother:
            return "妈妈头像"
        case .son:
            return "男生头像"
        case .daughter:
            return "女生头像"
        case .other:
            return "其他头像"
        }
    }
    
    /**
     * 头像颜色主题
     */
    var avatarColor: Color {
        switch self {
        case .father:
            return Color(hex: "#4A90E2")  // 蓝色
        case .mother:
            return Color(hex: "#FF6B9D")  // 粉色
        case .son:
            return Color(hex: "#50C878")  // 绿色
        case .daughter:
            return Color(hex: "#FFB347")  // 橙色
        case .other:
            return Color(hex: "#9B59B6")  // 紫色
        }
    }
    
    /**
     * 角色图标
     */
    var iconName: String {
        switch self {
        case .father:
            return "person.fill"
        case .mother:
            return "person.fill"
        case .son:
            return "person.fill"
        case .daughter:
            return "person.fill"
        case .other:
            return "person.circle.fill"
        }
    }
    
    // MARK: - 角色属性
    
    /**
     * 默认性别
     */
    var defaultGender: Gender {
        switch self {
        case .father, .son:
            return .male
        case .mother, .daughter:
            return .female
        case .other:
            return .male  // 默认值，可以后续修改
        }
    }
    
    /**
     * 是否为父母角色
     */
    var isParent: Bool {
        return self == .father || self == .mother
    }
    
    /**
     * 是否为孩子角色
     */
    var isChild: Bool {
        return self == .son || self == .daughter
    }
    
    /**
     * 是否为成年人角色
     */
    var isAdult: Bool {
        return isParent || self == .other
    }
    
    /**
     * 角色优先级（用于排序）
     */
    var priority: Int {
        switch self {
        case .father:
            return 1
        case .mother:
            return 2
        case .son:
            return 3
        case .daughter:
            return 4
        case .other:
            return 5
        }
    }
    
    // MARK: - 权限系统
    
    /**
     * 角色权限级别
     */
    var permissionLevel: FamilyPermissionLevel {
        switch self {
        case .father, .mother:
            return .admin
        case .son, .daughter:
            return .member
        case .other:
            return .guest
        }
    }
    
    /**
     * 可以编辑的内容类型
     */
    var editableContentTypes: Set<FamilyContentType> {
        switch permissionLevel {
        case .admin:
            return Set(FamilyContentType.allCases)
        case .member:
            return [.ownDiary, .ownProfile]
        case .guest:
            return [.ownProfile]
        }
    }
    
    /**
     * 可以查看的内容类型
     */
    var viewableContentTypes: Set<FamilyContentType> {
        switch permissionLevel {
        case .admin:
            return Set(FamilyContentType.allCases)
        case .member:
            return [.familyMembers, .pointRecords, .diaryEntries, .aiReports, .ownProfile, .ownDiary]
        case .guest:
            return [.familyMembers, .pointRecords, .ownProfile]
        }
    }
    
    // MARK: - 静态方法
    
    /**
     * 获取父母角色
     */
    static var parentRoles: [FamilyRole] {
        return [.father, .mother]
    }
    
    /**
     * 获取孩子角色
     */
    static var childRoles: [FamilyRole] {
        return [.son, .daughter]
    }
    
    /**
     * 根据性别获取推荐角色
     */
    static func recommendedRoles(for gender: Gender) -> [FamilyRole] {
        switch gender {
        case .male:
            return [.father, .son, .other]
        case .female:
            return [.mother, .daughter, .other]
        }
    }
    
    /**
     * 检查角色是否唯一（一个家庭中只能有一个）
     */
    var isUnique: Bool {
        return isParent  // 父母角色在家庭中是唯一的
    }
}

/**
 * 性别枚举
 */
enum Gender: String, CaseIterable, Identifiable {
    case male = "male"
    case female = "female"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .male:
            return "gender.male".localized
        case .female:
            return "gender.female".localized
        }
    }
    
    var iconName: String {
        switch self {
        case .male:
            return "person.fill"
        case .female:
            return "person.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .male:
            return Color(hex: "#4A90E2")
        case .female:
            return Color(hex: "#FF6B9D")
        }
    }
}

/**
 * 家庭权限级别
 */
enum FamilyPermissionLevel: String, CaseIterable {
    case admin = "admin"      // 管理员（父母）
    case member = "member"    // 成员（孩子）
    case guest = "guest"      // 访客（其他成员）
    
    var displayName: String {
        switch self {
        case .admin:
            return "family.permission.admin".localized
        case .member:
            return "family.permission.member".localized
        case .guest:
            return "family.permission.guest".localized
        }
    }
    
    var description: String {
        switch self {
        case .admin:
            return "family.permission.admin.description".localized
        case .member:
            return "family.permission.member.description".localized
        case .guest:
            return "family.permission.guest.description".localized
        }
    }
}

/**
 * 家庭内容类型
 */
enum FamilyContentType: String, CaseIterable {
    case familyMembers = "familyMembers"      // 家庭成员管理
    case pointRecords = "pointRecords"        // 积分记录
    case diaryEntries = "diaryEntries"        // 日记条目
    case aiReports = "aiReports"              // AI报告
    case familyRules = "familyRules"          // 家庭规则
    case familyPrizes = "familyPrizes"        // 家庭奖品
    case ownProfile = "ownProfile"            // 自己的资料
    case ownDiary = "ownDiary"                // 自己的日记
    
    var displayName: String {
        switch self {
        case .familyMembers:
            return "family.content.members".localized
        case .pointRecords:
            return "family.content.points".localized
        case .diaryEntries:
            return "family.content.diary".localized
        case .aiReports:
            return "family.content.ai_reports".localized
        case .familyRules:
            return "family.content.rules".localized
        case .familyPrizes:
            return "family.content.prizes".localized
        case .ownProfile:
            return "family.content.own_profile".localized
        case .ownDiary:
            return "family.content.own_diary".localized
        }
    }
}
