//
//  LotteryToolFormData.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/16.
//

import Foundation
import SwiftUI

/**
 * 抽奖道具表单数据模型
 * 用于管理抽奖道具配置的表单数据和验证逻辑
 */
class LotteryToolFormData: ObservableObject {
    
    // MARK: - 基本配置
    
    /// 道具类型
    @Published var toolType: LotteryToolConfig.ToolType = .wheel
    
    /// 道具数量（盲盒和刮刮卡可调整，大转盘固定12）
    @Published var itemCount: Int = 12
    
    /// 每次抽奖消耗积分
    @Published var costPerPlay: Int = 0
    
    // MARK: - 道具项目配置
    
    /// 道具项目数据
    @Published var items: [LotteryToolItemData] = []
    
    // MARK: - 验证状态
    
    /// 表单验证错误
    @Published var validationErrors: [String] = []
    
    /// 是否正在保存
    @Published var isSaving: Bool = false
    
    // MARK: - 计算属性
    
    /**
     * 检查表单是否有效
     */
    var isValid: Bool {
        validate()
        return validationErrors.isEmpty
    }
    
    /**
     * 获取最小道具数量
     */
    var minItemCount: Int {
        return toolType.minItemCount
    }
    
    /**
     * 获取最大道具数量
     */
    var maxItemCount: Int {
        return toolType.maxItemCount
    }
    
    /**
     * 检查道具数量是否可调整
     */
    var isItemCountAdjustable: Bool {
        return true  // 所有道具类型都支持数量调整
    }
    
    /**
     * 获取道具项目标题前缀
     */
    var itemTitlePrefix: String {
        switch toolType {
        case .wheel:
            return "分区"
        case .box:
            return "盲盒"
        case .scratch:
            return "刮刮卡"
        }
    }
    
    /**
     * 获取本地化的道具项目标题前缀
     */
    var localizedItemTitlePrefix: String {
        switch toolType {
        case .wheel:
            return "lottery_tool_config.tool_type.wheel".localized
        case .box:
            return "lottery_tool_config.tool_type.box".localized
        case .scratch:
            return "lottery_tool_config.tool_type.scratch".localized
        }
    }
    
    // MARK: - 初始化
    
    /**
     * 默认初始化
     */
    init() {
        setupDefaultItems()
    }
    
    /**
     * 使用现有配置初始化
     */
    init(from config: LotteryToolConfig) {
        self.toolType = config.lotteryToolType
        self.itemCount = Int(config.itemCount)
        self.costPerPlay = Int(config.costPerPlay)
        
        // 加载现有项目数据
        loadItemsFromConfig(config)
    }
    
    // MARK: - 数据管理
    
    /**
     * 设置道具类型
     */
    func setToolType(_ type: LotteryToolConfig.ToolType) {
        self.toolType = type
        
        // 重置道具数量为默认值
        self.itemCount = type.defaultItemCount
        
        // 重新设置道具项目
        setupDefaultItems()
    }
    
    /**
     * 从现有配置加载数据
     */
    func loadFromExistingConfig(_ config: LotteryToolConfig) {
        self.toolType = config.lotteryToolType
        self.itemCount = Int(config.itemCount)
        self.costPerPlay = Int(config.costPerPlay)
        
        // 加载现有项目数据
        loadItemsFromConfig(config)
    }
    
    /**
     * 更新道具数量
     */
    func updateItemCount(_ count: Int) {
        let clampedCount = max(minItemCount, min(maxItemCount, count))
        self.itemCount = clampedCount
        
        // 调整道具项目数组
        adjustItemsArray()
    }
    
    /**
     * 设置默认道具项目
     */
    private func setupDefaultItems() {
        items = (1...itemCount).map { index in
            LotteryToolItemData(
                index: index,
                prizeName: "",
                toolType: toolType
            )
        }
    }
    
    /**
     * 从配置加载道具项目
     */
    private func loadItemsFromConfig(_ config: LotteryToolConfig) {
        let sortedItems = config.sortedItems
        
        // 创建项目数据数组
        items = (1...itemCount).map { index in
            let existingItem = sortedItems.first { $0.itemIndex == index }
            return LotteryToolItemData(
                index: index,
                prizeName: existingItem?.formattedPrizeName ?? "",
                toolType: toolType
            )
        }
    }
    
    /**
     * 调整道具项目数组大小
     */
    private func adjustItemsArray() {
        if items.count < itemCount {
            // 添加新项目
            let newItems = (items.count + 1...itemCount).map { index in
                LotteryToolItemData(
                    index: index,
                    prizeName: "",
                    toolType: toolType
                )
            }
            items.append(contentsOf: newItems)
        } else if items.count > itemCount {
            // 移除多余项目
            items = Array(items.prefix(itemCount))
        }
        
        // 重新编号
        for (index, item) in items.enumerated() {
            item.index = index + 1
        }
    }
    
    /**
     * 更新道具项目奖品名称
     */
    func updateItemPrizeName(at index: Int, name: String) {
        guard index >= 0 && index < items.count else { return }
        items[index].prizeName = name
    }
    
    /**
     * 批量设置奖品名称
     */
    func batchSetPrizeNames(_ names: [String]) {
        for (index, name) in names.enumerated() {
            if index < items.count {
                items[index].prizeName = name
            }
        }
    }
    
    // MARK: - 验证逻辑
    
    /**
     * 验证表单数据
     */
    @discardableResult
    func validate() -> Bool {
        validationErrors.removeAll()
        
        // 验证基本配置
        validateBasicConfig()
        
        // 验证道具项目
        validateItems()
        
        return validationErrors.isEmpty
    }
    
    /**
     * 验证基本配置
     */
    private func validateBasicConfig() {
        // 验证道具数量
        if itemCount < minItemCount || itemCount > maxItemCount {
            validationErrors.append("道具数量必须在 \(minItemCount) 到 \(maxItemCount) 之间")
        }
        
        // 验证积分消耗
        if costPerPlay < 1 {
            validationErrors.append("每次抽奖消耗积分必须大于0")
        }
        
        if costPerPlay > 1000 {
            validationErrors.append("每次抽奖消耗积分不能超过1000")
        }
    }
    
    /**
     * 验证道具项目
     */
    private func validateItems() {
        var emptyCount = 0
        var duplicateNames: [String] = []
        var nameSet: Set<String> = []
        
        for (index, item) in items.enumerated() {
            // 检查空奖品名称
            if item.prizeName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                emptyCount += 1
                continue
            }
            
            // 检查奖品名称长度
            if item.prizeName.count > 20 {
                validationErrors.append(String(format: "lottery_tool_config.validation.prize_name_too_long".localized, "\(index + 1)", localizedItemTitlePrefix))
            }
            
            // 检查重复奖品名称
            let trimmedName = item.prizeName.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            if nameSet.contains(trimmedName) {
                if !duplicateNames.contains(trimmedName) {
                    duplicateNames.append(trimmedName)
                }
            } else {
                nameSet.insert(trimmedName)
            }
        }
        
        // 添加验证错误
        if emptyCount > 0 {
            validationErrors.append(String(format: "lottery_tool_config.validation.empty_prize_names".localized, "\(emptyCount)", localizedItemTitlePrefix))
        }
        
        if !duplicateNames.isEmpty {
            validationErrors.append("存在重复的奖品名称，请检查并修改")
        }
    }
    
    // MARK: - 数据导出
    
    /**
     * 导出为配置数据
     */
    func toConfigData() -> (toolType: String, itemCount: Int, costPerPlay: Int, prizeNames: [String]) {
        let prizeNames = items.map { $0.prizeName.trimmingCharacters(in: .whitespacesAndNewlines) }
        return (
            toolType: toolType.rawValue,
            itemCount: itemCount,
            costPerPlay: costPerPlay,
            prizeNames: prizeNames
        )
    }
    
    /**
     * 重置表单
     */
    func reset() {
        toolType = .wheel
        itemCount = 12
        costPerPlay = 0
        validationErrors.removeAll()
        isSaving = false
        setupDefaultItems()
    }
}

/**
 * 抽奖道具项目数据
 */
class LotteryToolItemData: ObservableObject, Identifiable {
    let id = UUID()
    
    /// 项目索引
    @Published var index: Int
    
    /// 奖品名称
    @Published var prizeName: String
    
    /// 道具类型
    let toolType: LotteryToolConfig.ToolType
    
    /// 显示标题
    var displayTitle: String {
        switch toolType {
        case .wheel:
            return String(format: "lottery_tool_config.item.display_title_wheel".localized, "\(index)")
        case .box:
            return String(format: "lottery_tool_config.item.display_title_box".localized, "\(index)")
        case .scratch:
            return String(format: "lottery_tool_config.item.display_title_scratch".localized, "\(index)")
        }
    }
    
    /// 占位符文本
    var placeholderText: String {
        switch toolType {
        case .wheel:
            return String(format: "lottery_tool_config.item.placeholder_wheel".localized, "\(index)")
        case .box:
            return String(format: "lottery_tool_config.item.placeholder_box".localized, "\(index)")
        case .scratch:
            return String(format: "lottery_tool_config.item.placeholder_scratch".localized, "\(index)")
        }
    }
    
    init(index: Int, prizeName: String, toolType: LotteryToolConfig.ToolType) {
        self.index = index
        self.prizeName = prizeName
        self.toolType = toolType
    }
} 