//
//  PermissionManager.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/20.
//

import Foundation
import Combine

/**
 * 权限管理器
 * 统一处理所有订阅功能的权限检查和控制
 * 基于RevenueCat的订阅状态提供权限验证
 */
@MainActor
class PermissionManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = PermissionManager()
    
    // MARK: - Published Properties
    @Published var currentLevel: RevenueCatManager.SubscriptionLevel = .free
    @Published var maxClassesAllowed: Int = 1
    @Published var canUseLottery: Bool = false
    @Published var canUseAdvancedGames: Bool = false
    @Published var canUseAIAnalysis: Bool = false
    @Published var canUseMultiDeviceSync: Bool = false
    
    // MARK: - Private Properties
    private let revenueCatManager = RevenueCatManager.shared
    private let trialManager = TrialManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        setupObservers()
        updatePermissions()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查特定功能权限
     */
    func hasPermission(for feature: SubscriptionFeature) -> Bool {
        switch feature {
        case .basicScoring, .prizeExchange:
            return true // 所有用户都可用
        case .lottery:
            return canUseLottery
        case .advancedGames:
            return canUseAdvancedGames
        case .aiAnalysis:
            return canUseAIAnalysis
        case .multiDeviceSync:
            return canUseMultiDeviceSync
        case .createMultipleClasses:
            return maxClassesAllowed > 1
        }
    }
    
    /**
     * 检查用户是否可以创建更多班级
     */
    func canCreateMoreClasses(currentClassCount: Int) -> Bool {
        return currentClassCount < maxClassesAllowed
    }
    
    /**
     * 获取剩余可创建班级数
     */
    func getRemainingClassCount(currentClassCount: Int) -> Int {
        return max(0, maxClassesAllowed - currentClassCount)
    }
    
    /**
     * 检查是否为付费用户
     */
    func isPaidUser() -> Bool {
        return currentLevel != .free
    }
    
    /**
     * 检查是否为高级用户
     */
    func isPremiumUser() -> Bool {
        return currentLevel == .premium
    }
    
    /**
     * 获取功能权限描述
     */
    func getFeaturePermissionDescription(for feature: SubscriptionFeature) -> String {
        if hasPermission(for: feature) {
            return "permission.feature.available".localized
        } else {
            return getUpgradeRequirementDescription(for: feature)
        }
    }
    
    /**
     * 获取升级要求描述
     */
    func getUpgradeRequirementDescription(for feature: SubscriptionFeature) -> String {
        switch feature {
        case .basicScoring, .prizeExchange:
            return "permission.feature.free".localized
        case .lottery, .multiDeviceSync, .createMultipleClasses:
            return "permission.feature.requires_basic".localized
        case .advancedGames, .aiAnalysis:
            return "permission.feature.requires_premium".localized
        }
    }
    
    /**
     * 检查AI分析功能的完整权限
     * 需要高级会员 + 至少10条积分记录
     */
    func canUseAIAnalysisForStudent(_ student: Student) -> (canUse: Bool, reason: String) {
        // 检查订阅权限
        guard canUseAIAnalysis else {
            return (false, "permission.ai_analysis.requires_premium".localized)
        }
        
        // 检查记录数量
        let validRecords = student.sortedPointRecords.filter { !$0.isReversed }
        guard validRecords.count >= 10 else {
            return (false, "permission.ai_analysis.insufficient_records".localized.replacingOccurrences(of: "{count}", with: "\(validRecords.count)"))
        }
        
        return (true, "permission.feature.available".localized)
    }
    
    /**
     * 检查抽奖功能权限
     */
    func canUseLotteryTool(_ toolType: LotteryToolConfig.ToolType) -> (canUse: Bool, reason: String) {
        switch toolType {
        case .wheel:
            // 大转盘需要基础会员及以上
            if canUseLottery {
                return (true, "permission.feature.available".localized)
            } else {
                return (false, "permission.lottery.wheel_requires_basic".localized)
            }
        case .box, .scratch:
            // 盲盒和刮刮卡需要高级会员
            if canUseAdvancedGames {
                return (true, "permission.feature.available".localized)
            } else {
                return (false, "permission.lottery.advanced_requires_premium".localized)
            }
        }
    }
    
    /**
     * 获取订阅级别显示信息
     */
    func getSubscriptionDisplayInfo() -> SubscriptionDisplayInfo {
        return SubscriptionDisplayInfo(
            level: currentLevel,
            displayName: currentLevel.displayName,
            maxClasses: maxClassesAllowed,
            features: getAvailableFeatures(),
            upgradeHint: getUpgradeHint()
        )
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置观察者
     */
    private func setupObservers() {
        revenueCatManager.$currentSubscriptionLevel
            .sink { [weak self] level in
                self?.currentLevel = level
                self?.updatePermissions()
            }
            .store(in: &cancellables)

        // 监听试用状态变化
        trialManager.$isTrialActive
            .sink { [weak self] _ in
                self?.updatePermissions()
            }
            .store(in: &cancellables)
    }
    
    /**
     * 更新权限状态
     */
    private func updatePermissions() {
        maxClassesAllowed = currentLevel.maxClasses
        canUseLottery = currentLevel != .free
        canUseAdvancedGames = currentLevel == .premium
        canUseAIAnalysis = currentLevel == .premium
        canUseMultiDeviceSync = true  // 所有用户都支持多设备同步
        
        print("🔐 权限已更新: \(currentLevel.rawValue), 最大班级数: \(maxClassesAllowed)")
    }
    
    /**
     * 获取可用功能列表
     */
    private func getAvailableFeatures() -> [SubscriptionFeature] {
        var features: [SubscriptionFeature] = [.basicScoring, .prizeExchange]
        
        if canUseLottery {
            features.append(.lottery)
        }
        if canUseAdvancedGames {
            features.append(.advancedGames)
        }
        if canUseAIAnalysis {
            features.append(.aiAnalysis)
        }
        if canUseMultiDeviceSync {
            features.append(.multiDeviceSync)
        }
        if maxClassesAllowed > 1 {
            features.append(.createMultipleClasses)
        }
        
        return features
    }
    
    /**
     * 获取升级提示
     */
    private func getUpgradeHint() -> String? {
        switch currentLevel {
        case .free:
            return "permission.upgrade_hint.free_to_basic".localized
        case .basic:
            return "permission.upgrade_hint.basic_to_premium".localized
        case .premium:
            return nil // 已是最高级别
        }
    }
}

// MARK: - Supporting Types

/**
 * 订阅功能枚举
 */
enum SubscriptionFeature: String, CaseIterable {
    case basicScoring = "basic_scoring"
    case prizeExchange = "prize_exchange"
    case lottery = "lottery"
    case advancedGames = "advanced_games"
    case aiAnalysis = "ai_analysis"
    case multiDeviceSync = "multi_device_sync"
    case createMultipleClasses = "create_multiple_classes"
    
    var displayName: String {
        switch self {
        case .basicScoring:
            return "permission.feature.basic_scoring".localized
        case .prizeExchange:
            return "permission.feature.prize_exchange".localized
        case .lottery:
            return "permission.feature.lottery".localized
        case .advancedGames:
            return "permission.feature.advanced_games".localized
        case .aiAnalysis:
            return "permission.feature.ai_analysis".localized
        case .multiDeviceSync:
            return "permission.feature.multi_device_sync".localized
        case .createMultipleClasses:
            return "permission.feature.create_multiple_classes".localized
        }
    }
    
    var iconName: String {
        switch self {
        case .basicScoring:
            return "plus.circle"
        case .prizeExchange:
            return "gift"
        case .lottery:
            return "dice"
        case .advancedGames:
            return "gamecontroller"
        case .aiAnalysis:
            return "brain.head.profile"
        case .multiDeviceSync:
            return "icloud"
        case .createMultipleClasses:
            return "folder.badge.plus"
        }
    }
}

/**
 * 订阅级别显示信息
 */
struct SubscriptionDisplayInfo {
    let level: RevenueCatManager.SubscriptionLevel
    let displayName: String
    let maxClasses: Int
    let features: [SubscriptionFeature]
    let upgradeHint: String?
}

// MARK: - Convenience Extensions

extension PermissionManager {
    
    /**
     * 检查权限并返回结果或错误提示
     */
    func checkPermissionWithAlert(for feature: SubscriptionFeature) -> PermissionCheckResult {
        if hasPermission(for: feature) {
            return .allowed
        } else {
            let requiredLevel = getRequiredLevelFor(feature: feature)
            let message = getUpgradeRequirementDescription(for: feature)
            return .denied(requiredLevel: requiredLevel, message: message)
        }
    }
    
    /**
     * 获取功能所需的最低订阅级别
     */
    private func getRequiredLevelFor(feature: SubscriptionFeature) -> RevenueCatManager.SubscriptionLevel {
        switch feature {
        case .basicScoring, .prizeExchange, .multiDeviceSync:
            return .free  // 多设备同步现在对所有用户免费
        case .lottery, .createMultipleClasses:
            return .basic
        case .advancedGames, .aiAnalysis:
            return .premium
        }
    }
}

/**
 * 权限检查结果
 */
enum PermissionCheckResult {
    case allowed
    case denied(requiredLevel: RevenueCatManager.SubscriptionLevel, message: String)
    
    var isAllowed: Bool {
        switch self {
        case .allowed:
            return true
        case .denied:
            return false
        }
    }
    
    var denialMessage: String? {
        switch self {
        case .allowed:
            return nil
        case .denied(_, let message):
            return message
        }
    }
}