//
//  HomeViewModel.swift
//  ztt1
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI
import Foundation
import Combine
import CoreData

/**
 * 家庭成员排序类型枚举
 */
enum FamilyMemberSortType {
    case byRole     // 按角色排序
    case byPoints   // 按积分排序
}

/**
 * 学生排序类型枚举（兼容性保留）
 */
enum StudentSortType {
    case byStudentNumber // 按学号排序
    case byScore        // 按积分排序
}

/**
 * 首页视图模型 - 管理首页的所有状态和数据
 * 使用CoreData+CloudKit进行数据管理
 * 已从班级-学生模式改为家庭-成员模式
 */
class HomeViewModel: ObservableObject {

    // MARK: - Published Properties

    // MARK: - 家庭相关属性
    @Published var families: [Family] = []
    @Published var selectedFamilyIndex: Int = 0

    // MARK: - 兼容性属性（保持向后兼容）
    @Published var classes: [SchoolClass] = []
    @Published var selectedClassIndex: Int = 0

    @Published var selectedTabIndex: Int = 0
    @Published var isInitialLoad: Bool = true
    @Published var isFamilySwitching: Bool = false
    @Published var isClassSwitching: Bool = false // 兼容性保留
    @Published var isRefreshing: Bool = false

    // MARK: - 排序相关属性
    @Published var memberSortType: FamilyMemberSortType = .byRole
    @Published var sortType: StudentSortType = .byStudentNumber // 兼容性保留
    @Published var showSortOptions: Bool = false

    // MARK: - Delete Mode Properties
    @Published var isDeleteMode: Bool = false
    @Published var showDeleteConfirmation: Bool = false
    @Published var memberToDelete: FamilyMember? = nil
    @Published var studentToDelete: Student? = nil // 兼容性保留
    
    // MARK: - Date Range Properties
    @Published var selectedDateRange: DateRangeType = .thisMonth
    @Published var showDateRangePicker: Bool = false
    @Published var currentRangeTotalScore: Int = 0
    
    // MARK: - Add Member Properties
    @Published var showManualAddMember: Bool = false
    @Published var showNoFamilyAlert: Bool = false
    @Published var addMemberError: String? = nil
    @Published var isProcessingMembers: Bool = false

    // MARK: - 兼容性：Add Student Properties
    @Published var showManualAddStudent: Bool = false
    @Published var showNoClassAlert: Bool = false
    @Published var addStudentError: String? = nil
    @Published var isProcessingStudents: Bool = false

    // MARK: - Create Family Properties
    @Published var showCreateFamilyDialog: Bool = false
    @Published var familyName: String = ""
    @Published var isCreatingFamily: Bool = false
    @Published var createFamilyErrorMessage: String? = nil
    @Published var showCreateFamilySuccessAlert: Bool = false

    // MARK: - 兼容性：Create Class Properties
    @Published var showCreateClassDialog: Bool = false
    @Published var showPermissionDeniedAlert: Bool = false
    @Published var showSubscriptionView: Bool = false
    @Published var className: String = ""
    @Published var isCreatingClass: Bool = false
    @Published var createClassErrorMessage: String? = nil
    @Published var showCreateClassSuccessAlert: Bool = false
    
    // MARK: - Class Operation Properties
    @Published var showClassOperationOptions: Bool = false
    @Published var showClassOperationForm: Bool = false
    @Published var classOperationType: ClassOperationType = .add
    @Published var isSubmittingClassOperation: Bool = false
    @Published var classOperationError: String? = nil

    // MARK: - Family Operation Properties
    @Published var familyOperationType: FamilyOperationType = .add
    
    // MARK: - 班级冻结管理相关属性
    @Published var showClassFreezeSelection: Bool = false
    @Published var showClassUnfreezeSelection: Bool = false
    @Published var frozenClasses: [SchoolClass] = []
    @Published var activeClasses: [SchoolClass] = []
    @Published var allowedActiveClassCount: Int = 1
    @Published var availableUnfreezeCount: Int = 0
    @Published var previousSubscriptionLevelName: String = ""
    @Published var currentSubscriptionLevelName: String = ""
    
    // MARK: - CoreData Manager
    private let coreDataManager = CoreDataManager.shared
    private let classManagementService = ClassManagementService.shared
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var currentUser: User?
    
    // MARK: - Computed Properties

    /**
     * 当前选中的家庭
     */
    var selectedFamily: Family? {
        guard selectedFamilyIndex >= 0 && selectedFamilyIndex < families.count else { return nil }
        return families[selectedFamilyIndex]
    }

    /**
     * 当前家庭的成员列表（已排序）
     */
    var filteredMembers: [FamilyMember] {
        guard let currentFamily = selectedFamily else { return [] }

        // 获取所有成员并根据排序类型排序
        let allMembers = currentFamily.sortedMembers

        switch memberSortType {
        case .byRole:
            return allMembers // sortedMembers已经按角色排序
        case .byPoints:
            return allMembers.sorted {
                $0.currentPoints > $1.currentPoints
            }
        }
    }

    /**
     * 当前家庭的总积分
     */
    var currentFamilyTotalScore: Int {
        return Int(selectedFamily?.calculatedTotalPoints ?? 0)
    }

    // MARK: - 兼容性计算属性

    /**
     * 兼容性：当前选中的班级
     */
    var selectedClass: SchoolClass? {
        return nil // 家庭模式下返回nil
    }

    /**
     * 兼容性：当前班级的学生列表
     */
    var filteredStudents: [Student] {
        return [] // 家庭模式下返回空数组
    }

    /**
     * 兼容性：当前班级的总积分
     */
    var currentClassTotalScore: Int {
        return currentFamilyTotalScore // 映射到家庭总积分
    }
    
    /**
     * 用户订阅级别显示名称
     */
    private var userLevelDisplayName: String {
        guard let user = currentUser else { return "create_class.permission_denied.free_user".localized }
        
        switch user.subscriptionLevel {
        case "free":
            return "create_class.permission_denied.free_user".localized
        case "basic":
            return "create_class.permission_denied.basic_user".localized
        case "premium":
            return "create_class.permission_denied.premium_user".localized
        default:
            return "create_class.permission_denied.free_user".localized
        }
    }
    
    // MARK: - Initialization

    init() {
        loadUserAndFamilies()
        setupDateRangeObserver()
        setupNotificationObservers()
    }

    // MARK: - Data Loading

    /**
     * 加载用户和家庭数据
     */
    private func loadUserAndFamilies() {
        // 获取或创建默认用户
        currentUser = coreDataManager.getOrCreateDefaultUser()

        // 加载用户的日期范围偏好
        loadUserDateRangePreference()

        // 加载家庭数据
        loadFamilies()

        // 等待数据初始化完成
        if coreDataManager.isInitialized {
            isInitialLoad = false
        } else {
            // 监听数据初始化完成
            coreDataManager.$isInitialized
                .receive(on: DispatchQueue.main)
                .sink { [weak self] isInitialized in
                    if isInitialized {
                        self?.loadFamilies()
                        self?.loadUserDateRangePreference()
                        self?.isInitialLoad = false
                    }
                }
                .store(in: &cancellables)
        }
    }

    /**
     * 加载家庭数据
     */
    private func loadFamilies() {
        guard let user = currentUser else { return }

        // 获取用户的所有家庭
        families = coreDataManager.getFamilies(for: user)

        // 确保选中索引有效
        if !families.isEmpty && selectedFamilyIndex >= families.count {
            selectedFamilyIndex = 0
        } else if families.isEmpty {
            selectedFamilyIndex = -1
        }

        print("加载家庭数据完成，共 \(families.count) 个家庭，选中索引: \(selectedFamilyIndex)")
    }

    /**
     * 兼容性：加载班级数据
     */
    private func loadClasses() {
        // 为了保持兼容性，保留此方法但不执行任何操作
        classes = []
        selectedClassIndex = -1
        print("兼容性：loadClasses方法被调用，但在家庭模式下不执行任何操作")
    }
    
    /**
     * 获取班级列表
     * 只显示活跃状态的班级
     */
    private func fetchClasses() {
        guard let user = coreDataManager.getCurrentUser() else {
            print("⚠️ 未获取到当前用户")
            return
        }
        
        currentUser = user
        
        // 只获取活跃状态的班级
        classes = classManagementService.getActiveClasses(for: user)
        
        // 如果选中的班级索引超出范围，重置为0
        if selectedClassIndex >= classes.count {
            selectedClassIndex = classes.isEmpty ? -1 : 0
        }
        
        // 如果没有班级，显示创建班级提示
        if classes.isEmpty && !isCreatingClass {
            showNoClassAlert = true
        }
        
        print("📊 获取到\(classes.count)个活跃班级")
    }
    
    // MARK: - Public Methods
    
    /**
     * 切换选中的班级
     */
    func selectClass(at index: Int) {
        guard index < classes.count else { return }
        
        // 设置班级切换状态
        isClassSwitching = true
        
        // 首次切换后禁用初始加载动画
        if isInitialLoad {
            isInitialLoad = false
        }
        
        selectedClassIndex = index

        // 更新当前班级的时间范围内总积分
        refreshRangeTotalScore()
        
        // 延迟恢复班级切换状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isClassSwitching = false
        }
        
        print("班级切换: 选中索引 \(index), 班级名称: \(classes[index].name ?? "未知")")
    }
    

    
    /**
     * 添加学生到当前班级
     */
    func addStudent(name: String, studentNumber: String, gender: String) {
        guard let currentClass = selectedClass else { return }
        
        let newStudent = coreDataManager.createStudent(
            name: name,
            number: studentNumber,
            gender: gender,
            in: currentClass
        )
        
        // 刷新班级数据
        refreshCurrentClass()
        
        print("添加学生: \(newStudent.name ?? "未知")")
    }
    
    // MARK: - Add Student Methods
    
    /**
     * 处理添加成员按钮点击
     */
    func handleAddMember() {
        // 清除之前的错误信息
        addMemberError = nil

        // 检查是否已创建家庭
        if families.isEmpty {
            showNoFamilyAlert = true
            print("未创建家庭，显示提示弹窗")
        } else {
            showManualAddMember = true
            print("直接显示手动添加成员表单")
        }
    }

    /**
     * 处理添加学生按钮点击（保持向后兼容）
     */
    func handleAddStudent() {
        // 兼容性：清除之前的错误信息
        addStudentError = nil

        // 检查是否已创建班级（兼容性）
        if families.isEmpty {
            showNoClassAlert = true
            print("兼容性：未创建家庭，显示班级提示弹窗")
        } else {
            showManualAddStudent = true
            print("兼容性：显示手动添加学生表单")
        }
    }
    

    
    /**
     * 批量添加成员
     */
    func addMembers(_ memberForms: [MemberFormData]) {
        guard let currentFamily = selectedFamily else {
            addMemberError = "add_member.error.no_family".localized
            return
        }

        // 设置处理状态
        isProcessingMembers = true
        addMemberError = nil

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            var successCount = 0
            let errorCount = 0 // 当前实现中没有错误处理，设为常量

            for memberForm in memberForms {
                // 创建家庭成员
                print("👨‍👩‍👧‍👦 创建成员: \(memberForm.formattedName), 角色: \(memberForm.role), 初始积分: \(memberForm.initialPointsValue)")

                let member = self.coreDataManager.createFamilyMember(
                    name: memberForm.formattedName,
                    role: memberForm.role,
                    gender: memberForm.gender,
                    age: Int16(memberForm.age),
                    initialPoints: memberForm.initialPointsValue,
                    in: currentFamily
                )
                print("✅ 成员创建完成: \(member.name ?? "Unknown"), 当前积分: \(member.currentPoints)")
                successCount += 1
            }

            DispatchQueue.main.async {
                self.isProcessingMembers = false

                if errorCount > 0 {
                    self.addMemberError = "add_member.error.partial_failure".localized(with: "\(successCount)", "\(errorCount)")
                }

                if successCount > 0 {
                    // 刷新家庭数据
                    self.refreshCurrentFamily()

                    // 如果全部成功，关闭表单
                    if errorCount == 0 {
                        self.showManualAddMember = false
                    }
                }

                print("批量添加成员完成: 成功 \(successCount) 个，失败 \(errorCount) 个")
            }
        }
    }

    /**
     * 批量添加学生（保持向后兼容）
     */
    func addStudents(_ studentForms: [StudentFormData]) {
        guard let currentClass = selectedClass else {
            addStudentError = "add_student.error.no_class".localized
            return
        }

        // 设置处理状态
        isProcessingStudents = true
        addStudentError = nil

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            var successCount = 0
            var errorCount = 0
            var duplicateNumbers: [String] = []

            for studentForm in studentForms {
                // 检查学号是否已存在
                if self.coreDataManager.isStudentNumberExists(studentForm.formattedStudentNumber, in: currentClass) {
                    duplicateNumbers.append(studentForm.formattedStudentNumber)
                    errorCount += 1
                    continue
                }

                // 创建学生
                print("🎓 创建学生: \(studentForm.formattedName), 学号: \(studentForm.formattedStudentNumber), 初始积分: \(studentForm.initialPointsValue)")
                let student = self.coreDataManager.createStudent(
                    name: studentForm.formattedName,
                    number: studentForm.formattedStudentNumber,
                    gender: studentForm.gender,
                    initialPoints: studentForm.initialPointsValue,
                    in: currentClass
                )
                print("✅ 学生创建完成: \(student.name ?? "Unknown"), 当前积分: \(student.point)")
                successCount += 1
            }

            DispatchQueue.main.async {
                self.isProcessingStudents = false

                if errorCount > 0 {
                    if !duplicateNumbers.isEmpty {
                        self.addStudentError = "add_student.error.duplicate_numbers".localized(with: duplicateNumbers.joined(separator: ", "))
                    } else {
                        self.addStudentError = "add_student.error.partial_failure".localized(with: "\(successCount)", "\(errorCount)")
                    }
                }

                if successCount > 0 {
                    // 刷新班级数据
                    self.refreshCurrentClass()

                    // 如果全部成功，关闭表单
                    if errorCount == 0 {
                        self.showManualAddStudent = false
                    }
                }

                print("批量添加学生完成: 成功 \(successCount) 个，失败 \(errorCount) 个")
            }
        }
    }
    

    
    /**
     * 处理创建家庭请求（从无家庭提示弹窗或创建家庭按钮）
     */
    func handleCreateFamilyFromAlert() {
        showNoFamilyAlert = false

        // 检查用户
        guard currentUser != nil else {
            print("无法创建家庭：用户不存在")
            return
        }

        // 显示创建家庭弹窗
        familyName = ""
        showCreateFamilyDialog = true

        print("显示创建家庭弹窗")
    }

    /**
     * 执行创建家庭
     */
    func createFamily() {
        // 验证家庭名称
        let trimmedName = familyName.trimmingCharacters(in: .whitespacesAndNewlines)

        if trimmedName.isEmpty {
            createFamilyErrorMessage = "create_family.validation.name_empty".localized
            return
        }

        if trimmedName.count > 30 {
            createFamilyErrorMessage = "create_family.validation.name_too_long".localized
            return
        }

        // 检查家庭名称是否已存在
        if families.contains(where: { $0.name == trimmedName }) {
            createFamilyErrorMessage = "create_family.validation.name_exists".localized
            return
        }

        // 开始创建家庭
        isCreatingFamily = true

        guard let user = currentUser else {
            createFamilyErrorMessage = "创建家庭失败：用户不存在"
            isCreatingFamily = false
            return
        }

        // 使用CoreData创建家庭
        let newFamily = coreDataManager.createFamily(name: trimmedName, for: user)

        // 重新加载家庭数据
        loadFamilies()

        // 选中新创建的家庭
        if let newIndex = families.firstIndex(where: { $0.id == newFamily.id }) {
            selectedFamilyIndex = newIndex
        }

        // 重置状态
        isCreatingFamily = false
        showCreateFamilyDialog = false
        resetCreateFamilyState()

        // 显示成功消息
        showCreateFamilySuccessAlert = true

        print("家庭创建成功: \(trimmedName)")
    }

    /**
     * 重置创建家庭状态
     */
    func resetCreateFamilyState() {
        familyName = ""
        isCreatingFamily = false
        createFamilyErrorMessage = nil
    }

    /**
     * 兼容性：处理创建班级请求（从无班级提示弹窗或创建班级按钮）
     */
    func handleCreateClassFromAlert() {
        showNoClassAlert = false

        // 检查会员权限
        guard let user = currentUser else {
            print("无法创建班级：用户不存在")
            return
        }

        if !user.canCreateMoreClasses() {
            showPermissionDeniedAlert = true
            return
        }

        // 显示创建班级弹窗
        className = ""
        showCreateClassDialog = true

        print("显示创建班级弹窗")
    }
    
    /**
     * 执行创建班级
     */
    func createClass() {
        // 验证班级名称
        let trimmedName = className.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedName.isEmpty {
            createClassErrorMessage = "create_class.validation.name_empty".localized
            return
        }
        
        if trimmedName.count > 30 {
            createClassErrorMessage = "create_class.validation.name_too_long".localized
            return
        }
        
        // 检查班级名称是否已存在
        if classes.contains(where: { $0.name == trimmedName }) {
            createClassErrorMessage = "create_class.validation.name_exists".localized
            return
        }
        
        // 开始创建班级
        isCreatingClass = true
        
        guard let user = currentUser else {
            createClassErrorMessage = "创建班级失败：用户不存在"
            isCreatingClass = false
            return
        }
        
        // 使用CoreData创建班级
        let newClass = coreDataManager.createClass(name: trimmedName, for: user)
        
        // 重新加载班级数据
        loadClasses()
        
        // 选中新创建的班级
        if let newIndex = classes.firstIndex(where: { $0.id == newClass.id }) {
            selectedClassIndex = newIndex
        }
        
        // 重置状态
        isCreatingClass = false
        showCreateClassDialog = false
        resetCreateClassState()
        
        // 显示成功消息
        showCreateClassSuccessAlert = true
        
        print("班级创建成功: \(trimmedName)")
    }
    
    /**
     * 重置创建班级状态
     */
    func resetCreateClassState() {
        className = ""
        isCreatingClass = false
        createClassErrorMessage = nil
    }
    
    /**
     * 获取权限不足提示消息
     */
    var permissionDeniedMessage: String {
        guard let user = currentUser else { return "" }
        
        return String(format: "create_class.permission_denied.message".localized,
                     userLevelDisplayName,
                     user.maxClassesAllowed,
                     classes.count)
    }
    
    /**
     * 关闭手动添加成员表单
     */
    func closeManualAddMember() {
        showManualAddMember = false
        addMemberError = nil
        print("关闭手动添加成员表单")
    }

    /**
     * 关闭无家庭提示弹窗
     */
    func closeNoFamilyAlert() {
        showNoFamilyAlert = false
    }

    /**
     * 清除添加成员错误信息
     */
    func clearAddMemberError() {
        addMemberError = nil
    }

    // MARK: - 兼容性方法

    /**
     * 兼容性：关闭手动添加学生表单
     */
    func closeManualAddStudent() {
        showManualAddStudent = false
        addStudentError = nil
        print("兼容性：关闭手动添加学生表单")
    }

    /**
     * 兼容性：关闭无班级提示弹窗
     */
    func closeNoClassAlert() {
        showNoClassAlert = false
        print("关闭无班级提示弹窗")
    }

    /**
     * 兼容性：清除添加学生错误信息
     */
    func clearAddStudentError() {
        addStudentError = nil
    }
    
    /**
     * 从当前家庭移除成员
     */
    func removeFamilyMember(_ member: FamilyMember) {
        coreDataManager.deleteFamilyMember(member)

        // 刷新家庭数据
        refreshCurrentFamily()

        print("删除家庭成员: \(member.name ?? "未知")")
    }

    /**
     * 兼容性：从当前班级移除学生
     */
    func removeStudent(_ student: Student) {
        // 兼容性方法，在家庭模式下不执行任何操作
        print("兼容性：removeStudent方法被调用，但在家庭模式下不执行任何操作")
    }
    
    /**
     * 全班加分
     */
    func addScoreToAllStudents(_ score: Int, reason: String = "全班加分") {
        guard let currentClass = selectedClass else { return }
        
        currentClass.addPointsToAllStudents(score, reason: reason, in: coreDataManager.viewContext)
        coreDataManager.save()
        
        // 刷新班级数据
        refreshCurrentClass()
        
        // 实时更新时间范围内的积分统计
        refreshRangeTotalScore()
        
        // 发送班级统计刷新通知
        sendClassStatisticsRefreshNotification(triggerSource: "class_operation")
        
        print("全班加分: +\(score) 分，原因: \(reason)")
    }
    
    /**
     * 全班扣分
     */
    func deductScoreFromAllStudents(_ score: Int, reason: String = "全班扣分") {
        guard let currentClass = selectedClass else { return }
        
        currentClass.deductPointsFromAllStudents(score, reason: reason, in: coreDataManager.viewContext)
        coreDataManager.save()
        
        // 刷新班级数据
        refreshCurrentClass()
        
        // 实时更新时间范围内的积分统计
        refreshRangeTotalScore()
        
        // 发送班级统计刷新通知
        sendClassStatisticsRefreshNotification(triggerSource: "class_operation")
        
        print("全班扣分: -\(score) 分，原因: \(reason)")
    }
    
    /**
     * 选择Tab页面
     */
    func selectTab(_ index: Int) {
        selectedTabIndex = index
    }
    
    // MARK: - Delete Mode Methods
    
    /**
     * 进入删除模式
     */
    func enterDeleteMode() {
        isDeleteMode = true
        print("进入删除模式")
    }
    
    /**
     * 退出删除模式
     */
    func exitDeleteMode() {
        isDeleteMode = false
        showDeleteConfirmation = false
        studentToDelete = nil
        print("退出删除模式")
    }
    
    /**
     * 请求删除家庭成员
     */
    func requestDeleteFamilyMember(_ member: FamilyMember) {
        memberToDelete = member
        showDeleteConfirmation = true
        print("请求删除家庭成员: \(member.name ?? "未知")")
    }

    /**
     * 确认删除家庭成员
     */
    func confirmDeleteFamilyMember() {
        guard let member = memberToDelete else { return }

        removeFamilyMember(member)
        exitDeleteMode()

        print("确认删除家庭成员: \(member.name ?? "未知")")
    }

    /**
     * 取消删除家庭成员
     */
    func cancelDeleteFamilyMember() {
        showDeleteConfirmation = false
        memberToDelete = nil
        print("取消删除家庭成员")
    }

    // MARK: - 兼容性删除方法

    /**
     * 兼容性：请求删除学生
     */
    func requestDeleteStudent(_ student: Student) {
        studentToDelete = student
        showDeleteConfirmation = true
        print("兼容性：请求删除学生: \(student.name ?? "未知")")
    }

    /**
     * 兼容性：确认删除学生
     */
    func confirmDeleteStudent() {
        guard let student = studentToDelete else { return }

        removeStudent(student)
        exitDeleteMode()

        print("兼容性：确认删除学生: \(student.name ?? "未知")")
    }

    /**
     * 兼容性：取消删除学生
     */
    func cancelDeleteStudent() {
        showDeleteConfirmation = false
        studentToDelete = nil
        print("兼容性：取消删除学生")
    }
    
    // MARK: - Student Data Methods
    
    /**
     * 根据学生ID获取学生信息
     */
    func getStudentById(_ studentId: String) -> Student? {
        guard let uuid = UUID(uuidString: studentId) else { return nil }
        return coreDataManager.getStudent(by: uuid)
    }
    
    /**
     * 刷新学生数据
     */
    func refreshStudentData() async {
        await MainActor.run {
            refreshCurrentClass()
        }
        print("刷新学生数据完成")
    }

    /**
     * 下拉刷新数据
     * 完整的数据刷新流程，包括班级数据、学生数据和积分统计
     */
    func pullToRefreshData() async {
        // 防止重复刷新
        await MainActor.run {
            if isRefreshing {
                print("⚠️ 正在刷新中，跳过重复请求")
                return
            }
            isRefreshing = true
        }

        print("🔄 开始下拉刷新数据...")

        await MainActor.run {
            // 1. 重新加载班级数据
            loadClasses()

            // 2. 如果有选中的班级，刷新当前班级数据
            if let selectedClass = selectedClass {
                // 刷新CoreData对象，确保获取最新数据
                coreDataManager.viewContext.refresh(selectedClass, mergeChanges: true)

                // 刷新当前班级的所有学生数据
                if let students = selectedClass.students as? Set<Student> {
                    for student in students {
                        coreDataManager.viewContext.refresh(student, mergeChanges: true)
                    }
                }
            }

            // 3. 更新积分统计
            refreshRangeTotalScore()

            // 4. 触发UI更新
            objectWillChange.send()

            print("✅ 下拉刷新完成 - 班级数: \(classes.count), 当前班级学生数: \(selectedClass?.students?.count ?? 0)")
        }

        // 延迟一小段时间确保刷新动画完成
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

        await MainActor.run {
            isRefreshing = false
        }
    }
    
    /**
     * 处理学生数据变更
     */
    func handleStudentDataChange(_ student: Student) {
        // CoreData会自动处理变更，只需保存
        coreDataManager.save()
        
        // 实时更新时间范围内的积分统计
        refreshRangeTotalScore()
        
        print("处理学生数据变更: \(student.name ?? "未知")")
    }
    
    /**
     * 重新加载班级数据
     * 用于在其他页面创建班级后刷新首页数据
     */
    func reloadClassData() {
        print("重新加载班级数据")
        loadClasses()
        
        // 确保选中索引有效
        if !classes.isEmpty {
            // 如果有班级但选中索引无效，则选中第一个班级
            if selectedClassIndex < 0 || selectedClassIndex >= classes.count {
                selectedClassIndex = 0
                print("修正选中班级索引为: 0")
            }
        } else {
            // 没有班级时设置为-1
            selectedClassIndex = -1
            print("没有班级，设置选中索引为: -1")
        }
        
        // 触发UI更新
        objectWillChange.send()
    }
    
    // MARK: - Private Methods

    /**
     * 刷新当前家庭数据
     */
    private func refreshCurrentFamily() {
        guard let currentFamily = selectedFamily else { return }

        // 刷新CoreData对象
        coreDataManager.viewContext.refresh(currentFamily, mergeChanges: true)

        // 刷新家庭成员数据
        for member in currentFamily.sortedMembers {
            coreDataManager.viewContext.refresh(member, mergeChanges: true)
        }

        // 触发UI更新
        objectWillChange.send()
    }

    /**
     * 兼容性：刷新当前班级数据
     */
    private func refreshCurrentClass() {
        // 兼容性方法，在家庭模式下刷新家庭数据
        refreshCurrentFamily()
    }
    

    
    // MARK: - Date Range Methods
    
    /**
     * 加载用户的日期范围偏好
     */
    private func loadUserDateRangePreference() {
        guard let user = currentUser else { return }
        
        selectedDateRange = user.getDateRangePreference()
        updateCurrentRangeTotalScore()
        
        print("加载日期范围偏好: \(selectedDateRange.displayText)")
    }
    
    /**
     * 保存用户的日期范围偏好
     */
    private func saveUserDateRangePreference() {
        guard let user = currentUser else { return }
        
        user.saveDateRangePreference(selectedDateRange)
        updateCurrentRangeTotalScore()
        
        print("保存日期范围偏好: \(selectedDateRange.displayText)")
    }
    
    /**
     * 设置日期范围观察者
     */
    private func setupDateRangeObserver() {
        $selectedDateRange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.saveUserDateRangePreference()
            }
            .store(in: &cancellables)
    }
    
    /**
     * 更新当前时间范围内的总积分
     */
    private func updateCurrentRangeTotalScore() {
        guard let user = currentUser else {
            currentRangeTotalScore = 0
            return
        }
        
        currentRangeTotalScore = user.calculateClassTotalScore(for: selectedDateRange, in: selectedClass)
        print("更新时间范围内总积分: \(currentRangeTotalScore)")
    }
    
    /**
     * 显示日期范围选择器
     */
    func showDateRangeSelector() {
        showDateRangePicker = true
        print("显示日期范围选择器")
    }
    
    /**
     * 隐藏日期范围选择器
     */
    func hideDateRangeSelector() {
        showDateRangePicker = false
        print("隐藏日期范围选择器")
    }
    
    /**
     * 获取当前时间范围的显示文本
     */
    var currentDateRangeDisplayText: String {
        return selectedDateRange.displayText
    }
    
    /**
     * 刷新时间范围内的积分统计（用于实时更新）
     */
    func refreshRangeTotalScore() {
        updateCurrentRangeTotalScore()
    }
    
    // MARK: - Class Operation Methods
    
    /**
     * 处理全员操作按钮点击
     */
    func handleFamilyOperation() {
        // 清除之前的错误信息
        classOperationError = nil

        // 检查是否已创建班级
        if classes.isEmpty {
            showNoClassAlert = true
            print("未创建家庭，显示提示弹窗")
        } else {
            showClassOperationOptions = true
            print("显示全员操作选项弹窗")
        }
    }

    /**
     * 处理全班操作按钮点击（保持向后兼容）
     */
    func handleClassOperation() {
        handleFamilyOperation()
    }
    
    /**
     * 显示全家加分表单
     */
    func showAddPointsForm() {
        classOperationType = .add
        familyOperationType = .add
        showClassOperationOptions = false
        showClassOperationForm = true
        print("显示全家加分表单")
    }

    /**
     * 显示全家扣分表单
     */
    func showDeductPointsForm() {
        classOperationType = .deduct
        familyOperationType = .deduct
        showClassOperationOptions = false
        showClassOperationForm = true
        print("显示全家扣分表单")
    }
    
    /**
     * 提交全员操作
     */
    func submitFamilyOperation(name: String, value: Int) {
        guard selectedClass != nil else {
            classOperationError = "family_operation.error.no_family_selected".localized
            return
        }

        // 设置提交状态
        isSubmittingClassOperation = true
        classOperationError = nil

        // 延迟处理，提供用户反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            switch self.familyOperationType {
            case .add:
                self.addScoreToAllStudents(value, reason: name)
                print("全家加分操作: +\(value) 分，原因: \(name)")
            case .deduct:
                self.deductScoreFromAllStudents(value, reason: name)
                print("全家扣分操作: -\(value) 分，原因: \(name)")
            }

            // 完成操作
            self.isSubmittingClassOperation = false
            self.showClassOperationForm = false

            print("全员操作提交成功")
        }
    }

    /**
     * 提交全班操作（保持向后兼容）
     */
    func submitClassOperation(name: String, value: Int) {
        guard selectedClass != nil else {
            classOperationError = "class_operation.error.no_class_selected".localized
            return
        }

        // 设置提交状态
        isSubmittingClassOperation = true
        classOperationError = nil

        // 延迟处理，提供用户反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            switch self.classOperationType {
            case .add:
                self.addScoreToAllStudents(value, reason: name)
                print("全班加分操作: +\(value) 分，原因: \(name)")
            case .deduct:
                self.deductScoreFromAllStudents(value, reason: name)
                print("全班扣分操作: -\(value) 分，原因: \(name)")
            }

            // 完成操作
            self.isSubmittingClassOperation = false
            self.showClassOperationForm = false

            print("全班操作提交成功")
        }
    }
    
    /**
     * 关闭全员操作弹窗
     */
    func closeFamilyOperationDialogs() {
        showClassOperationOptions = false
        showClassOperationForm = false
        classOperationError = nil
        print("关闭全员操作弹窗")
    }

    /**
     * 关闭全班操作弹窗（保持向后兼容）
     */
    func closeClassOperationDialogs() {
        closeFamilyOperationDialogs()
    }
    
    /**
     * 清除全班操作错误信息
     */
    func clearClassOperationError() {
        classOperationError = nil
    }
    
    // MARK: - 通知监听方法

    /**
     * 设置通知监听器
     */
    private func setupNotificationObservers() {
        // 监听学生积分变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleStudentPointsChange(_:)),
            name: .studentPointsDidChange,
            object: nil
        )
        
        // 监听班级统计刷新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleClassStatisticsRefresh(_:)),
            name: .classStatisticsNeedsRefresh,
            object: nil
        )
        
        // 监听会员降级需要处理班级冻结通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSubscriptionDowngrade(_:)),
            name: .subscriptionDowngradeNeedsAction,
            object: nil
        )
        
        // 监听会员升级可解冻班级通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSubscriptionUpgrade(_:)),
            name: .subscriptionUpgradeClassUnfreeze,
            object: nil
        )
        
        // 监听班级状态变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleClassStatusChanged(_:)),
            name: .classStatusChanged,
            object: nil
        )
        
        print("📝 已设置通知监听器")
    }

    /**
     * 处理学生积分变更通知
     */
    @objc private func handleStudentPointsChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let studentId = userInfo[NotificationUserInfoKey.studentId] as? String,
              let pointsChange = userInfo[NotificationUserInfoKey.pointsChange] as? Int,
              let reason = userInfo[NotificationUserInfoKey.reason] as? String else {
            print("⚠️ 学生积分变更通知数据不完整")
            return
        }
        
        DispatchQueue.main.async {
            // 实时更新时间范围内的积分统计
            self.refreshRangeTotalScore()
            
            print("📊 已更新统计数据：学生\(studentId) 积分变化\(pointsChange)，原因：\(reason)")
        }
    }

    /**
     * 处理班级统计刷新通知
     */
    @objc private func handleClassStatisticsRefresh(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let classId = userInfo[NotificationUserInfoKey.classId] as? String,
              let triggerSource = userInfo[NotificationUserInfoKey.triggerSource] as? String else {
            print("⚠️ 班级统计刷新通知数据不完整")
            return
        }
        
        // 检查是否为当前选中的班级
        if let currentClassId = selectedClass?.id?.uuidString,
           currentClassId == classId {
            DispatchQueue.main.async {
                // 刷新当前班级数据
                self.refreshCurrentClass()
                // 更新统计数据
                self.refreshRangeTotalScore()
                
                print("📊 已刷新班级统计：班级\(classId)，触发源：\(triggerSource)")
            }
        }
    }

    /**
     * 处理会员降级通知
     */
    @objc private func handleSubscriptionDowngrade(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let oldLevel = userInfo[NotificationUserInfoKey.oldLevel] as? String,
              let newLevel = userInfo[NotificationUserInfoKey.newLevel] as? String else {
            print("⚠️ 会员降级通知数据不完整")
            return
        }
        
        // 设置订阅等级名称
        previousSubscriptionLevelName = oldLevel
        currentSubscriptionLevelName = newLevel
        
        // 获取新等级的订阅类型
        let newSubscriptionLevel = Subscription.Level(rawValue: newLevel) ?? .free
        
        // 设置允许的活跃班级数
        allowedActiveClassCount = newSubscriptionLevel.maxClasses
        
        // 获取活跃班级
        guard let user = currentUser else { return }
        activeClasses = classManagementService.getActiveClasses(for: user)
        
        // 如果活跃班级数量超出新等级限制，显示班级冻结选择界面
        if activeClasses.count > allowedActiveClassCount {
            DispatchQueue.main.async {
                self.showClassFreezeSelection = true
            }
        }
    }
    
    /**
     * 处理会员升级通知
     */
    @objc private func handleSubscriptionUpgrade(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let oldLevel = userInfo[NotificationUserInfoKey.oldLevel] as? String,
              let newLevel = userInfo[NotificationUserInfoKey.newLevel] as? String,
              let availableCount = userInfo[NotificationUserInfoKey.availableUnfreezeCount] as? Int else {
            print("⚠️ 会员升级通知数据不完整")
            return
        }
        
        // 设置订阅等级名称
        previousSubscriptionLevelName = oldLevel
        currentSubscriptionLevelName = newLevel
        
        // 设置可解冻班级数量
        availableUnfreezeCount = availableCount
        
        // 获取冻结的班级
        guard let user = currentUser else { return }
        frozenClasses = classManagementService.getFrozenClasses(for: user)
        
        // 检查是否升级为高级会员
        let isUpgradeToAdvanced = Subscription.Level(rawValue: newLevel) == .premium
        
        // 如果升级为高级会员，自动解冻所有班级
        if isUpgradeToAdvanced && !frozenClasses.isEmpty {
            print("🔓 检测到升级为高级会员，自动解冻所有班级")
            let allFrozenClassIds = frozenClasses.compactMap { $0.id?.uuidString }
            
            // 自动解冻所有班级
            if !allFrozenClassIds.isEmpty {
                let success = classManagementService.handleClassUnfreeze(
                    for: user,
                    unfreezeClassIds: allFrozenClassIds
                )
                
                if success {
                    print("✅ 高级会员自动解冻所有班级成功")
                    // 重新获取班级列表
                    fetchClasses()
                } else {
                    print("❌ 高级会员自动解冻班级失败")
                }
            }
        }
        // 否则，如果有冻结的班级且可解冻数量大于0，显示解冻选择界面
        else if !frozenClasses.isEmpty && availableUnfreezeCount > 0 {
            DispatchQueue.main.async {
                self.showClassUnfreezeSelection = true
            }
        }
    }
    
    /**
     * 处理班级状态变更通知
     */
    @objc private func handleClassStatusChanged(_ notification: Notification) {
        // 重新获取班级列表
        fetchClasses()
    }
    
    /**
     * 清理通知监听器
     */
    private func removeNotificationObservers() {
        NotificationCenter.default.removeObserver(self, name: .studentPointsDidChange, object: nil)
        NotificationCenter.default.removeObserver(self, name: .classStatisticsNeedsRefresh, object: nil)
        NotificationCenter.default.removeObserver(self, name: .subscriptionDowngradeNeedsAction, object: nil)
        NotificationCenter.default.removeObserver(self, name: .subscriptionUpgradeClassUnfreeze, object: nil)
        NotificationCenter.default.removeObserver(self, name: .classStatusChanged, object: nil)
        print("📝 已清理通知监听器")
    }

    /**
     * 发送班级统计刷新通知
     */
    private func sendClassStatisticsRefreshNotification(triggerSource: String) {
        guard let classId = selectedClass?.id?.uuidString else { return }
        
        NotificationCenter.default.post(
            name: .classStatisticsNeedsRefresh,
            object: nil,
            userInfo: [
                NotificationUserInfoKey.classId: classId,
                NotificationUserInfoKey.triggerSource: triggerSource
            ]
        )
    }

    deinit {
        removeNotificationObservers()
    }

    // MARK: - 班级冻结管理方法
    
    /**
     * 处理会员降级冻结班级选择
     */
    func handleClassFreezeSelection(_ selectedClassIds: [String]) {
        guard let user = currentUser else { return }
        
        let oldLevel = Subscription.Level(rawValue: previousSubscriptionLevelName) ?? .free
        let newLevel = Subscription.Level(rawValue: user.subscriptionLevel) ?? .free
        
        let success = classManagementService.handleSubscriptionDowngrade(
            for: user,
            fromLevel: oldLevel,
            toLevel: newLevel,
            keepingActiveClassIds: selectedClassIds
        )
        
        if success {
            // 重新获取班级列表
            fetchClasses()
            
            // 根据选择的班级ID设置当前选中的班级
            if let selectedClassId = selectedClassIds.first,
               let selectedIndex = classes.firstIndex(where: { $0.id?.uuidString == selectedClassId }) {
                selectClass(at: selectedIndex)
            } else if !classes.isEmpty {
                selectClass(at: 0)
            }
            
            // 关闭弹窗
            showClassFreezeSelection = false
        }
    }
    
    /**
     * 处理会员升级解冻班级选择
     */
    func handleClassUnfreezeSelection(_ selectedClassIds: [String]) {
        guard let user = currentUser else { return }
        
        let success = classManagementService.handleClassUnfreeze(
            for: user,
            unfreezeClassIds: selectedClassIds
        )
        
        if success {
            // 重新获取班级列表
            fetchClasses()
            
            // 关闭弹窗
            showClassUnfreezeSelection = false
        }
    }
    
    /**
     * 取消班级解冻
     */
    func cancelClassUnfreeze() {
        showClassUnfreezeSelection = false
    }
    
    /**
     * 打开订阅页面
     */
    func openSubscriptionPage() {
        showSubscriptionView = true
    }

    // MARK: - 排序相关方法

    /**
     * 处理排序选项
     * 当用户点击已选中的班级按钮时调用
     */
    func handleSortOptions() {
        showSortOptions = true
        print("显示排序选项菜单")
    }

    /**
     * 设置排序类型为按学号排序
     */
    func sortByStudentNumber() {
        sortType = .byStudentNumber
        print("设置排序方式：按学号排序")
    }

    /**
     * 设置排序类型为按积分排序
     */
    func sortByScore() {
        sortType = .byScore
        print("设置排序方式：按积分排序")
    }
}