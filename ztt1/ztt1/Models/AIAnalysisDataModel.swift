//
//  AIAnalysisDataModel.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation

/**
 * 学生AI分析数据模型
 * 用于向AI服务传输脱敏后的学生数据
 * 不包含学生真实姓名、学号等敏感信息
 */
struct StudentAnalysisData: Codable {
    let studentName: String         // 学生姓名
    let gender: String              // 性别：male/female
    let gradeLevel: String          // 年级：从班级信息推断
    let pointRecords: [PointRecordData]
    
    /**
     * 从学生实体创建分析数据
     * @param student CoreData学生实体
     * @return 脱敏后的分析数据，如果数据不足则返回nil
     */
    static func from(student: Student) -> StudentAnalysisData? {
        // 获取积分记录，排除已撤销的记录
        let validRecords = student.sortedPointRecords.filter { !$0.isReversed }
        
        // 检查记录数量，至少需要10条
        guard validRecords.count >= 10 else {
            print("⚠️ 积分记录不足，当前记录数: \(validRecords.count)，需要至少10条")
            return nil
        }
        
        // 转换积分记录
        let pointRecords = validRecords.map { record in
            PointRecordData(
                reason: record.reason ?? "未知原因",
                value: Int(record.value),
                timestamp: record.timestamp ?? Date()
            )
        }
        
        // 推断年级信息（从班级名称或默认值）
        let gradeLevel = inferGradeLevel(from: student.schoolClass)
        
        return StudentAnalysisData(
            studentName: student.name ?? "未知学生",
            gender: student.gender ?? "male",
            gradeLevel: gradeLevel,
            pointRecords: pointRecords
        )
    }
    
    /**
     * 从班级信息推断年级
     */
    private static func inferGradeLevel(from schoolClass: SchoolClass?) -> String {
        guard let className = schoolClass?.name else {
            return "小学"
        }
        
        // 简单的年级推断逻辑，可以根据需要扩展
        if className.contains("幼儿园") || className.contains("幼") {
            return "幼儿园"
        } else if className.contains("一年级") || className.contains("1年级") {
            return "小学一年级"
        } else if className.contains("二年级") || className.contains("2年级") {
            return "小学二年级"
        } else if className.contains("三年级") || className.contains("3年级") {
            return "小学三年级"
        } else if className.contains("四年级") || className.contains("4年级") {
            return "小学四年级"
        } else if className.contains("五年级") || className.contains("5年级") {
            return "小学五年级"
        } else if className.contains("六年级") || className.contains("6年级") {
            return "小学六年级"
        } else {
            return "小学"
        }
    }
    
    /**
     * 获取统计信息
     */
    var statistics: AnalysisStatistics {
        let positiveRecords = pointRecords.filter { $0.value > 0 }
        let negativeRecords = pointRecords.filter { $0.value < 0 }
        
        return AnalysisStatistics(
            totalRecords: pointRecords.count,
            positiveRecords: positiveRecords.count,
            negativeRecords: negativeRecords.count,
            totalPositivePoints: positiveRecords.reduce(0) { $0 + $1.value },
            totalNegativePoints: negativeRecords.reduce(0) { $0 + $1.value }
        )
    }
}

/**
 * 脱敏后的积分记录数据
 */
struct PointRecordData: Codable {
    let reason: String              // 加/扣分原因
    let value: Int                  // 分值（正数为加分，负数为扣分）
    let timestamp: Date             // 时间戳
    
    /**
     * 格式化时间显示
     */
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 分值类型
     */
    var isPositive: Bool {
        return value > 0
    }
}

/**
 * 分析统计信息
 */
struct AnalysisStatistics {
    let totalRecords: Int           // 总记录数
    let positiveRecords: Int        // 加分记录数
    let negativeRecords: Int        // 扣分记录数
    let totalPositivePoints: Int    // 总加分
    let totalNegativePoints: Int    // 总扣分
    
    /**
     * 积极行为比例
     */
    var positiveRatio: Double {
        guard totalRecords > 0 else { return 0.0 }
        return Double(positiveRecords) / Double(totalRecords)
    }
} 