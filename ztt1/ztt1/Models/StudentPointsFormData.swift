//
//  StudentPointsFormData.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation

/**
 * 学生积分操作表单数据模型
 * 用于自定义积分操作的表单数据管理和验证
 */
struct StudentPointsFormData {
    
    // MARK: - 表单项数据结构
    struct FormItem: Identifiable {
        let id = UUID()
        var name: String = ""
        var value: String = ""
        let operationType: StudentPointsOperation.OperationType
        
        // MARK: - 初始化
        init(operationType: StudentPointsOperation.OperationType) {
            self.operationType = operationType
        }
        
        // MARK: - 计算属性
        
        /**
         * 获取分值的整数值
         */
        var valueInt: Int {
            return Int(value) ?? 1
        }
        
        /**
         * 检查表单项是否有效
         */
        var isValid: Bool {
            return !formattedName.isEmpty &&
                   valueInt > 0 &&
                   valueInt <= 100 &&
                   formattedName.count <= 20
        }
        
        /**
         * 获取格式化的操作名称（去除空格）
         */
        var formattedName: String {
            return name.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        /**
         * 转换为积分操作对象
         */
        func toOperation() -> StudentPointsOperation? {
            guard isValid else { return nil }
            
            return StudentPointsOperation(
                type: operationType,
                name: formattedName,
                value: valueInt
            )
        }
        
        // MARK: - 验证方法
        
        /**
         * 验证操作名称格式
         */
        func validateName() -> ValidationResult {
            let trimmedName = formattedName
            if trimmedName.isEmpty {
                return .invalid("student_points.validation.name_empty".localized)
            }
            if trimmedName.count > 20 {
                return .invalid("操作名称长度不能超过20个字符")
            }
            return .valid
        }
        
        /**
         * 验证分值格式
         */
        func validateValue() -> ValidationResult {
            if value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                return .invalid("student_points.validation.value_empty".localized)
            }
            
            guard let intValue = Int(value.trimmingCharacters(in: .whitespacesAndNewlines)) else {
                return .invalid("student_points.validation.value_invalid".localized)
            }
            
            if intValue <= 0 {
                return .invalid("student_points.validation.value_positive".localized)
            }
            
            if intValue > 100 {
                return .invalid("分值不能超过100")
            }
            
            return .valid
        }
        
        /**
         * 完整验证表单项
         */
        func validate() -> ValidationResult {
            // 1. 验证名称
            let nameResult = validateName()
            if case .invalid = nameResult {
                return nameResult
            }
            
            // 2. 验证分值
            let valueResult = validateValue()
            if case .invalid = valueResult {
                return valueResult
            }
            
            return .valid
        }
    }
    
    // MARK: - 主数据结构属性
    var items: [FormItem]
    let operationType: StudentPointsOperation.OperationType
    
    // MARK: - 初始化
    init(operationType: StudentPointsOperation.OperationType) {
        self.operationType = operationType
        self.items = [FormItem(operationType: operationType)]
    }
    
    // MARK: - 计算属性
    
    /**
     * 获取所有有效的表单项
     */
    var validItems: [FormItem] {
        return items.filter { $0.isValid }
    }
    
    /**
     * 检查是否有任何有效数据
     */
    var hasValidData: Bool {
        return !validItems.isEmpty
    }
    
    /**
     * 检查是否可以添加更多表单项
     */
    var canAddMoreItems: Bool {
        return items.count < 5 // 最多5个表单项
    }
    
    /**
     * 检查是否可以删除表单项
     */
    var canDeleteItems: Bool {
        return items.count > 1
    }
    
    /**
     * 获取预计总积分变化
     */
    var totalPointsChange: Int {
        let change = validItems.reduce(0) { $0 + $1.valueInt }
        switch operationType {
        case .add:
            return change
        case .deduct:
            return -change
        }
    }
    
    // MARK: - 表单操作方法
    
    /**
     * 添加新的表单项
     */
    mutating func addNewItem() {
        guard canAddMoreItems else { return }
        items.append(FormItem(operationType: operationType))
    }
    
    /**
     * 删除指定索引的表单项
     */
    mutating func removeItem(at index: Int) {
        guard canDeleteItems && index < items.count else { return }
        items.remove(at: index)
    }
    
    /**
     * 重置表单数据
     */
    mutating func reset() {
        items = [FormItem(operationType: operationType)]
    }
    
    // MARK: - 验证方法
    
    /**
     * 验证所有表单项
     */
    func validateItems() -> StudentPointsFormValidationResult {
        var errorMessages: [String] = []
        var validatedItems: [FormItem] = []
        
        // 检查内部重复
        var seenNames: [String: Int] = [:]
        
        for (index, item) in items.enumerated() {
            let itemIndex = index + 1
            let trimmedName = item.formattedName.lowercased()
            
            // 跳过空白项
            if item.formattedName.isEmpty && item.value.isEmpty {
                continue
            }
            
            // 检查基本验证
            let basicValidation = item.validate()
            if case .invalid(let message) = basicValidation {
                errorMessages.append("第\(itemIndex)项：\(message)")
                continue
            }
            
            // 检查批次内重复
            if let firstIndex = seenNames[trimmedName] {
                errorMessages.append("第\(itemIndex)项：与第\(firstIndex)项重复")
                continue
            }
            
            seenNames[trimmedName] = itemIndex
            validatedItems.append(item)
        }
        
        // 检查是否有有效项
        if validatedItems.isEmpty && !items.allSatisfy({ $0.formattedName.isEmpty && $0.value.isEmpty }) {
            errorMessages.append("没有有效的操作项")
        }
        
        return StudentPointsFormValidationResult(
            isValid: errorMessages.isEmpty && !validatedItems.isEmpty,
            errorMessages: errorMessages,
            validItems: validatedItems
        )
    }
    
    /**
     * 转换为积分操作数组
     */
    func toOperations() -> [StudentPointsOperation] {
        return validItems.compactMap { $0.toOperation() }
    }
}

// MARK: - 表单验证结果
struct StudentPointsFormValidationResult {
    let isValid: Bool
    let errorMessages: [String]
    let validItems: [StudentPointsFormData.FormItem]
    
    var errorSummary: String {
        return errorMessages.joined(separator: "\n")
    }
}

// MARK: - 便利扩展
extension StudentPointsFormData {
    
    /**
     * 从现有操作创建表单数据
     */
    static func from(operations: [StudentPointsOperation]) -> StudentPointsFormData? {
        guard let firstOperation = operations.first else { return nil }
        
        var formData = StudentPointsFormData(operationType: firstOperation.type)
        
        formData.items = operations.map { operation in
            var item = FormItem(operationType: operation.type)
            item.name = operation.name
            item.value = String(operation.value)
            return item
        }
        
        return formData
    }
    
    /**
     * 快速创建单项表单
     */
    static func singleItem(operationType: StudentPointsOperation.OperationType, name: String, value: Int) -> StudentPointsFormData {
        var formData = StudentPointsFormData(operationType: operationType)
        formData.items[0].name = name
        formData.items[0].value = String(value)
        return formData
    }
} 