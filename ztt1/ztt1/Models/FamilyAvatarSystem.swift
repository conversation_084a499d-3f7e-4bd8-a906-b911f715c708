//
//  FamilyAvatarSystem.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation
import SwiftUI

/**
 * 家庭头像系统
 * 管理不同角色的头像资源和显示逻辑
 */
struct FamilyAvatarSystem {
    
    // MARK: - 头像配置
    
    /**
     * 头像类型枚举
     */
    enum AvatarType: String, CaseIterable {
        case father = "father"
        case mother = "mother"
        case son = "son"
        case daughter = "daughter"
        case other = "other"
        
        var displayName: String {
            switch self {
            case .father:
                return "头像.爸爸"
            case .mother:
                return "头像.妈妈"
            case .son:
                return "头像.儿子"
            case .daughter:
                return "头像.女儿"
            case .other:
                return "头像.其他"
            }
        }
    }
    
    /**
     * 头像样式枚举
     */
    enum AvatarStyle: String, CaseIterable {
        case cartoon = "cartoon"      // 卡通风格
        case realistic = "realistic"  // 写实风格
        case minimal = "minimal"      // 简约风格
        
        var displayName: String {
            switch self {
            case .cartoon:
                return "卡通风格"
            case .realistic:
                return "写实风格"
            case .minimal:
                return "简约风格"
            }
        }
    }
    
    // MARK: - 头像资源管理
    
    /**
     * 获取角色的默认头像名称
     */
    static func getDefaultAvatarName(for role: FamilyRole, style: AvatarStyle = .cartoon) -> String {
        // 直接返回实际的头像资源名称
        switch role {
        case .father:
            return "爸爸头像"
        case .mother:
            return "妈妈头像"
        case .son:
            return "男生头像"
        case .daughter:
            return "女生头像"
        case .other:
            return "其他头像"
        }
    }
    
    /**
     * 获取角色的所有可用头像
     */
    static func getAvailableAvatars(for role: FamilyRole) -> [AvatarOption] {
        var avatars: [AvatarOption] = []
        
        // 为每个角色提供多个头像选项
        for style in AvatarStyle.allCases {
            for index in 1...3 {  // 每种风格提供3个选项
                let avatarName = "avatar_\(role.rawValue)_\(style.rawValue)_\(index)"
                let option = AvatarOption(
                    id: avatarName,
                    name: avatarName,
                    style: style,
                    role: role,
                    isDefault: index == 1 && style == .cartoon
                )
                avatars.append(option)
            }
        }
        
        return avatars
    }
    
    /**
     * 获取角色的颜色主题
     */
    static func getColorTheme(for role: FamilyRole) -> AvatarColorTheme {
        switch role {
        case .father:
            return AvatarColorTheme(
                primary: Color(hex: "#4A90E2"),
                secondary: Color(hex: "#357ABD"),
                background: Color(hex: "#E3F2FD")
            )
        case .mother:
            return AvatarColorTheme(
                primary: Color(hex: "#FF6B9D"),
                secondary: Color(hex: "#E91E63"),
                background: Color(hex: "#FCE4EC")
            )
        case .son:
            return AvatarColorTheme(
                primary: Color(hex: "#50C878"),
                secondary: Color(hex: "#4CAF50"),
                background: Color(hex: "#E8F5E8")
            )
        case .daughter:
            return AvatarColorTheme(
                primary: Color(hex: "#FFB347"),
                secondary: Color(hex: "#FF9800"),
                background: Color(hex: "#FFF3E0")
            )
        case .other:
            return AvatarColorTheme(
                primary: Color(hex: "#9B59B6"),
                secondary: Color(hex: "#8E44AD"),
                background: Color(hex: "#F3E5F5")
            )
        }
    }
    
    /**
     * 生成头像占位符
     */
    static func generatePlaceholder(for role: FamilyRole, name: String) -> AvatarPlaceholder {
        let colorTheme = getColorTheme(for: role)
        let initials = getInitials(from: name)
        
        return AvatarPlaceholder(
            initials: initials,
            backgroundColor: colorTheme.primary,
            textColor: .white,
            role: role
        )
    }
    
    /**
     * 从姓名中提取首字母
     */
    private static func getInitials(from name: String) -> String {
        let components = name.components(separatedBy: .whitespaces)
        let initials = components.compactMap { $0.first }.map { String($0) }
        
        if initials.count >= 2 {
            return initials[0] + initials[1]
        } else if initials.count == 1 {
            return initials[0]
        } else {
            return "?"
        }
    }
    
    // MARK: - 头像验证
    
    /**
     * 验证头像是否适合指定角色
     */
    static func isAvatarValid(avatarName: String, for role: FamilyRole) -> Bool {
        // 检查头像名称是否包含角色标识
        return avatarName.contains(role.rawValue) || avatarName.contains("default") || avatarName.contains("other")
    }
    
    /**
     * 获取备用头像
     */
    static func getFallbackAvatar(for role: FamilyRole) -> String {
        return getDefaultAvatarName(for: role, style: .minimal)
    }
}

// MARK: - 数据模型

/**
 * 头像选项
 */
struct AvatarOption: Identifiable, Hashable {
    let id: String
    let name: String
    let style: FamilyAvatarSystem.AvatarStyle
    let role: FamilyRole
    let isDefault: Bool
    
    var displayName: String {
        return "\(role.shortName) - \(style.displayName)"
    }
}

/**
 * 头像颜色主题
 */
struct AvatarColorTheme {
    let primary: Color
    let secondary: Color
    let background: Color
}

/**
 * 头像占位符
 */
struct AvatarPlaceholder {
    let initials: String
    let backgroundColor: Color
    let textColor: Color
    let role: FamilyRole
}

// MARK: - SwiftUI 组件

/**
 * 家庭成员头像视图
 */
struct FamilyMemberAvatarView: View {
    let member: FamilyMember
    let size: CGFloat
    let showBorder: Bool
    
    init(member: FamilyMember, size: CGFloat = 60, showBorder: Bool = true) {
        self.member = member
        self.size = size
        self.showBorder = showBorder
    }
    
    var body: some View {
        Group {
            if let avatarName = member.avatar, !avatarName.isEmpty {
                // 显示自定义头像
                Image(avatarName)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                // 显示占位符
                let role = FamilyRole(rawValue: member.role ?? "other") ?? .other
                let placeholder = FamilyAvatarSystem.generatePlaceholder(
                    for: role,
                    name: member.name ?? "?"
                )
                
                ZStack {
                    Circle()
                        .fill(placeholder.backgroundColor)
                    
                    Text(placeholder.initials)
                        .font(.system(size: size * 0.4, weight: .semibold))
                        .foregroundColor(placeholder.textColor)
                }
            }
        }
        .frame(width: size, height: size)
        .clipShape(Circle())
        .overlay(
            Circle()
                .stroke(
                    showBorder ? FamilyAvatarSystem.getColorTheme(
                        for: FamilyRole(rawValue: member.role ?? "other") ?? .other
                    ).primary : Color.clear,
                    lineWidth: showBorder ? 2 : 0
                )
        )
    }
}

/**
 * 头像选择器视图
 */
struct AvatarSelectorView: View {
    let role: FamilyRole
    @Binding var selectedAvatar: String
    
    private let columns = Array(repeating: GridItem(.flexible()), count: 3)
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(FamilyAvatarSystem.getAvailableAvatars(for: role)) { option in
                Button(action: {
                    selectedAvatar = option.name
                }) {
                    VStack(spacing: 8) {
                        Image(option.name)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 60, height: 60)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(
                                        selectedAvatar == option.name ? 
                                        FamilyAvatarSystem.getColorTheme(for: role).primary : 
                                        Color.gray.opacity(0.3),
                                        lineWidth: selectedAvatar == option.name ? 3 : 1
                                    )
                            )
                        
                        if option.isDefault {
                            Text("默认")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding()
    }
}
