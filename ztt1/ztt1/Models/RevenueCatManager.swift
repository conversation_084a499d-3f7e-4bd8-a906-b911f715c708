//
//  RevenueCatManager.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/20.
//

import Foundation
import RevenueCat
import StoreKit
import CloudKit
import Combine

/**
 * RevenueCat订阅管理器
 * 负责处理订阅购买、恢复、状态检查和与CoreData的同步
 * 支持 free / basic / premium 三个等级
 */
@MainActor
class RevenueCatManager: NSObject, ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = RevenueCatManager()
    
    // MARK: - Published Properties
    @Published var customerInfo: CustomerInfo?
    @Published var offerings: Offerings?
    @Published var currentSubscriptionLevel: SubscriptionLevel = .free
    @Published var isInitialized: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var purchaseSuccess: Bool = false
    
    // MARK: - Product IDs
    enum ProductIDs {
        static let basicMonthly = "com.ztt1.subscription.monthly.basic"
        static let basicYearly = "com.ztt1.subscription.yearly.basic"
        static let premiumMonthly = "com.ztt1.subscription.monthly.premium"
        static let premiumYearly = "com.ztt1.subscription.yearly.premium"
    }
    
    // MARK: - Entitlement IDs
    enum EntitlementIDs {
        static let basicMember = "basic_member"
        static let premiumMember = "premium_member"
    }
    
    // MARK: - Subscription Levels
    enum SubscriptionLevel: String, CaseIterable {
        case free = "free"
        case basic = "basic"
        case premium = "premium"
        
        var displayName: String {
            switch self {
            case .free:
                return "subscription.level.free".localized
            case .basic:
                return "subscription.level.basic".localized
            case .premium:
                return "subscription.level.premium".localized
            }
        }
        
        var maxClasses: Int {
            switch self {
            case .free:
                return 1
            case .basic:
                return 2
            case .premium:
                return 5
            }
        }
        
        var features: [SubscriptionFeature] {
            switch self {
            case .free:
                return [.basicScoring, .prizeExchange, .multiDeviceSync]  // 免费用户也支持多设备同步
            case .basic:
                return [.basicScoring, .prizeExchange, .lottery, .multiDeviceSync]
            case .premium:
                return [.basicScoring, .prizeExchange, .lottery, .advancedGames, .aiAnalysis, .multiDeviceSync]
            }
        }
    }
    
    // MARK: - Subscription Features
    enum SubscriptionFeature: String, CaseIterable {
        case basicScoring = "basic_scoring"
        case prizeExchange = "prize_exchange"
        case lottery = "lottery"
        case advancedGames = "advanced_games"
        case aiAnalysis = "ai_analysis"
        case multiDeviceSync = "multi_device_sync"
        
        var displayName: String {
            switch self {
            case .basicScoring:
                return "subscription.feature.basic_scoring".localized
            case .prizeExchange:
                return "subscription.feature.prize_exchange".localized
            case .lottery:
                return "subscription.feature.lottery".localized
            case .advancedGames:
                return "subscription.feature.advanced_games".localized
            case .aiAnalysis:
                return "subscription.feature.ai_analysis".localized
            case .multiDeviceSync:
                return "subscription.feature.multi_device_sync".localized
            }
        }
    }
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let trialManager = TrialManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private override init() {
        super.init()
        setupSubscriptionStatusListener()
    }
    
    // MARK: - Configuration
    
    /**
     * 配置RevenueCat SDK
     * 在App启动时调用，需要提供API Key
     */
    func configure(apiKey: String, userId: String? = nil) {
        guard !apiKey.isEmpty else {
            print("❌ RevenueCat API Key不能为空")
            errorMessage = "subscription.error.configuration_failed".localized
            return
        }
        
        // 配置RevenueCat
        Purchases.logLevel = .debug
        Purchases.configure(withAPIKey: apiKey, appUserID: userId)
        
        // 设置delegate
        Purchases.shared.delegate = self
        
        // 获取初始数据
        Task {
            await loadInitialData()
        }
        
        print("✅ RevenueCat已配置，API Key: \(apiKey)")
    }
    
    /**
     * 加载初始数据
     */
    private func loadInitialData() async {
        isLoading = true
        
        do {
            // 获取用户信息
            let customerInfo = try await Purchases.shared.customerInfo()
            await updateCustomerInfo(customerInfo)
            
            // 获取产品信息
            let offerings = try await Purchases.shared.offerings()
            await MainActor.run {
                self.offerings = offerings
                self.isInitialized = true
                self.isLoading = false
            }
            
            print("✅ RevenueCat初始数据加载完成")
            
        } catch {
            await MainActor.run {
                self.errorMessage = "subscription.error.loading_failed".localized
                self.isLoading = false
            }
            print("❌ RevenueCat初始数据加载失败: \(error)")
        }
    }
    
    // MARK: - Purchase Methods

    /**
     * 提取详细错误信息
     */
    private func extractDetailedError(from error: Error) -> String {

        // 检查错误描述中的关键词来提供更友好的错误信息
        let errorDescription = error.localizedDescription.lowercased()

        if errorDescription.contains("user cancelled") ||
           errorDescription.contains("用户取消") ||
           errorDescription.contains("cancelled") ||
           errorDescription.contains("cancel") {
            return "用户取消了购买"
        } else if errorDescription.contains("network") || errorDescription.contains("网络") {
            return "网络错误，请检查网络连接"
        } else if errorDescription.contains("product") && errorDescription.contains("not found") {
            return "产品未找到，请稍后重试"
        } else if errorDescription.contains("payment") && errorDescription.contains("not allowed") {
            return "当前设备不允许支付"
        } else if errorDescription.contains("store") && errorDescription.contains("problem") {
            return "App Store服务异常，请稍后重试"
        } else if errorDescription.contains("ready_to_submit") || errorDescription.contains("missing_metadata") {
            return "产品尚未在App Store Connect中激活，请联系开发者"
        } else if errorDescription.contains("receipt") {
            return "购买凭证问题，请重试"
        } else if errorDescription.contains("pending") {
            return "支付处理中，请稍候"
        }

        // 检查是否是StoreKit错误
        if let storeKitError = error as? StoreKitError {
            switch storeKitError {
            case .userCancelled:
                return "用户取消了购买"
            case .unknown:
                return "未知错误，请重试"
            default:
                return "购买失败：\(storeKitError.localizedDescription)"
            }
        }

        return "购买失败：\(error.localizedDescription)"
    }
    
    /**
     * 购买订阅产品
     */
    func purchaseProduct(productId: String) async -> Bool {
        guard let offerings = offerings else {
            await MainActor.run {
                errorMessage = "subscription.error.products_not_loaded".localized
            }
            return false
        }

        // 查找产品
        guard let package = findPackage(for: productId, in: offerings) else {
            await MainActor.run {
                errorMessage = "subscription.error.product_not_found".localized
            }
            return false
        }

        await MainActor.run {
            isLoading = true
            errorMessage = nil // 清除之前的错误
        }

        print("🆔 准备购买产品: \(productId)")

        // 检查是否在沙盒环境
        #if DEBUG
        print("🧪 当前运行在调试模式，支持沙盒测试")
        #endif

        do {
            let result = try await Purchases.shared.purchase(package: package)

            // 检查购买结果
            if !result.userCancelled {
                // 检查是否在沙盒环境
                let environment = result.customerInfo.originalAppUserId.contains("$RCAnonymousID") ? "生产环境" : "沙盒环境"
                print("✅ 购买成功 - 环境: \(environment)")
                print("✅ 用户ID: \(result.customerInfo.originalAppUserId)")

                await updateCustomerInfo(result.customerInfo)

                // 触发成功购买的处理
                await handleSuccessfulPurchase(productId: productId)

                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = nil
                }
                return true
            } else {
                // 用户取消了购买
                print("ℹ️ 用户取消了购买")
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = "用户取消了购买"
                }
                return false
            }

        } catch {
            await MainActor.run {
                let detailedError = self.extractDetailedError(from: error)
                // 如果错误信息为空，可能是产品状态问题
                if detailedError.isEmpty || detailedError == "购买失败：" {
                    self.errorMessage = "产品暂时不可用，请稍后重试。如果问题持续存在，请联系开发者。"
                } else {
                    self.errorMessage = detailedError
                }
                self.isLoading = false
            }
            print("❌ 购买失败: \(error)")
            print("❌ 详细错误: \(error.localizedDescription)")
            return false
        }
    }
    
    /**
     * 恢复购买
     */
    func restorePurchases() async -> Bool {
        isLoading = true
        
        do {
            let customerInfo = try await Purchases.shared.restorePurchases()
            await updateCustomerInfo(customerInfo)
            
            isLoading = false
            return true
            
        } catch {
            await MainActor.run {
                let detailedError = self.extractDetailedError(from: error)
                self.errorMessage = detailedError
                self.isLoading = false
            }
            print("❌ 恢复购买失败: \(error)")
            print("❌ 详细错误: \(error.localizedDescription)")
            return false
        }
    }
    
    /**
     * 代码赠送30天高级会员
     * 用于新用户试用或特殊活动
     */
    func grantPremiumTrialDays(_ days: Int) async -> Bool {
        // 注意：RevenueCat本身不支持代码赠送，需要通过服务器端处理
        // 这里我们通过本地记录来模拟，实际应用中需要通过RevenueCat的REST API
        
        guard let user = coreDataManager.getCurrentUser() else {
            errorMessage = "subscription.error.user_not_found".localized
            return false
        }
        
        // 检查用户是否已经享受过试用
        if user.subscription?.level != "free" {
            errorMessage = "subscription.error.already_premium".localized
            return false
        }
        
        // 更新本地订阅状态
        let expirationDate = Calendar.current.date(byAdding: .day, value: days, to: Date())!
        
        await MainActor.run {
            user.subscription?.level = "premium"
            user.subscription?.updatedAt = expirationDate
            
            // 保存到CoreData
            coreDataManager.save()
            
            // 更新本地状态
            self.currentSubscriptionLevel = .premium
            
            // 发送通知
            NotificationCenter.default.post(name: .subscriptionStatusChanged, object: nil)
        }
        
        print("✅ 成功赠送\(days)天高级会员")
        return true
    }
    
    // MARK: - Status Check Methods
    
    /**
     * 检查特定功能权限
     */
    func hasFeature(_ feature: SubscriptionFeature) -> Bool {
        return currentSubscriptionLevel.features.contains(feature)
    }
    
    /**
     * 检查是否为付费用户
     */
    var isPaidUser: Bool {
        return currentSubscriptionLevel != .free
    }
    
    /**
     * 检查是否为高级用户
     */
    var isPremiumUser: Bool {
        return currentSubscriptionLevel == .premium
    }
    
    /**
     * 获取订阅到期日期
     * 优先返回RevenueCat订阅到期时间，然后返回试用到期时间
     */
    var expirationDate: Date? {
        guard let customerInfo = customerInfo else {
            // 如果没有RevenueCat信息，检查试用状态
            return trialManager.isTrialActive ? trialManager.trialExpirationDate : nil
        }

        // 1. 优先检查RevenueCat高级会员权限
        if let premiumEntitlement = customerInfo.entitlements[EntitlementIDs.premiumMember],
           premiumEntitlement.isActive {
            return premiumEntitlement.expirationDate
        }

        // 2. 检查RevenueCat基础会员权限
        if let basicEntitlement = customerInfo.entitlements[EntitlementIDs.basicMember],
           basicEntitlement.isActive {
            return basicEntitlement.expirationDate
        }

        // 3. 检查试用状态（仅在没有正式订阅时）
        if trialManager.isTrialActive {
            return trialManager.trialExpirationDate
        }

        return nil
    }
    
    // MARK: - Private Helper Methods
    
    /**
     * 查找指定产品ID的Package
     */
    private func findPackage(for productId: String, in offerings: Offerings) -> Package? {
        for offering in offerings.all.values {
            for package in offering.availablePackages {
                if package.storeProduct.productIdentifier == productId {
                    return package
                }
            }
        }
        return nil
    }
    
    /**
     * 更新客户信息
     */
    private func updateCustomerInfo(_ customerInfo: CustomerInfo) async {
        await MainActor.run {
            self.customerInfo = customerInfo
            self.currentSubscriptionLevel = determineSubscriptionLevel(from: customerInfo)
            
            // 同步到CoreData
            syncSubscriptionToCoreData()
            
            // 发送状态变更通知
            NotificationCenter.default.post(name: .subscriptionStatusChanged, object: nil)
        }
    }
    
    /**
     * 根据CustomerInfo确定订阅级别
     * 优先判断RevenueCat订阅，然后判断试用状态
     */
    private func determineSubscriptionLevel(from customerInfo: CustomerInfo) -> SubscriptionLevel {
        // 1. 优先检查RevenueCat高级会员权限
        if let premiumEntitlement = customerInfo.entitlements[EntitlementIDs.premiumMember],
           premiumEntitlement.isActive {
            return .premium
        }

        // 2. 检查RevenueCat基础会员权限
        if let basicEntitlement = customerInfo.entitlements[EntitlementIDs.basicMember],
           basicEntitlement.isActive {
            return .basic
        }

        // 3. 检查试用状态（仅在没有正式订阅时）
        if trialManager.isTrialActive {
            return .premium  // 试用期间享受高级会员权益
        }

        return .free
    }
    
    /**
     * 同步订阅状态到CoreData
     */
    private func syncSubscriptionToCoreData() {
        guard let user = coreDataManager.getCurrentUser() else { return }
        
        let oldLevel = user.subscription?.level ?? "free"
        let newLevel = currentSubscriptionLevel.rawValue
        
        // 更新用户订阅信息
        if user.subscription == nil {
            user.subscription = Subscription.createFreeSubscription(for: user, in: coreDataManager.viewContext)
        }
        
        user.subscription?.level = newLevel
        user.subscription?.maxClasses = Int32(currentSubscriptionLevel.maxClasses)
        user.subscription?.updatedAt = expirationDate ?? Date()
        
        // 保存到CoreData
        coreDataManager.save()
        
        // 处理订阅级别变更
        if oldLevel != newLevel {
            handleSubscriptionLevelChange(from: oldLevel, to: newLevel)
        }
        
        print("✅ 订阅状态已同步到CoreData: \(newLevel)")
    }
    
    /**
     * 处理成功购买
     */
    private func handleSuccessfulPurchase(productId: String) async {
        print("✅ 购买成功: \(productId)")
        
        // 这里可以添加购买成功后的处理逻辑
        // 例如：发送分析事件、显示感谢页面等
    }
    
    /**
     * 处理订阅级别变更
     */
    private func handleSubscriptionLevelChange(from oldLevel: String, to newLevel: String) {
        print("🔄 订阅级别变更: \(oldLevel) → \(newLevel)")
        
        // 使用现有的SubscriptionService逻辑
        SubscriptionService.shared.handleSubscriptionLevelChange(oldLevel: oldLevel, newLevel: newLevel)
    }
    
    /**
     * 设置订阅状态监听器
     */
    private func setupSubscriptionStatusListener() {
        // 监听订阅状态变更通知
        NotificationCenter.default.publisher(for: .subscriptionStatusChanged)
            .sink { _ in
                // 处理订阅状态变更
                print("📢 接收到订阅状态变更通知")
            }
            .store(in: &cancellables)

        // 监听试用状态变化
        trialManager.$isTrialActive
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.refreshSubscriptionStatus()
                }
            }
            .store(in: &cancellables)
    }

    /**
     * 刷新订阅状态（用于试用状态变化时）
     */
    private func refreshSubscriptionStatus() {
        if let customerInfo = customerInfo {
            currentSubscriptionLevel = determineSubscriptionLevel(from: customerInfo)
        } else {
            // 没有RevenueCat信息时，仅基于试用状态判断
            currentSubscriptionLevel = trialManager.isTrialActive ? .premium : .free
        }

        // 发送状态变更通知
        NotificationCenter.default.post(name: .subscriptionStatusChanged, object: nil)
    }
}

// MARK: - PurchasesDelegate

extension RevenueCatManager: PurchasesDelegate {

    /**
     * 当订阅状态发生变化时调用
     */
    nonisolated func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        Task { @MainActor in
            await updateCustomerInfo(customerInfo)
            print("📢 RevenueCat订阅状态更新")

            // 检查是否有活跃的订阅，如果有则标记购买成功
            let hasActiveSubscription = !customerInfo.activeSubscriptions.isEmpty
            if hasActiveSubscription && isLoading {
                print("✅ 检测到活跃订阅，标记购买成功")
                purchaseSuccess = true
                isLoading = false
                errorMessage = nil
            }
        }
    }

    /**
     * 当收到促销购买时调用
     */
    nonisolated func purchases(_ purchases: Purchases,
                              readyForPromotedProduct product: StoreProduct,
                              purchase startPurchase: @escaping StartPurchaseBlock) {
        // 处理促销购买
        startPurchase { (transaction, customerInfo, error, cancelled) in
            if let error = error {
                print("❌ 促销购买失败: \(error)")
            } else if !cancelled {
                print("✅ 促销购买成功")
            }
        }
    }
}

// MARK: - Product Information Helper

extension RevenueCatManager {
    
    /**
     * 获取产品价格信息
     */
    func getProductPrices() -> [String: String] {
        guard let offerings = offerings else { return [:] }
        
        var prices: [String: String] = [:]
        
        for offering in offerings.all.values {
            for package in offering.availablePackages {
                let productId = package.storeProduct.productIdentifier
                let price = package.storeProduct.localizedPriceString
                prices[productId] = price
            }
        }
        
        return prices
    }
    
    /**
     * 获取本地化的产品信息
     */
    func getLocalizedProductInfo(for productId: String) -> (title: String, description: String, price: String)? {
        guard let offerings = offerings,
              let package = findPackage(for: productId, in: offerings) else {
            return nil
        }
        
        let product = package.storeProduct
        return (
            title: product.localizedTitle,
            description: product.localizedDescription,
            price: product.localizedPriceString
        )
    }
}