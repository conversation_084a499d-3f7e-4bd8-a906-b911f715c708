//
//  StudentPointsOperation.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation

/**
 * 学生积分操作数据模型
 * 统一封装加分和扣分操作的数据结构
 */
struct StudentPointsOperation {
    
    // MARK: - 操作类型枚举
    enum OperationType: String, CaseIterable {
        case add = "add"
        case deduct = "deduct"
        
        var displayName: String {
            switch self {
            case .add:
                return "加分"
            case .deduct:
                return "扣分"
            }
        }
        
        var iconName: String {
            switch self {
            case .add:
                return "plus.circle"
            case .deduct:
                return "minus.circle"
            }
        }
        
        var colorHex: String {
            switch self {
            case .add:
                return "#74c07f"
            case .deduct:
                return "#e74c3c"
            }
        }
    }
    
    // MARK: - 属性
    let type: OperationType
    let name: String
    let value: Int
    let timestamp: Date
    let id: UUID
    
    // MARK: - 初始化
    init(type: OperationType, name: String, value: Int, timestamp: Date = Date()) {
        self.type = type
        self.name = name
        self.value = value
        self.timestamp = timestamp
        self.id = UUID()
    }
    
    // MARK: - 从Rule创建操作
    static func fromRule(_ rule: Rule) -> StudentPointsOperation? {
        guard let name = rule.name,
              let typeString = rule.type,
              let operationType = OperationType(rawValue: typeString) else {
            return nil
        }

        return StudentPointsOperation(
            type: operationType,
            name: name,
            value: Int(rule.value)
        )
    }

    // MARK: - 从FamilyMemberRule创建操作
    static func fromMemberRule(_ rule: FamilyMemberRule) -> StudentPointsOperation? {
        guard let name = rule.name,
              let typeString = rule.type,
              let operationType = OperationType(rawValue: typeString) else {
            return nil
        }

        return StudentPointsOperation(
            type: operationType,
            name: name,
            value: Int(rule.value)
        )
    }
    
    // MARK: - 计算属性
    
    /**
     * 获取积分变化值（加分为正，扣分为负）
     */
    var pointsChange: Int {
        switch type {
        case .add:
            return value
        case .deduct:
            return -value
        }
    }
    
    /**
     * 格式化显示分值
     */
    var formattedPoints: String {
        switch type {
        case .add:
            return "+\(value)"
        case .deduct:
            return "-\(value)"
        }
    }
    
    /**
     * 格式化显示时间
     */
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 操作描述
     */
    var description: String {
        return "\(type.displayName): \(name) (\(formattedPoints)分)"
    }
}

// MARK: - Identifiable
extension StudentPointsOperation: Identifiable {
    // id 已经定义为 UUID
}

// MARK: - Equatable
extension StudentPointsOperation: Equatable {
    static func == (lhs: StudentPointsOperation, rhs: StudentPointsOperation) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - 批量操作扩展
extension Array where Element == StudentPointsOperation {
    
    /**
     * 获取总积分变化
     */
    var totalPointsChange: Int {
        return self.reduce(0) { $0 + $1.pointsChange }
    }
    
    /**
     * 按类型分组
     */
    var groupedByType: [StudentPointsOperation.OperationType: [StudentPointsOperation]] {
        return Dictionary(grouping: self) { $0.type }
    }
    
    /**
     * 验证批量操作
     */
    func validateBatch() -> ValidationResult {
        // 检查是否为空
        if isEmpty {
            return .invalid("没有有效的操作")
        }
        
        // 检查是否所有操作都是同一类型
        let types = Set(self.map { $0.type })
        if types.count > 1 {
            return .invalid("批量操作必须是同一类型")
        }
        
        // 检查分值范围
        for operation in self {
            if operation.value <= 0 {
                return .invalid("分值必须为正数")
            }
            if operation.value > 100 {
                return .invalid("单次操作分值不能超过100")
            }
        }
        
        return .valid
    }
} 