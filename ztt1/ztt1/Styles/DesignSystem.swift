//
//  DesignSystem.swift
//  ztt1
//
//  Created by AI Assistant on 2025/6/23.
//

import SwiftUI

/**
 * 设计系统 - 包含所有UI常量和样式定义
 * 基于design.json规范实现
 */
struct DesignSystem {
    
    // MARK: - Colors
    struct Colors {
        static let primary = Color(hex: "#B5E36B")
        static let primaryLight = Color(hex: "#E8F9C5")
        static let secondary = Color(hex: "#FFE49E")
        static let textPrimary = Color(hex: "#555555")
        static let textSecondary = Color(hex: "#999999")
        static let textTertiary = Color(hex: "#CCCCCC")
        static let textScore = Color(hex: "#87C441")
        static let errorColor = Color(hex: "#FF5B5B")
        static let textButton = Color.white
        static let background = Color(hex: "#fcfff4")
        static let cardBackground = Color.white
        static let tagSelected = Color(hex: "#FFE49E")
        static let tagUnselected = Color.white
        static let tabBarActive = Color(hex: "#A5C84A")
        static let tabBarInactive = Color(hex: "#CCCCCC")
        static let studentCardBorder = Color(hex: "#F2F2F2")
        
        // 学生详情页专用颜色
        static let studentDetailCardBackground = Color(hex: "#edf6d9")
        static let actionButtonBackground = Color(hex: "#a9d051")
        static let scoreDisplay = Color(hex: "#74c07f")
        static let historyBackground = Color(hex: "#ededed")
        static let textPositive = Color(hex: "#26C34B")
        static let textNegative = Color(hex: "#FF5B5B")
        
        // 设置页面专用颜色
        static let createButtonBackground = Color(hex: "#57b583")
        static let classCardBackground = Color(hex: "#edf5d9") 
        static let configButtonBackground = Color(hex: "#cee29a")
        static let functionCardBackground = Color(hex: "#dcf1a4")
        
        // 个人中心页面专用颜色
        static let profileUserInfoBackground = Color(hex: "#edf6d9")
        static let profileSubscriptionBannerBackground = Color(hex: "#b7da93")
        static let profileSubscriptionButtonBackground = Color(hex: "#5dbb93")
        static let profileSettingsItemBackground = Color.white.opacity(0.9)
        static let profileSettingsIconColor = Color(hex: "#666666")
        static let profileSettingsTextColor = Color(hex: "#333333")
        static let profileSettingsArrowColor = Color(hex: "#CCCCCC")
    }
    
    // MARK: - Typography
    struct Typography {
        static let fontFamily = "PingFang SC"
        
        struct HeadingLarge {
            static let fontSize: CGFloat = 24
            static let fontWeight = Font.Weight.bold
            static let lineHeight: CGFloat = 32
        }
        
        struct HeadingMedium {
            static let fontSize: CGFloat = 18
            static let fontWeight = Font.Weight.semibold
            static let lineHeight: CGFloat = 24
        }
        
        struct Body {
            static let fontSize: CGFloat = 16
            static let fontWeight = Font.Weight.regular
            static let lineHeight: CGFloat = 22
        }
        
        struct Caption {
            static let fontSize: CGFloat = 14
            static let fontWeight = Font.Weight.regular
            static let lineHeight: CGFloat = 20
        }
        
        struct Score {
            static let fontSize: CGFloat = 18
            static let fontWeight = Font.Weight.medium
        }
        
        struct StudentId {
            static let fontSize: CGFloat = 36
            static let fontWeight = Font.Weight.heavy
            static let opacity: Double = 0.08
        }
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let pageHorizontal: CGFloat = 25  // 页面水平边距，专门用于首页组件
    }
    
    // MARK: - Radius
    struct Radius {
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 24
        static let round: CGFloat = 1000
    }
    
    // MARK: - Component Styles
    struct SearchBar {
        static let height: CGFloat = 40
        static let paddingHorizontal: CGFloat = 16
        static let borderRadius: CGFloat = 20
        static let backgroundColor = Colors.cardBackground
        static let placeholderColor = Color(hex: "#888888")
    }
    
    struct StudentCard {
        static let widthRatio: CGFloat = 0.45
        static let height: CGFloat = 100  // 增加高度从72到90
        static let backgroundColor = Colors.cardBackground
        static let borderRadius: CGFloat = 12
        static let padding: CGFloat = 8
        static let avatarSize: CGFloat = 40
        static let spacingBetweenElements: CGFloat = 8
        
        // MARK: - 新增：独立布局配置集成
        struct Layout {
            static let defaultConfig = StudentCardLayoutConfig.default
            static let compactConfig = StudentCardLayoutConfig.compact
            static let largeConfig = StudentCardLayoutConfig.large
            static let extraLargeConfig = StudentCardLayoutConfig.extraLarge
        }
    }
    
    struct CustomButton {
        static let height: CGFloat = 40
        static let paddingHorizontal: CGFloat = 24
        static let backgroundColor = Colors.primaryLight
        static let borderRadius: CGFloat = Radius.md
        static let textColor = Colors.textButton
    }
    
    struct TabBar {
        static let height: CGFloat = 72
        static let backgroundColor = Colors.cardBackground
        static let activeColor = Colors.tabBarActive
        static let inactiveColor = Colors.tabBarInactive
        static let activeBackground = Colors.secondary
    }
    
    struct LiquidTabBar {
        static let height: CGFloat = 100
        static let backgroundColor = Color.white
        static let borderColor = Color(hex: "#a9d051")
        static let borderWidth: CGFloat = 5
        static let bubbleColor = Color(hex: "#FFE49E")
        static let bubbleIconColor = Color(hex: "#666666")
        static let bubbleSize: CGFloat = 80
        static let bubbleIconSize: CGFloat = 35
        static let archHeight: CGFloat = 15
        static let transitionWidth: CGFloat = 40
        static let inactiveIconColor = Color(hex: "#CCCCCC")
        
        // 统一动画配置
        static let animationResponse: Double = 0.6
        static let animationDamping: Double = 0.7
        
        // 图标对称性调整
        static let rightIconOffsetX: CGFloat = -20  // 右侧图标向左偏移量
    }
    
    // MARK: - Delete Mode Styles
    struct DeleteMode {
        static let deleteButtonSize: CGFloat = 26
        static let deleteButtonOffset: CGPoint = CGPoint(x: 55, y: -40) // 调整到右上角
        static let deleteButtonBackground = Color.red
        static let deleteButtonIcon = Color.white
        static let longPressMinimumDuration: Double = 0.6
        static let deleteCardBorderColor = Color.red.opacity(0.8)
        static let shakeAnimationDistance: CGFloat = 3
    }
    
    // MARK: - Confirmation Dialog Styles
    struct ConfirmationDialog {
        static let cornerRadius: CGFloat = 16
        static let maxWidth: CGFloat = 300
        static let backgroundColor = Color.white
        static let overlayColor = Color.black.opacity(0.4)
        static let borderColor = Colors.primary
        static let cancelButtonColor = Color.gray
        static let deleteButtonColor = Color.red
    }
    
    // MARK: - Settings Page Styles
    struct SettingsPage {
        struct ClassCard {
            static let cornerRadius: CGFloat = 12
            static let height: CGFloat = 70
            static let iconSize: CGFloat = 28
            static let spacing: CGFloat = 8
            static let padding: CGFloat = 12
        }
        
        struct FunctionCard {
            static let cornerRadius: CGFloat = 12
            static let height: CGFloat = 80
            static let iconSize: CGFloat = 20
            static let spacing: CGFloat = 12
            static let padding: CGFloat = 16
            static let bottomMarginPercentage: CGFloat = 0.04 // 底部边距为屏幕高度的15%
        }
        
        struct CreateButton {
            static let cornerRadius: CGFloat = 16
            static let height: CGFloat = 32
            static let paddingHorizontal: CGFloat = 16
        }
        
        struct ConfigButton {
            static let cornerRadius: CGFloat = 12
            static let height: CGFloat = 24
            static let paddingHorizontal: CGFloat = 12
        }
    }
    
    // MARK: - Profile Page Styles
    struct ProfilePage {
        struct UserInfoCard {
            static let height: CGFloat = 120
            static let avatarSize: CGFloat = 48
            static let illustrationSize: CGFloat = 80
            static let cornerRadius: CGFloat = 16
            static let padding: CGFloat = 20
            static let nameFont: CGFloat = 16
            static let membershipFont: CGFloat = 12
            static let idFont: CGFloat = 14
            static let dateFont: CGFloat = 12
        }
        
        struct SubscriptionBanner {
            static let height: CGFloat = 60
            static let cornerRadius: CGFloat = 12
            static let buttonHeight: CGFloat = 32
            static let buttonCornerRadius: CGFloat = 16
            static let padding: CGFloat = 16
            static let textFont: CGFloat = 14
            static let buttonFont: CGFloat = 12
        }
        
        struct SettingsItem {
            static let height: CGFloat = 56
            static let iconSize: CGFloat = 20
            static let cornerRadius: CGFloat = 12
            static let spacing: CGFloat = 8
            static let padding: CGFloat = 16
            static let textFont: CGFloat = 14
            static let arrowSize: CGFloat = 16
        }
    }
    
    // MARK: - Subscription Page Styles
    struct SubscriptionPage {
        struct UserInfoSection {
            static let heightPercentage: CGFloat = 0.30 // 30%屏幕高度
            static let avatarSize: CGFloat = 48
            static let crownSize: CGFloat = 96 // 变大1.5倍：64 * 1.5 = 96
            static let crownRotation: Double = 30 // 调整为30度
            static let cornerRadius: CGFloat = 30
            static let padding: CGFloat = 20
            static let nameFont: CGFloat = 20
            static let membershipFont: CGFloat = 12
            static let idFont: CGFloat = 14
            static let dateFont: CGFloat = 12
            
            // MARK: - 独立定位配置
            // 每个元素都有独立的位置参数，可单独调整而不影响其他元素
            
            // 用户头像独立位置
            static var avatarPositionX: CGFloat = 60  // 头像X坐标
            static var avatarPositionY: CGFloat = 120 // 头像Y坐标
            
            // 用户信息独立位置  
            static var userInfoPositionX: CGFloat = 200 // 用户信息X坐标
            static var userInfoPositionY: CGFloat = 0   // 用户信息Y坐标偏移（基于屏幕中心）
            
            // 皇冠图标独立位置
            static var crownPositionOffsetX: CGFloat = 60  // 皇冠距离右边距离
            static var crownPositionY: CGFloat = 60       // 皇冠Y坐标
            
            // 返回按钮独立位置
            static var backButtonPositionX: CGFloat = 50   // 返回按钮X坐标  
            static var backButtonPositionY: CGFloat = 40   // 返回按钮Y坐标
        }
        
        struct BackButton {
            static let size: CGFloat = 40
            static let iconSize: CGFloat = 20
            static let offsetFromEdge: CGFloat = 30
            static let backgroundColor = Color.white.opacity(0.9)
            static let cornerRadius: CGFloat = 20
        }
        
        struct MembershipTab {
            static let heightPercentage: CGFloat = 0.25 // 距离顶部25%
            static let height: CGFloat = 44
            static let cornerRadius: CGFloat = 16
            static let fontSize: CGFloat = 14
            static let fontWeight = Font.Weight.medium
            static let activeTextColor = Color.white
            static let inactiveTextColor = Color(hex: "#333333")
            static let activeShadowColor = Color(hex: "#a9cd53").opacity(0.3)
            static let inactiveShadowColor = Color.black.opacity(0.1)
            
            // 📌 新增：分段选项卡调整参数 - 让用户可以手动调整
            
            // 选项卡整体位置调整
            static var offsetX: CGFloat = 0              // 水平偏移 (负值向左，正值向右)
            static var offsetY: CGFloat = 30              // 垂直偏移 (负值向上，正值向下)
            static var topOffsetPercentage: CGFloat = 0.25 // 距离顶部的百分比位置 (0.0-1.0)
            
            // 选项卡尺寸调整
            static var tabWidth: CGFloat = 120           // 单个选项卡宽度
            static var tabHeight: CGFloat = 44           // 选项卡高度 (覆盖上面的height)
            static var totalWidth: CGFloat = 280         // 整个选项卡组件总宽度
            static var scaleX: CGFloat = 1.9             // 水平缩放比例
            static var scaleY: CGFloat = 1.2             // 垂直缩放比例
            
            // 选项卡间距调整
            static var spacing: CGFloat = 0              // 选项卡之间的间距
            static var horizontalPadding: CGFloat = 60   // 选项卡左右边距
            
            // 选项卡外观调整
            static var tabCornerRadius: CGFloat = 16     // 选项卡圆角半径
            static var selectedBackgroundOpacity: Double = 0.0 // 选中状态背景透明度 - 完全透明
            static var unselectedBackgroundOpacity: Double = 0.0 // 未选中状态背景透明度
            
            // 选项卡文字调整
            static var textFontSize: CGFloat = 14        // 文字大小
            static var selectedTextOpacity: Double = 0 // 选中文字透明度
            static var unselectedTextOpacity: Double = 0 // 未选中文字透明度
            
            // 选项卡阴影效果
            static var shadowRadius: CGFloat = 0         // 阴影半径 - 移除阴影
            static var shadowOpacity: Double = 0.0       // 阴影透明度 - 完全透明
            static var shadowOffsetX: CGFloat = 0        // 阴影水平偏移
            static var shadowOffsetY: CGFloat = 0        // 阴影垂直偏移
            
            // 选项卡动画效果
            static var animationDuration: Double = 0.3   // 切换动画持续时间
            static var animationSpringResponse: Double = 0.5 // 弹簧动画响应时间
            static var animationSpringDamping: Double = 0.7  // 弹簧动画阻尼系数
            
            // 选项卡交互效果
            static var pressedScale: CGFloat = 0.95      // 按下时的缩放比例
            static var hoverScale: CGFloat = 1.02        // 悬停时的缩放比例 (iPad支持)
            
            // 点击区域扩展 - 扩大触发范围，提升用户体验
            static var touchAreaPadding: CGFloat = 0    // 点击区域额外扩展的边距
            static var touchAreaMinHeight: CGFloat = 5  // 点击区域最小高度 (推荐44-60px)
            static var touchAreaMinWidth: CGFloat = 80   // 点击区域最小宽度
        }
        
        struct BackgroundImage {
            static let primaryImageName = "初级会员"
            static let advancedImageName = "高级会员"
            static let animationDuration: Double = 0.5
            
            // 📌 新增：背景图调整参数 - 让用户可以手动调整
            
            // 背景图整体位置调整
            static var offsetX: CGFloat = 0          // 水平偏移 (负值向左，正值向右)
            static var offsetY: CGFloat = 15          // 垂直偏移 (负值向上，正值向下)
            
            // 背景图尺寸调整
            static var scaleX: CGFloat = 1.0         // 水平缩放比例 (1.0为原始大小)
            static var scaleY: CGFloat = 1.0         // 垂直缩放比例 (1.0为原始大小)
            static var uniformScale: CGFloat = 1.1   // 统一缩放比例 (会同时影响scaleX和scaleY)
            
            // 背景图对齐方式
            enum AlignmentMode {
                case fill       // 填充整个区域
                case fit        // 适应区域大小
                case stretch    // 拉伸到指定大小
            }
            static var alignmentMode: AlignmentMode = .fill
            
            // 背景图裁剪区域 (百分比，0.0-1.0)
            static var cropTop: CGFloat = 0.0        // 顶部裁剪比例
            static var cropBottom: CGFloat = 0.0     // 底部裁剪比例  
            static var cropLeft: CGFloat = 0.0       // 左侧裁剪比例
            static var cropRight: CGFloat = 0.0      // 右侧裁剪比例
            
            // 背景图透明度
            static var opacity: Double = 1.0         // 背景图透明度 (0.0-1.0)
            
            // 背景图特效
            static var blur: CGFloat = 0.0           // 模糊效果 (0.0无模糊)
            static var brightness: Double = 0.0      // 亮度调整 (-1.0到1.0)
            static var contrast: Double = 1.0        // 对比度调整 (0.0到2.0)
            
            // 背景图层级控制
            static var zIndex: Double = 1.0          // 层级顺序 (数值越大越在上层)
        }
        
        struct ContentSection {
            static let topOffsetPercentage: CGFloat = 0.56 // 距离顶部45%开始
            static let horizontalPadding: CGFloat = 25
            static let verticalSpacing: CGFloat = 24
            // 新增：关键区块独立间距参数
            static let featureToPriceSpacing: CGFloat = 30 // 权益列表与价格卡片
            static let priceToButtonSpacing: CGFloat = 30  // 价格卡片与订阅按钮
            static let buttonToAgreementSpacing: CGFloat = 30 // 订阅按钮与勾选框
        }
        
        struct FeatureList {
            static let iconSize: CGFloat = 35
            static let textColor = Color(hex: "#8b8b8b")
            static let fontSize: CGFloat = 16
            static let spacing: CGFloat = 12
            static let verticalSpacing: CGFloat = 5
        }
        
        struct PriceCard {
            static let width: CGFloat = 160
            static let height: CGFloat = 160
            static let cornerRadius: CGFloat = 16
            static let spacing: CGFloat = 16
            static let selectedBackgroundColor = Color(hex: "#e7f7c4")
            static let selectedBorderColor = Color(hex: "#a9cd53")
            static let unselectedBackgroundColor = Color(hex: "#f1f6e7")
            static let unselectedBorderColor = Color(hex: "#b3c293")
            static let titleColor = Color(hex: "#8b8b8b")
            static let priceColor = Color(hex: "#a9cd53")
            static let titleFont: CGFloat = 26
            static let priceFont: CGFloat = 50
            static let unitFont: CGFloat = 24
            static let selectedBorderWidth: CGFloat = 10  // 选中状态边框宽度
            static let unselectedBorderWidth: CGFloat = 3 // 未选中状态边框宽度
        }
        
        struct SubscribeButton {
            static let height: CGFloat = 50
            static let cornerRadius: CGFloat = 24
            static let backgroundColor = Color(hex: "#a9cd53")
            static let textColor = Color.white
            static let fontSize: CGFloat = 20
            static let fontWeight = Font.Weight.semibold
        }
        
        struct AgreementSection {
            static let fontSize: CGFloat = 14
            static let textColor = Color(hex: "#a9cd53")
            static let checkboxSize: CGFloat = 16
            static let spacing: CGFloat = 15
        }
        
        // MARK: - 装饰元素配置
        struct DecorationElements {
            static let circle1Size: CGFloat = 100
            static let circle1Color = Color(hex: "#B5E36B").opacity(0.03)
            static let circle1Position: (x: CGFloat, y: CGFloat) = (-30, 20)
            
            static let circle2Size: CGFloat = 120  
            static let circle2Color = Color(hex: "#FFE49E").opacity(0.04)
            static let circle2Position: (x: CGFloat, y: CGFloat) = (40, -10)
            
            static let circle3Size: CGFloat = 80
            static let circle3Color = Color(hex: "#B5E36B").opacity(0.02) 
            static let circle3Position: (x: CGFloat, y: CGFloat) = (20, 30)
            
            static let circle4Size: CGFloat = 60
            static let circle4Color = Color.white.opacity(0.06)
            static let circle4Position: (x: CGFloat, y: CGFloat) = (-100, -50)
            
            static let circle5Size: CGFloat = 40
            static let circle5Color = Color(hex: "#FFE49E").opacity(0.05)
            static let circle5Position: (x: CGFloat, y: CGFloat) = (120, 80)
        }
        
        // MARK: - 动画预设配置
        struct AnimationPresets {
            static let entranceDelay: Double = 0.1
            static let staggerDelay: Double = 0.2
            static let springResponse: Double = 0.8
            static let springDamping: Double = 0.8
            
            struct Background {
                static let delay: Double = 0.0
                static let duration: Double = 1.0
                static let fadeInDuration: Double = 0.8
            }
            
            struct Decoration {
                static let delay: Double = 0.3
                static let duration: Double = 1.2
                static let rotationAngle: Double = 360
                static let scaleStart: CGFloat = 0.8
                static let scaleEnd: CGFloat = 1.0
            }
            
            struct UserInfo {
                static let delay: Double = 0.1
                static let duration: Double = 0.8
                static let offsetY: CGFloat = 50
                static let scaleStart: CGFloat = 0.8
                static let scaleEnd: CGFloat = 1.0
            }
            
            struct Content {
                static let delay: Double = 0.4
                static let duration: Double = 0.8
                static let offsetY: CGFloat = 80
                static let staggerInterval: Double = 0.1
            }
            
            struct Tab {
                static let delay: Double = 0.3
                static let duration: Double = 0.6
                static let scaleStart: CGFloat = 0.9
                static let scaleEnd: CGFloat = 1.0
                static let offsetY: CGFloat = 20
            }
            
            struct FeatureList {
                static let itemDelay: Double = 0.1
                static let baseDuration: Double = 0.5
                static let offsetX: CGFloat = -20
            }
            
            struct PriceCards {
                static let delay: Double = 0.5
                static let duration: Double = 0.8
                static let scaleStart: CGFloat = 0.9
                static let switchDuration: Double = 0.4
            }
            
            struct SubscribeButton {
                static let delay: Double = 0.6
                static let duration: Double = 0.7
                static let pulseScale: CGFloat = 1.05
                static let pulseDuration: Double = 1.5
            }
        }
    }
}

// MARK: - View Modifiers
extension View {
    /**
     * 应用搜索框样式
     */
    func searchBarStyle() -> some View {
        self
            .frame(height: DesignSystem.SearchBar.height)
            .padding(.horizontal, DesignSystem.SearchBar.paddingHorizontal)
            .background(DesignSystem.SearchBar.backgroundColor)
            .cornerRadius(DesignSystem.SearchBar.borderRadius)
            .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 4)
    }
    
    /**
     * 应用学生卡片样式
     */
    func studentCardStyle() -> some View {
        self
            .frame(height: DesignSystem.StudentCard.height)
            .padding(DesignSystem.StudentCard.padding)
            .background(DesignSystem.StudentCard.backgroundColor)
            .cornerRadius(DesignSystem.StudentCard.borderRadius)
    }
    
    /**
     * 应用主要按钮样式
     */
    func primaryButtonStyle() -> some View {
        self
            .frame(height: DesignSystem.CustomButton.height)
            .padding(.horizontal, DesignSystem.CustomButton.paddingHorizontal)
            .background(DesignSystem.Colors.primaryLight)
            .cornerRadius(DesignSystem.CustomButton.borderRadius)
    }
} 

// MARK: - iPad布局适配扩展
extension DesignSystem {
    
    /**
     * 动态获取当前设备适配的布局配置
     * 根据设备类型返回相应的布局参数
     */
    struct AdaptiveLayout {
        
        // MARK: - 订阅页面自适应布局
        struct SubscriptionPage {
            
            // MARK: - 用户信息区域自适应配置
            struct UserInfoSection {
                static var heightPercentage: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.heightPercentage :
                        DesignSystem.SubscriptionPage.UserInfoSection.heightPercentage
                }
                
                static var topPositionPercentage: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.topPositionPercentage :
                        0.2 // iPhone默认值
                }
                
                static var horizontalPadding: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.horizontalPadding :
                        25 // iPhone默认值
                }
                
                static var titleFontSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.titleFontSize :
                        22 // iPhone默认值
                }
                
                static var subtitleFontSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.subtitleFontSize :
                        16 // iPhone默认值
                }
                
                static var iconSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.iconSize :
                        40 // iPhone默认值
                }
                
                static var cornerRadius: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.cornerRadius :
                        16 // iPhone默认值
                }
                
                static var shadowRadius: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.UserInfoSection.shadowRadius :
                        8 // iPhone默认值
                }
            }
            
            // MARK: - 分段选项卡自适应配置
            struct MembershipTab {
                static var topOffsetPercentage: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.topOffsetPercentage :
                        DesignSystem.SubscriptionPage.MembershipTab.topOffsetPercentage
                }
                
                static var tabWidth: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.tabWidth :
                        DesignSystem.SubscriptionPage.MembershipTab.tabWidth
                }
                
                static var tabHeight: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.tabHeight :
                        DesignSystem.SubscriptionPage.MembershipTab.tabHeight
                }
                
                static var totalWidth: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.totalWidth :
                        DesignSystem.SubscriptionPage.MembershipTab.totalWidth
                }
                
                static var scaleX: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.scaleX :
                        DesignSystem.SubscriptionPage.MembershipTab.scaleX
                }
                
                static var scaleY: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.scaleY :
                        DesignSystem.SubscriptionPage.MembershipTab.scaleY
                }
                
                static var horizontalPadding: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.horizontalPadding :
                        DesignSystem.SubscriptionPage.MembershipTab.horizontalPadding
                }
                
                static var tabCornerRadius: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.tabCornerRadius :
                        DesignSystem.SubscriptionPage.MembershipTab.tabCornerRadius
                }
                
                static var textFontSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.textFontSize :
                        DesignSystem.SubscriptionPage.MembershipTab.textFontSize
                }
                
                static var selectedTextOpacity: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.selectedTextOpacity :
                        DesignSystem.SubscriptionPage.MembershipTab.selectedTextOpacity
                }
                
                static var unselectedTextOpacity: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.unselectedTextOpacity :
                        DesignSystem.SubscriptionPage.MembershipTab.unselectedTextOpacity
                }
                
                static var selectedBackgroundOpacity: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.selectedBackgroundOpacity :
                        DesignSystem.SubscriptionPage.MembershipTab.selectedBackgroundOpacity
                }
                
                static var unselectedBackgroundOpacity: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.unselectedBackgroundOpacity :
                        DesignSystem.SubscriptionPage.MembershipTab.unselectedBackgroundOpacity
                }
                
                static var shadowRadius: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.shadowRadius :
                        DesignSystem.SubscriptionPage.MembershipTab.shadowRadius
                }
                
                static var touchAreaMinHeight: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.touchAreaMinHeight :
                        DesignSystem.SubscriptionPage.MembershipTab.touchAreaMinHeight
                }
                
                static var shadowOpacity: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.shadowOpacity :
                        DesignSystem.SubscriptionPage.MembershipTab.shadowOpacity
                }
                
                static var shadowOffsetX: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.shadowOffsetX :
                        DesignSystem.SubscriptionPage.MembershipTab.shadowOffsetX
                }
                
                static var shadowOffsetY: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.shadowOffsetY :
                        DesignSystem.SubscriptionPage.MembershipTab.shadowOffsetY
                }
                
                static var animationDuration: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.animationDuration :
                        DesignSystem.SubscriptionPage.MembershipTab.animationDuration
                }
                
                static var animationSpringResponse: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.animationSpringResponse :
                        DesignSystem.SubscriptionPage.MembershipTab.animationSpringResponse
                }
                
                static var animationSpringDamping: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.animationSpringDamping :
                        DesignSystem.SubscriptionPage.MembershipTab.animationSpringDamping
                }
                
                static var pressedScale: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.pressedScale :
                        DesignSystem.SubscriptionPage.MembershipTab.pressedScale
                }
                
                static var hoverScale: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.hoverScale :
                        DesignSystem.SubscriptionPage.MembershipTab.hoverScale
                }
                
                static var touchAreaPadding: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.touchAreaPadding :
                        DesignSystem.SubscriptionPage.MembershipTab.touchAreaPadding
                }
                
                static var touchAreaMinWidth: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.touchAreaMinWidth :
                        DesignSystem.SubscriptionPage.MembershipTab.touchAreaMinWidth
                }
                
                static var spacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.spacing :
                        DesignSystem.SubscriptionPage.MembershipTab.spacing
                }
                
                static var offsetX: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.offsetX :
                        DesignSystem.SubscriptionPage.MembershipTab.offsetX
                }
                
                static var offsetY: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.MembershipTab.offsetY :
                        DesignSystem.SubscriptionPage.MembershipTab.offsetY
                }
            }
            
            // MARK: - 内容区域自适应配置
            struct ContentSection {
                static var topOffsetPercentage: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.ContentSection.topOffsetPercentage :
                        DesignSystem.SubscriptionPage.ContentSection.topOffsetPercentage
                }
                
                static var horizontalPadding: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.ContentSection.horizontalPadding :
                        DesignSystem.SubscriptionPage.ContentSection.horizontalPadding
                }
                
                static var verticalSpacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.ContentSection.verticalSpacing :
                        DesignSystem.SubscriptionPage.ContentSection.verticalSpacing
                }
                
                static var featureToPriceSpacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.ContentSection.featureToPriceSpacing :
                        DesignSystem.SubscriptionPage.ContentSection.featureToPriceSpacing
                }
                
                static var priceToButtonSpacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.ContentSection.priceToButtonSpacing :
                        DesignSystem.SubscriptionPage.ContentSection.priceToButtonSpacing
                }
                
                static var buttonToAgreementSpacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.ContentSection.buttonToAgreementSpacing :
                        DesignSystem.SubscriptionPage.ContentSection.buttonToAgreementSpacing
                }
                
                static var maxContentWidth: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.ContentSection.maxContentWidth :
                        .infinity
                }
            }
            
            // MARK: - 功能列表自适应配置
            struct FeatureList {
                static var iconSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.FeatureList.iconSize :
                        DesignSystem.SubscriptionPage.FeatureList.iconSize
                }
                
                static var fontSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.FeatureList.fontSize :
                        DesignSystem.SubscriptionPage.FeatureList.fontSize
                }
                
                static var spacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.FeatureList.spacing :
                        DesignSystem.SubscriptionPage.FeatureList.spacing
                }
                
                static var verticalSpacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.FeatureList.verticalSpacing :
                        DesignSystem.SubscriptionPage.FeatureList.verticalSpacing
                }
            }
            
            // MARK: - 价格卡片自适应配置
            struct PriceCard {
                static var width: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.PriceCard.width :
                        DesignSystem.SubscriptionPage.PriceCard.width
                }
                
                static var height: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.PriceCard.height :
                        DesignSystem.SubscriptionPage.PriceCard.height
                }
                
                static var cornerRadius: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.PriceCard.cornerRadius :
                        DesignSystem.SubscriptionPage.PriceCard.cornerRadius
                }
                
                static var spacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.PriceCard.spacing :
                        DesignSystem.SubscriptionPage.PriceCard.spacing
                }
                
                static var titleFont: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.PriceCard.titleFont :
                        DesignSystem.SubscriptionPage.PriceCard.titleFont
                }
                
                static var priceFont: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.PriceCard.priceFont :
                        DesignSystem.SubscriptionPage.PriceCard.priceFont
                }
                
                static var unitFont: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.PriceCard.unitFont :
                        DesignSystem.SubscriptionPage.PriceCard.unitFont
                }
            }
            
            // MARK: - 订阅按钮自适应配置
            struct SubscribeButton {
                static var height: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.SubscribeButton.height :
                        DesignSystem.SubscriptionPage.SubscribeButton.height
                }
                
                static var cornerRadius: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.SubscribeButton.cornerRadius :
                        DesignSystem.SubscriptionPage.SubscribeButton.cornerRadius
                }
                
                static var fontSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.SubscribeButton.fontSize :
                        DesignSystem.SubscriptionPage.SubscribeButton.fontSize
                }
                
                static var maxWidth: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.SubscribeButton.maxWidth :
                        .infinity
                }
            }
            
            // MARK: - 协议区域自适应配置
            struct AgreementSection {
                static var fontSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.AgreementSection.fontSize :
                        DesignSystem.SubscriptionPage.AgreementSection.fontSize
                }
                
                static var checkboxSize: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.AgreementSection.checkboxSize :
                        DesignSystem.SubscriptionPage.AgreementSection.checkboxSize
                }
                
                static var spacing: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.AgreementSection.spacing :
                        DesignSystem.SubscriptionPage.AgreementSection.spacing
                }
            }
            
            // MARK: - 装饰元素自适应配置
            struct DecorationElements {
                static var circle1Size: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle1Size :
                        DesignSystem.SubscriptionPage.DecorationElements.circle1Size
                }
                
                static var circle1Position: (x: CGFloat, y: CGFloat) {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle1Position :
                        DesignSystem.SubscriptionPage.DecorationElements.circle1Position
                }
                
                static var circle2Size: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle2Size :
                        DesignSystem.SubscriptionPage.DecorationElements.circle2Size
                }
                
                static var circle2Position: (x: CGFloat, y: CGFloat) {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle2Position :
                        DesignSystem.SubscriptionPage.DecorationElements.circle2Position
                }
                
                static var circle3Size: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle3Size :
                        DesignSystem.SubscriptionPage.DecorationElements.circle3Size
                }
                
                static var circle3Position: (x: CGFloat, y: CGFloat) {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle3Position :
                        DesignSystem.SubscriptionPage.DecorationElements.circle3Position
                }
                
                static var circle4Size: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle4Size :
                        DesignSystem.SubscriptionPage.DecorationElements.circle4Size
                }
                
                static var circle4Position: (x: CGFloat, y: CGFloat) {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle4Position :
                        DesignSystem.SubscriptionPage.DecorationElements.circle4Position
                }
                
                static var circle5Size: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle5Size :
                        DesignSystem.SubscriptionPage.DecorationElements.circle5Size
                }
                
                static var circle5Position: (x: CGFloat, y: CGFloat) {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.DecorationElements.circle5Position :
                        DesignSystem.SubscriptionPage.DecorationElements.circle5Position
                }
            }
            
            // MARK: - 背景图片自适应配置
            struct BackgroundImage {
                static var offsetX: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.BackgroundImage.offsetX :
                        DesignSystem.SubscriptionPage.BackgroundImage.offsetX
                }
                
                static var offsetY: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.BackgroundImage.offsetY :
                        DesignSystem.SubscriptionPage.BackgroundImage.offsetY
                }
                
                static var scaleX: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.BackgroundImage.scaleX :
                        DesignSystem.SubscriptionPage.BackgroundImage.scaleX
                }
                
                static var scaleY: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.BackgroundImage.scaleY :
                        DesignSystem.SubscriptionPage.BackgroundImage.scaleY
                }
                
                static var opacity: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.BackgroundImage.opacity :
                        DesignSystem.SubscriptionPage.BackgroundImage.opacity
                }
                
                static var blur: CGFloat {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.BackgroundImage.blur :
                        DesignSystem.SubscriptionPage.BackgroundImage.blur
                }
                
                // 其他属性直接使用原始配置
                static var uniformScale: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.uniformScale
                }
                
                static var alignmentMode: DesignSystem.SubscriptionPage.BackgroundImage.AlignmentMode {
                    return DesignSystem.SubscriptionPage.BackgroundImage.alignmentMode
                }
                
                static var animationDuration: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.animationDuration
                }
                
                static var brightness: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.brightness
                }
                
                static var contrast: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.contrast
                }
                
                static var zIndex: Double {
                    return DesignSystem.SubscriptionPage.BackgroundImage.zIndex
                }
                
                static var cropTop: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropTop
                }
                
                static var cropBottom: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropBottom
                }
                
                static var cropLeft: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropLeft
                }
                
                static var cropRight: CGFloat {
                    return DesignSystem.SubscriptionPage.BackgroundImage.cropRight
                }
            }
            
            // MARK: - 动画预设自适应配置
            struct AnimationPresets {
                static var springResponse: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.AnimationPresets.springResponse :
                        DesignSystem.SubscriptionPage.AnimationPresets.springResponse
                }
                
                static var springDamping: Double {
                    return DeviceDetection.isPad ? 
                        iPadLayoutConfig.SubscriptionPage.AnimationPresets.springDamping :
                        DesignSystem.SubscriptionPage.AnimationPresets.springDamping
                }
                
                struct UserInfo {
                    static var delay: Double {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.UserInfo.delay :
                            DesignSystem.SubscriptionPage.AnimationPresets.UserInfo.delay
                    }
                    
                    static var duration: Double {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.UserInfo.duration :
                            DesignSystem.SubscriptionPage.AnimationPresets.UserInfo.duration
                    }
                    
                    static var offsetY: CGFloat {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.UserInfo.offsetY :
                            DesignSystem.SubscriptionPage.AnimationPresets.UserInfo.offsetY
                    }
                    
                    static var scaleStart: CGFloat {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.UserInfo.scaleStart :
                            DesignSystem.SubscriptionPage.AnimationPresets.UserInfo.scaleStart
                    }
                    
                    static var scaleEnd: CGFloat {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.UserInfo.scaleEnd :
                            DesignSystem.SubscriptionPage.AnimationPresets.UserInfo.scaleEnd
                    }
                }
                
                struct Content {
                    static var delay: Double {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Content.delay :
                            DesignSystem.SubscriptionPage.AnimationPresets.Content.delay
                    }
                    
                    static var duration: Double {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Content.duration :
                            DesignSystem.SubscriptionPage.AnimationPresets.Content.duration
                    }
                    
                    static var offsetY: CGFloat {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Content.offsetY :
                            DesignSystem.SubscriptionPage.AnimationPresets.Content.offsetY
                    }
                    
                    static var staggerInterval: Double {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Content.staggerInterval :
                            DesignSystem.SubscriptionPage.AnimationPresets.Content.staggerInterval
                    }
                }
                
                struct Tab {
                    static var delay: Double {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Tab.delay :
                            DesignSystem.SubscriptionPage.AnimationPresets.Tab.delay
                    }
                    
                    static var duration: Double {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Tab.duration :
                            DesignSystem.SubscriptionPage.AnimationPresets.Tab.duration
                    }
                    
                    static var scaleStart: CGFloat {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Tab.scaleStart :
                            DesignSystem.SubscriptionPage.AnimationPresets.Tab.scaleStart
                    }
                    
                    static var scaleEnd: CGFloat {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Tab.scaleEnd :
                            DesignSystem.SubscriptionPage.AnimationPresets.Tab.scaleEnd
                    }
                    
                    static var offsetY: CGFloat {
                        return DeviceDetection.isPad ? 
                            iPadLayoutConfig.SubscriptionPage.AnimationPresets.Tab.offsetY :
                            DesignSystem.SubscriptionPage.AnimationPresets.Tab.offsetY
                    }
                }
            }
        }
    }
}