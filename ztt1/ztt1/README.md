# 转团团 - 家长版应用开发项目

## 项目概述

**转团团**是基于教师版应用代码基础开发的家长版iOS应用，旨在帮助家庭成员通过可视化积分与成长记录系统，鼓励孩子良好行为，同时利用AI分析工具生成个性化的行为与成长报告，提升家庭教育质量。

### 基本信息
- **项目名称**：转团团（家长版）
- **基础代码**：教师版应用 (`tuantuanzhuan/`)
- **目标平台**：iOS 15.6+
- **开发语言**：Swift + SwiftUI
- **架构模式**：MVVM

## 技术架构

### 核心技术栈
- **UI框架**：SwiftUI（声明式UI）
- **数据存储**：CoreData + CloudKit同步
- **认证系统**：Apple ID登录
- **订阅管理**：RevenueCat
- **AI服务**：DeepSeek API
- **本地化**：中英文支持

### 架构设计
```
转团团家长版
├── 数据层 (CoreData + CloudKit)
├── 服务层 (Services)
│   └── 订阅服务 (RevenueCatManager)
├── 视图模型层 (ViewModels)
└── 视图层 (SwiftUI Views)
```

## 功能需求

### 页面结构（底部三大Tab）

#### 1. 首页（家庭成员管理与积分操作）
- 顶部区域：添加成员按钮
- 全员积分操作按钮（加分/扣分）
- 全家总积分统计
- 成员卡片展示（矩形双列展示）

#### 2. 成员详情页
- 展示角色、姓名、当前积分总数、系统头像
- 积分记录列表和兑换记录
- 加分与扣分按钮
- AI分析按钮（孩子角色专享）

#### 3. 成长日记页
- 输入框：填写日记内容
- 时间选择器：设置记录时间
- 保存按钮：存入日记记录
- 查看日记按钮：查看所有日记

### 会员订阅体系

| 功能 | 免费用户 | 初级会员 | 高级会员 |
|------|----------|----------|----------|
| 家庭成员管理 | ✅ | ✅ | ✅ |
| 积分管理 | ✅ | ✅ | ✅ |
| 大转盘 | ❌ | ✅ | ✅ |
| 成长日记功能 | ✅ | ✅ | ✅ |
| 盲盒、刮刮卡 | ❌ | ❌ | ✅ |
| AI分析功能 | ❌ | ❌ | ✅ |
| 多设备同步 |  ✅  | ✅  | ✅  |

**价格体系**：
- 初级会员：38元/月，240元/年
- 高级会员：58元/月，388元/年
- 新用户福利：1个月高级会员试用

```

## 数据模型设计

### 核心实体关系与数据模型
```
User（用户）
└── FamilyMember（家庭成员）
       ├── FamilyRole（角色）
       ├── Name (名称)
       ├── Number (编号)
       ├── Points (积分)
       ├── PointRecord（积分记录）
       ├── Age (年龄)
       ├── Avatar (头像)
       ├── AddPointsRules (加分规则)
       ├── SubtractPointsRules (扣分规则)
       ├── RedemptionRecord（兑换记录）
       ├── LotteryRecord（抽奖记录）
       ├── AIReport（AI分析报告）
       ├── Prize (奖品)
       ├── LotteryToolConfig (大转盘配置)
       ├── BlindBoxConfig （盲盒配置）
       ├── ScratchCardConfig （刮刮卡配置）
       └── DiaryEntry（成长日记）

```


## 开发环境

### 项目结构
```
zhuantuantuan/
├── Models/                  # 数据模型
├── Views/                   # SwiftUI视图
├── ViewModels/             # 视图模型
├── Services/               # 业务服务
├── Managers/               # 管理器类
├── Extensions/             # 扩展功能
├── Styles/                 # 样式系统
├── Utils/                  # 工具类
├── Resources/              # 资源文件
│   ├── zh-Hans.lproj/      # 中文本地化
│   └── en.lproj/           # 英文本地化
└── Documentation/          # 文档
```

### 开发工具
- **IDE**: Xcode 15.0+
- **iOS版本**: iOS 15.6+
- **Swift版本**: Swift 5.9+
- **依赖管理**: Swift Package Manager

## 项目特色

### 与教师版的差异
| 方面 | 教师版 | 家长版 |
|------|--------|--------|
| 用户角色 | 教师管理班级学生 | 父母管理家庭成员 |
| 主要对象 | 班级(Class) → 学生(Student) | 用户(User) → 成员(Member) |
| 积分操作 | 教师给学生加扣分 | 父母给家庭成员加扣分 |
| 特色功能 | 抽奖系统、AI分析报告 | 成长日记、AI分析报告 |
| 权限管理 | 单一教师权限 | 单一父母权限 |

### 保留的设计元素
- UI/UX设计风格完全保持一致
- 技术架构和组件复用
- 设计系统和动画效果
- 本地化字符串管理
- 订阅和权限管理框架

## 开发状态

当前项目处于需求分析和架构设计阶段，准备开始基础架构搭建工作。
基础UI已完成

---
