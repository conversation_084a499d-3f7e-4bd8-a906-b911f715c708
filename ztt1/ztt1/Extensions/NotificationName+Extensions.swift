//
//  NotificationName+Extensions.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/17.
//

import Foundation

/**
 * 应用内通知名称扩展
 * 统一管理所有自定义通知名称
 */
extension Notification.Name {
    
    // MARK: - 学生数据更新通知
    
    /**
     * 学生积分数据变更通知
     * 当学生的积分发生变化时发送此通知
     * userInfo包含：
     * - "studentId": String - 学生ID
     * - "pointsChange": Int - 积分变化量（正数为加分，负数为扣分）
     * - "reason": String - 变更原因
     */
    static let studentPointsDidChange = Notification.Name("StudentPointsDidChange")
    
    /**
     * 学生数据批量更新通知
     * 当多个学生数据同时更新时发送此通知
     * userInfo包含：
     * - "classId": String - 班级ID
     * - "updateType": String - 更新类型（"points", "redemption", "lottery"等）
     */
    static let studentDataDidBatchUpdate = Notification.Name("StudentDataDidBatchUpdate")
    
    /**
     * 班级统计需要刷新通知
     * 当班级相关统计数据需要重新计算时发送此通知
     * userInfo包含：
     * - "classId": String - 班级ID
     * - "triggerSource": String - 触发来源（"student_detail", "class_operation"等）
     */
    static let classStatisticsNeedsRefresh = Notification.Name("ClassStatisticsNeedsRefresh")
    
    // MARK: - 订阅状态更新通知
    
    /**
     * 订阅状态变更通知
     * 当用户的订阅状态发生变化时发送此通知
     * userInfo包含：
     * - "userId": String - 用户ID
     * - "newLevel": String - 新的订阅级别 ("free", "basic", "premium")
     * - "oldLevel": String? - 旧的订阅级别 (可能为空)
     */
    static let subscriptionStatusChanged = Notification.Name("SubscriptionStatusChanged")
    
    /**
     * 订阅购买成功通知
     * 当用户成功完成订阅购买时发送此通知
     * userInfo包含：
     * - "productId": String - 产品ID
     * - "isInitialPurchase": Bool - 是否为首次购买
     */
    static let subscriptionPurchaseSucceeded = Notification.Name("SubscriptionPurchaseSucceeded")
    
    /**
     * 订阅恢复成功通知
     * 当用户成功恢复之前的订阅时发送此通知
     */
    static let subscriptionRestoreSucceeded = Notification.Name("SubscriptionRestoreSucceeded")
    
    /**
     * 订阅过期通知
     * 当用户的订阅过期时发送此通知
     * userInfo包含：
     * - "expiredLevel": String - 过期的订阅级别
     */
    static let subscriptionDidExpire = Notification.Name("SubscriptionDidExpire")
    
    /**
     * 会员降级需要处理班级冻结通知
     * 当会员降级且活跃班级数超出限制时发送此通知
     * userInfo包含：
     * - "userId": String - 用户ID
     * - "oldLevel": String - 旧的订阅级别
     * - "newLevel": String - 新的订阅级别
     */
    static let subscriptionDowngradeNeedsAction = Notification.Name("SubscriptionDowngradeNeedsAction")
    
    /**
     * 会员升级可解冻班级通知
     * 当会员升级且有冻结班级可以解冻时发送此通知
     * userInfo包含：
     * - "userId": String - 用户ID
     * - "oldLevel": String - 旧的订阅级别
     * - "newLevel": String - 新的订阅级别
     * - "availableUnfreezeCount": Int - 可解冻的班级数量
     */
    static let subscriptionUpgradeClassUnfreeze = Notification.Name("SubscriptionUpgradeClassUnfreeze")
    
    /**
     * 班级状态变更通知
     * 当班级被冻结或解冻时发送此通知
     * userInfo包含：
     * - "classId": String - 班级ID
     * - "className": String - 班级名称
     * - "newStatus": String - 新状态 ("active", "frozen")
     * - "oldStatus": String - 旧状态
     */
    static let classStatusChanged = Notification.Name("ClassStatusChanged")
    
    /**
     * 显示订阅标签页通知
     * 用于从其他页面导航到订阅页面
     */
    static let showSubscriptionTab = Notification.Name("ShowSubscriptionTab")
}

/**
 * 通知UserInfo键名常量
 */
struct NotificationUserInfoKey {
    static let studentId = "studentId"
    static let pointsChange = "pointsChange"
    static let reason = "reason"
    static let classId = "classId"
    static let updateType = "updateType"
    static let triggerSource = "triggerSource"
    
    // 订阅相关键名
    static let userId = "userId"
    static let newLevel = "newLevel"
    static let oldLevel = "oldLevel"
    static let productId = "productId"
    static let isInitialPurchase = "isInitialPurchase"
    static let expiredLevel = "expiredLevel"
    static let availableUnfreezeCount = "availableUnfreezeCount"
    
    // 班级状态相关键名
    static let className = "className"
    static let newStatus = "newStatus"
    static let oldStatus = "oldStatus"
} 