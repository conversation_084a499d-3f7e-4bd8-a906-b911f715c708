import SwiftUI

/**
 * View扩展 - 提供隐藏键盘功能
 */
extension View {
    /**
     * 隐藏键盘
     * 在任何SwiftUI视图中方便地调用此方法来隐藏键盘
     */
    func hideKeyboard() {
        UIApplication.shared.hideKeyboard()
    }
    
    /**
     * 点击后隐藏键盘
     * 为视图添加点击手势，点击时自动隐藏键盘
     */
    func dismissKeyboardOnTap() -> some View {
        self.contentShape(Rectangle())
            .onTapGesture {
                hideKeyboard()
            }
    }
} 