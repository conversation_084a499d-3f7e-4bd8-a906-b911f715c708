//
//  FamilyPermissionManager.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/26.
//

import Foundation

/**
 * 家庭权限管理器
 * 负责处理家庭成员的权限检查和访问控制
 */
@MainActor
class FamilyPermissionManager: ObservableObject {
    
    static let shared = FamilyPermissionManager()
    
    private init() {}
    
    // MARK: - 权限检查方法
    
    /**
     * 检查成员是否可以编辑指定内容
     */
    func canEdit(
        member: FamilyMember?,
        contentType: FamilyContentType,
        targetMember: FamilyMember? = nil
    ) -> Bool {
        guard let member = member else { return false }
        
        let role = FamilyRole(rawValue: member.role ?? "other") ?? .other
        
        // 检查基本权限
        guard role.editableContentTypes.contains(contentType) else {
            return false
        }
        
        // 特殊情况处理
        switch contentType {
        case .ownProfile:
            // 只能编辑自己的资料
            return member.id == targetMember?.id
            
        case .ownDiary:
            // 只能编辑自己的日记，或者父母可以编辑所有日记
            if role.isParent {
                return true
            }
            return member.id == targetMember?.id
            
        case .familyMembers:
            // 只有父母可以管理家庭成员
            return role.isParent
            
        case .pointRecords:
            // 只有父母可以编辑积分记录
            return role.isParent
            
        case .familyRules, .familyPrizes:
            // 只有父母可以管理家庭规则和奖品
            return role.isParent
            
        case .diaryEntries:
            // 父母可以编辑所有日记，成员只能编辑自己的
            if role.isParent {
                return true
            }
            return member.id == targetMember?.id
            
        case .aiReports:
            // 只有父母可以删除AI报告
            return role.isParent
        }
    }
    
    /**
     * 检查成员是否可以查看指定内容
     */
    func canView(
        member: FamilyMember?,
        contentType: FamilyContentType,
        targetMember: FamilyMember? = nil
    ) -> Bool {
        guard let member = member else { return false }
        
        let role = FamilyRole(rawValue: member.role ?? "other") ?? .other
        
        // 检查基本权限
        guard role.viewableContentTypes.contains(contentType) else {
            return false
        }
        
        // 特殊情况处理
        switch contentType {
        case .ownProfile:
            // 只能查看自己的详细资料
            return member.id == targetMember?.id
            
        case .ownDiary:
            // 只能查看自己的私人日记，或者父母可以查看所有
            if role.isParent {
                return true
            }
            return member.id == targetMember?.id
            
        default:
            // 其他内容按基本权限处理
            return true
        }
    }
    
    /**
     * 检查成员是否可以删除指定内容
     */
    func canDelete(
        member: FamilyMember?,
        contentType: FamilyContentType,
        targetMember: FamilyMember? = nil
    ) -> Bool {
        guard let member = member else { return false }
        
        let role = FamilyRole(rawValue: member.role ?? "other") ?? .other
        
        switch contentType {
        case .familyMembers:
            // 只有父母可以删除家庭成员，但不能删除自己
            return role.isParent && member.id != targetMember?.id
            
        case .pointRecords:
            // 只有父母可以删除积分记录
            return role.isParent
            
        case .diaryEntries:
            // 父母可以删除所有日记，成员只能删除自己的
            if role.isParent {
                return true
            }
            return member.id == targetMember?.id
            
        case .aiReports:
            // 父母可以删除所有AI报告，成员只能删除自己的
            if role.isParent {
                return true
            }
            return member.id == targetMember?.id
            
        case .familyRules, .familyPrizes:
            // 只有父母可以删除家庭规则和奖品
            return role.isParent
            
        default:
            return false
        }
    }
    
    /**
     * 检查成员是否可以创建指定内容
     */
    func canCreate(
        member: FamilyMember?,
        contentType: FamilyContentType
    ) -> Bool {
        guard let member = member else { return false }
        
        let role = FamilyRole(rawValue: member.role ?? "other") ?? .other
        
        switch contentType {
        case .familyMembers:
            // 只有父母可以添加家庭成员
            return role.isParent
            
        case .pointRecords:
            // 只有父母可以创建积分记录
            return role.isParent
            
        case .diaryEntries:
            // 所有成员都可以创建日记
            return true
            
        case .familyRules, .familyPrizes:
            // 只有父母可以创建家庭规则和奖品
            return role.isParent
            
        case .aiReports:
            // AI报告由系统自动创建，不允许手动创建
            return false
            
        default:
            return false
        }
    }
    
    // MARK: - 角色管理方法
    
    /**
     * 检查角色是否可以在家庭中添加
     */
    func canAddRole(
        _ role: FamilyRole,
        to family: Family,
        by currentMember: FamilyMember?
    ) -> Bool {
        // 检查当前成员是否有权限添加成员
        guard canCreate(member: currentMember, contentType: .familyMembers) else {
            return false
        }
        
        // 检查角色唯一性
        if role.isUnique {
            return !family.hasRole(role.rawValue)
        }
        
        return true
    }
    
    /**
     * 获取成员可以执行的操作列表
     */
    func getAvailableActions(
        for member: FamilyMember?,
        on contentType: FamilyContentType,
        targetMember: FamilyMember? = nil
    ) -> Set<FamilyAction> {
        var actions: Set<FamilyAction> = []
        
        if canView(member: member, contentType: contentType, targetMember: targetMember) {
            actions.insert(.view)
        }
        
        if canEdit(member: member, contentType: contentType, targetMember: targetMember) {
            actions.insert(.edit)
        }
        
        if canDelete(member: member, contentType: contentType, targetMember: targetMember) {
            actions.insert(.delete)
        }
        
        if canCreate(member: member, contentType: contentType) {
            actions.insert(.create)
        }
        
        return actions
    }
    
    /**
     * 检查成员是否为家庭管理员
     */
    func isAdmin(_ member: FamilyMember?) -> Bool {
        guard let member = member else { return false }
        let role = FamilyRole(rawValue: member.role ?? "other") ?? .other
        return role.permissionLevel == .admin
    }
    
    /**
     * 获取权限错误消息
     */
    func getPermissionErrorMessage(
        for action: FamilyAction,
        contentType: FamilyContentType,
        memberRole: FamilyRole
    ) -> String {
        switch action {
        case .view:
            return "family.permission.error.view".localized
        case .edit:
            return "family.permission.error.edit".localized
        case .delete:
            return "family.permission.error.delete".localized
        case .create:
            return "family.permission.error.create".localized
        }
    }
}

/**
 * 家庭操作类型
 */
enum FamilyAction: String, CaseIterable {
    case view = "view"
    case edit = "edit"
    case delete = "delete"
    case create = "create"
    
    var displayName: String {
        switch self {
        case .view:
            return "family.action.view".localized
        case .edit:
            return "family.action.edit".localized
        case .delete:
            return "family.action.delete".localized
        case .create:
            return "family.action.create".localized
        }
    }
    
    var iconName: String {
        switch self {
        case .view:
            return "eye.fill"
        case .edit:
            return "pencil"
        case .delete:
            return "trash.fill"
        case .create:
            return "plus.circle.fill"
        }
    }
}
