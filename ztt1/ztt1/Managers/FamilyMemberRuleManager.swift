//
//  FamilyMemberRuleManager.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/28.
//

import Foundation
import CoreData

/**
 * 家庭成员规则管理器
 *
 * ## 职责
 * 负责管理家庭成员的积分规则，提供完整的CRUD操作和业务逻辑
 *
 * ## 核心功能
 * - 规则创建：支持单个和批量创建规则
 * - 规则删除：安全删除规则，包含依赖检查
 * - 规则应用：应用规则并创建积分记录
 * - 数据统计：提供规则使用统计信息
 * - 错误处理：完整的错误分类和处理机制
 *
 * ## 数据一致性
 * - 所有操作都在CoreData事务中执行
 * - 支持CloudKit自动同步
 * - 提供数据验证和冲突解决
 *
 * ## 线程安全
 * 所有公开方法都是线程安全的，内部使用主队列执行CoreData操作
 */
class FamilyMemberRuleManager: ObservableObject {
    
    // MARK: - Properties
    
    private let coreDataManager: CoreDataManager
    
    // MARK: - Initialization
    
    init(coreDataManager: CoreDataManager = CoreDataManager.shared) {
        self.coreDataManager = coreDataManager
    }
    
    // MARK: - Rule Validation
    
    /**
     * 验证规则数据
     */
    func validateRule(name: String, value: Int32, type: String) -> RuleValidationResult {
        var errors: [String] = []
        
        // 验证规则名称
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedName.isEmpty {
            errors.append("规则名称不能为空")
        } else if trimmedName.count > 20 {
            errors.append("规则名称不能超过20个字符")
        }
        
        // 验证分值
        if value <= 0 {
            errors.append("分值必须大于0")
        } else if value > 100 {
            errors.append("分值不能超过100")
        }
        
        // 验证类型
        if !["add", "deduct"].contains(type) {
            errors.append("规则类型无效")
        }
        
        return RuleValidationResult(
            isValid: errors.isEmpty,
            errors: errors,
            validatedName: trimmedName,
            validatedValue: value,
            validatedType: type
        )
    }
    
    /**
     * 批量验证规则数据
     */
    func validateRules(_ rulesData: [(name: String, value: Int32, type: String)]) -> BatchRuleValidationResult {
        var validRules: [(name: String, value: Int32, type: String)] = []
        var errors: [String] = []
        
        for (index, ruleData) in rulesData.enumerated() {
            let validation = validateRule(name: ruleData.name, value: ruleData.value, type: ruleData.type)
            
            if validation.isValid {
                validRules.append((
                    name: validation.validatedName,
                    value: validation.validatedValue,
                    type: validation.validatedType
                ))
            } else {
                for error in validation.errors {
                    errors.append("规则\(index + 1): \(error)")
                }
            }
        }
        
        return BatchRuleValidationResult(
            isValid: errors.isEmpty,
            errors: errors,
            validRules: validRules
        )
    }
    
    // MARK: - Rule Management
    
    /**
     * 创建单个规则
     */
    @discardableResult
    func createRule(
        for member: FamilyMember,
        name: String,
        value: Int32,
        type: String,
        isFrequent: Bool = false
    ) -> Result<FamilyMemberRule, RuleManagerError> {
        
        // 验证规则数据
        let validation = validateRule(name: name, value: value, type: type)
        guard validation.isValid else {
            return .failure(.validationFailed(validation.errors))
        }
        
        // 检查重复规则
        if hasExistingRule(for: member, name: validation.validatedName, type: validation.validatedType) {
            return .failure(.duplicateRule("已存在同名的\(type == "add" ? "加分" : "扣分")规则"))
        }
        
        // 创建规则
        let rule = coreDataManager.addFamilyMemberRule(
            to: member,
            name: validation.validatedName,
            value: validation.validatedValue,
            type: validation.validatedType,
            isFrequent: isFrequent
        )
        
        return .success(rule)
    }
    
    /**
     * 批量创建规则
     */
    func createRules(
        for member: FamilyMember,
        rulesData: [(name: String, value: Int32, type: String, isFrequent: Bool)]
    ) -> BatchRuleCreationResult {
        
        let validation = validateRules(rulesData.map { (name: $0.name, value: $0.value, type: $0.type) })
        
        guard validation.isValid else {
            return BatchRuleCreationResult(
                success: false,
                createdRules: [],
                errors: validation.errors
            )
        }
        
        var createdRules: [FamilyMemberRule] = []
        var errors: [String] = []
        
        for (index, ruleData) in rulesData.enumerated() {
            let result = createRule(
                for: member,
                name: ruleData.name,
                value: ruleData.value,
                type: ruleData.type,
                isFrequent: ruleData.isFrequent
            )
            
            switch result {
            case .success(let rule):
                createdRules.append(rule)
            case .failure(let error):
                errors.append("规则\(index + 1): \(error.localizedDescription)")
            }
        }
        
        return BatchRuleCreationResult(
            success: errors.isEmpty,
            createdRules: createdRules,
            errors: errors
        )
    }
    
    /**
     * 删除规则
     */
    func deleteRule(_ rule: FamilyMemberRule) -> Result<Void, RuleManagerError> {
        guard rule.member != nil else {
            return .failure(.invalidRule("规则没有关联的成员"))
        }
        
        // 检查权限（这里简化处理，实际应用中可以添加更复杂的权限检查）
        coreDataManager.deleteFamilyMemberRule(rule)
        return .success(())
    }
    
    /**
     * 更新规则
     */
    func updateRule(
        _ rule: FamilyMemberRule,
        name: String,
        value: Int32,
        type: String
    ) -> Result<Void, RuleManagerError> {
        
        // 验证规则数据
        let validation = validateRule(name: name, value: value, type: type)
        guard validation.isValid else {
            return .failure(.validationFailed(validation.errors))
        }
        
        // 检查重复规则（排除当前规则）
        if let member = rule.member,
           hasExistingRule(for: member, name: validation.validatedName, type: validation.validatedType, excluding: rule) {
            return .failure(.duplicateRule("已存在同名的\(type == "add" ? "加分" : "扣分")规则"))
        }
        
        // 更新规则
        coreDataManager.updateFamilyMemberRule(
            rule,
            name: validation.validatedName,
            value: validation.validatedValue,
            type: validation.validatedType
        )
        
        return .success(())
    }
    
    /**
     * 应用规则到成员
     */
    @discardableResult
    func applyRule(_ rule: FamilyMemberRule) -> Result<FamilyPointRecord, RuleManagerError> {
        guard let record = coreDataManager.applyMemberRule(rule) else {
            return .failure(.applicationFailed("规则应用失败"))
        }
        
        return .success(record)
    }
    
    /**
     * 切换规则的常用状态
     */
    func toggleRuleFrequent(_ rule: FamilyMemberRule) {
        coreDataManager.toggleFamilyMemberRuleFrequent(rule)
    }
    
    // MARK: - Rule Queries
    
    /**
     * 获取成员的常用规则
     */
    func getFrequentRules(for member: FamilyMember, type: String) -> [FamilyMemberRule] {
        return coreDataManager.getFrequentMemberRules(for: member, type: type)
    }
    
    /**
     * 获取成员的所有规则
     */
    func getAllRules(for member: FamilyMember) -> [FamilyMemberRule] {
        return coreDataManager.getAllMemberRules(for: member)
    }

    // MARK: - Default Rules Management

    /**
     * 为新成员创建默认规则
     */
    func createDefaultRules(for member: FamilyMember) -> BatchRuleCreationResult {
        let defaultRulesData = [
            (name: "完成作业", value: Int32(5), type: "add", isFrequent: true),
            (name: "帮助家务", value: Int32(3), type: "add", isFrequent: true),
            (name: "主动学习", value: Int32(4), type: "add", isFrequent: true),
            (name: "礼貌待人", value: Int32(2), type: "add", isFrequent: true),
            (name: "按时睡觉", value: Int32(2), type: "add", isFrequent: true),
            (name: "迟到", value: Int32(3), type: "deduct", isFrequent: true),
            (name: "不完成作业", value: Int32(5), type: "deduct", isFrequent: true),
            (name: "说谎", value: Int32(4), type: "deduct", isFrequent: true),
            (name: "不听话", value: Int32(2), type: "deduct", isFrequent: true),
            (name: "浪费食物", value: Int32(3), type: "deduct", isFrequent: true)
        ]

        return createRules(for: member, rulesData: defaultRulesData)
    }

    /**
     * 检查成员是否需要默认规则
     */
    func shouldCreateDefaultRules(for member: FamilyMember) -> Bool {
        return getAllRules(for: member).isEmpty
    }

    // MARK: - Rule Statistics

    /**
     * 获取成员规则统计信息
     */
    func getRuleStatistics(for member: FamilyMember) -> MemberRuleStatistics {
        let allRules = getAllRules(for: member)
        let addRules = allRules.filter { $0.ruleType == .add }
        let deductRules = allRules.filter { $0.ruleType == .deduct }
        let frequentRules = allRules.filter { $0.isFrequent }

        return MemberRuleStatistics(
            totalRules: allRules.count,
            addRules: addRules.count,
            deductRules: deductRules.count,
            frequentRules: frequentRules.count
        )
    }

    // MARK: - Helper Methods
    
    /**
     * 检查是否存在重复规则
     */
    private func hasExistingRule(
        for member: FamilyMember,
        name: String,
        type: String,
        excluding excludeRule: FamilyMemberRule? = nil
    ) -> Bool {
        let existingRules = getAllRules(for: member)
        
        return existingRules.contains { rule in
            if let excludeRule = excludeRule, rule.id == excludeRule.id {
                return false
            }
            return rule.name?.lowercased() == name.lowercased() && rule.type == type
        }
    }
}

// MARK: - Data Structures

/**
 * 规则验证结果
 */
struct RuleValidationResult {
    let isValid: Bool
    let errors: [String]
    let validatedName: String
    let validatedValue: Int32
    let validatedType: String
}

/**
 * 批量规则验证结果
 */
struct BatchRuleValidationResult {
    let isValid: Bool
    let errors: [String]
    let validRules: [(name: String, value: Int32, type: String)]
}

/**
 * 批量规则创建结果
 */
struct BatchRuleCreationResult {
    let success: Bool
    let createdRules: [FamilyMemberRule]
    let errors: [String]
}

/**
 * 成员规则统计信息
 */
struct MemberRuleStatistics {
    let totalRules: Int
    let addRules: Int
    let deductRules: Int
    let frequentRules: Int

    var hasRules: Bool {
        return totalRules > 0
    }

    var addRulesRatio: Double {
        guard totalRules > 0 else { return 0.0 }
        return Double(addRules) / Double(totalRules)
    }

    var deductRulesRatio: Double {
        guard totalRules > 0 else { return 0.0 }
        return Double(deductRules) / Double(totalRules)
    }

    var frequentRulesRatio: Double {
        guard totalRules > 0 else { return 0.0 }
        return Double(frequentRules) / Double(totalRules)
    }
}

/**
 * 规则管理器错误类型
 */
enum RuleManagerError: LocalizedError {
    case validationFailed([String])
    case duplicateRule(String)
    case invalidRule(String)
    case applicationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .validationFailed(let errors):
            return errors.joined(separator: ", ")
        case .duplicateRule(let message):
            return message
        case .invalidRule(let message):
            return message
        case .applicationFailed(let message):
            return message
        }
    }
}
