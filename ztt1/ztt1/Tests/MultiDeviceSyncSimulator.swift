//
//  MultiDeviceSyncSimulator.swift
//  ztt1
//
//  Created by AI Assistant on 2025/1/28.
//  Copyright © 2025 ztt1. All rights reserved.
//

import Foundation
import CoreData

/**
 * 多设备同步模拟器
 * 
 * 模拟多个设备之间的数据同步场景，验证：
 * - 数据一致性
 * - 冲突解决
 * - 网络异常处理
 * - 离线操作同步
 */
class MultiDeviceSyncSimulator {
    
    private let coreDataManager: CoreDataManager
    private let ruleManager: FamilyMemberRuleManager
    
    init(coreDataManager: CoreDataManager = CoreDataManager.shared) {
        self.coreDataManager = coreDataManager
        self.ruleManager = FamilyMemberRuleManager(coreDataManager: coreDataManager)
    }
    
    // MARK: - 同步场景测试
    
    /**
     * 模拟多设备同步场景
     */
    func simulateMultiDeviceSync() -> SyncTestResult {
        print("📱📱 开始多设备同步模拟...")
        
        var testResults: [String] = []
        var allPassed = true
        
        // 创建测试成员
        let family = coreDataManager.getDefaultFamily() ?? coreDataManager.createFamily(name: "同步测试家庭")
        let member = coreDataManager.createFamilyMember(
            name: "同步测试成员",
            role: "测试成员",
            gender: "未知",
            age: 10,
            initialPoints: 0,
            in: family
        )
        
        // 场景1: 同时创建规则
        let concurrentCreateResult = testConcurrentRuleCreation(member: member)
        testResults.append("并发创建规则: \(concurrentCreateResult ? "✅" : "❌")")
        allPassed = allPassed && concurrentCreateResult
        
        // 场景2: 离线操作同步
        let offlineOperationResult = testOfflineOperationSync(member: member)
        testResults.append("离线操作同步: \(offlineOperationResult ? "✅" : "❌")")
        allPassed = allPassed && offlineOperationResult
        
        // 场景3: 数据冲突解决
        let conflictResolutionResult = testConflictResolution(member: member)
        testResults.append("冲突解决: \(conflictResolutionResult ? "✅" : "❌")")
        allPassed = allPassed && conflictResolutionResult
        
        // 场景4: 网络恢复同步
        let networkRecoveryResult = testNetworkRecoverySync(member: member)
        testResults.append("网络恢复同步: \(networkRecoveryResult ? "✅" : "❌")")
        allPassed = allPassed && networkRecoveryResult
        
        // 清理测试数据
        coreDataManager.deleteFamilyMember(member)
        
        return SyncTestResult(
            allPassed: allPassed,
            testResults: testResults,
            summary: allPassed ? "所有同步测试通过" : "部分同步测试失败"
        )
    }
    
    /**
     * 测试并发规则创建
     * 模拟两个设备同时创建规则的情况
     */
    private func testConcurrentRuleCreation(member: FamilyMember) -> Bool {
        print("🔄 测试并发规则创建...")
        
        let initialCount = ruleManager.getAllRules(for: member).count
        
        // 模拟设备A创建规则
        let deviceAResult = ruleManager.createRule(
            for: member,
            name: "设备A规则",
            value: 5,
            type: "add",
            isFrequent: true
        )
        
        // 模拟设备B创建规则（几乎同时）
        let deviceBResult = ruleManager.createRule(
            for: member,
            name: "设备B规则",
            value: 3,
            type: "add",
            isFrequent: true
        )
        
        // 验证两个规则都创建成功
        guard case .success = deviceAResult,
              case .success = deviceBResult else {
            print("❌ 并发创建规则失败")
            return false
        }
        
        // 验证规则数量
        let finalCount = ruleManager.getAllRules(for: member).count
        let expectedCount = initialCount + 2
        
        if finalCount == expectedCount {
            print("✅ 并发规则创建测试通过")
            return true
        } else {
            print("❌ 规则数量不正确: 期望\(expectedCount)，实际\(finalCount)")
            return false
        }
    }
    
    /**
     * 测试离线操作同步
     * 模拟设备离线时的操作，然后恢复网络后的同步
     */
    private func testOfflineOperationSync(member: FamilyMember) -> Bool {
        print("📴 测试离线操作同步...")
        
        // 创建一个规则用于离线测试
        let createResult = ruleManager.createRule(
            for: member,
            name: "离线测试规则",
            value: 4,
            type: "add",
            isFrequent: false
        )
        
        guard case .success(let rule) = createResult else {
            print("❌ 离线测试规则创建失败")
            return false
        }
        
        let initialPoints = member.currentPoints
        
        // 模拟离线状态下应用规则
        let offlineApplyResult = ruleManager.applyRule(rule)
        
        guard case .success = offlineApplyResult else {
            print("❌ 离线规则应用失败")
            return false
        }
        
        // 验证本地数据更新
        let newPoints = member.currentPoints
        if newPoints == initialPoints + Int32(rule.value) {
            print("✅ 离线操作同步测试通过")
            return true
        } else {
            print("❌ 离线操作数据不一致")
            return false
        }
    }
    
    /**
     * 测试数据冲突解决
     * 模拟两个设备修改同一数据时的冲突处理
     */
    private func testConflictResolution(member: FamilyMember) -> Bool {
        print("⚔️ 测试数据冲突解决...")
        
        // 创建测试规则
        let createResult = ruleManager.createRule(
            for: member,
            name: "冲突测试规则",
            value: 6,
            type: "add",
            isFrequent: true
        )
        
        guard case .success(let rule) = createResult else {
            print("❌ 冲突测试规则创建失败")
            return false
        }
        
        // 模拟设备A应用规则
        let deviceAApply = ruleManager.applyRule(rule)
        
        // 模拟设备B也应用同一规则（可能的冲突场景）
        let deviceBApply = ruleManager.applyRule(rule)
        
        // 验证两次应用都成功（业务逻辑允许重复应用）
        guard case .success = deviceAApply,
              case .success = deviceBApply else {
            print("❌ 冲突场景处理失败")
            return false
        }
        
        print("✅ 数据冲突解决测试通过")
        return true
    }
    
    /**
     * 测试网络恢复同步
     * 模拟网络中断后恢复时的数据同步
     */
    private func testNetworkRecoverySync(member: FamilyMember) -> Bool {
        print("🌐 测试网络恢复同步...")
        
        let initialRuleCount = ruleManager.getAllRules(for: member).count
        
        // 模拟网络中断期间的操作
        let networkDownOperations = [
            (name: "网络中断规则1", value: Int32(2), type: "add", isFrequent: false),
            (name: "网络中断规则2", value: Int32(3), type: "deduct", isFrequent: false)
        ]
        
        let batchResult = ruleManager.createRules(for: member, rulesData: networkDownOperations)
        
        guard batchResult.success else {
            print("❌ 网络中断期间操作失败")
            return false
        }
        
        // 模拟网络恢复后的数据验证
        let finalRuleCount = ruleManager.getAllRules(for: member).count
        let expectedCount = initialRuleCount + networkDownOperations.count
        
        if finalRuleCount == expectedCount {
            print("✅ 网络恢复同步测试通过")
            return true
        } else {
            print("❌ 网络恢复后数据不一致")
            return false
        }
    }
    
    // MARK: - 性能基准测试
    
    /**
     * 运行同步性能基准测试
     */
    func runSyncPerformanceBenchmark() -> PerformanceResult {
        print("⚡ 运行同步性能基准测试...")
        
        let family = coreDataManager.getDefaultFamily() ?? coreDataManager.createFamily(name: "性能测试家庭")
        let member = coreDataManager.createFamilyMember(
            name: "性能测试成员",
            role: "测试成员",
            gender: "未知",
            age: 10,
            initialPoints: 0,
            in: family
        )

        // 测试大量规则创建的性能
        let startTime = Date()

        let largeRuleSet = (1...500).map { index -> (name: String, value: Int32, type: String, isFrequent: Bool) in
            let name = "性能测试规则\(index)"
            let value = Int32(index % 10 + 1)
            let type = index % 2 == 0 ? "add" : "deduct"
            let isFrequent = index % 5 == 0
            return (name: name, value: value, type: type, isFrequent: isFrequent)
        }
        
        let batchResult = ruleManager.createRules(for: member, rulesData: largeRuleSet)
        let creationDuration = Date().timeIntervalSince(startTime)
        
        // 测试规则查询性能
        let queryStartTime = Date()
        let _ = ruleManager.getAllRules(for: member)
        let queryDuration = Date().timeIntervalSince(queryStartTime)
        
        // 清理测试数据
        coreDataManager.deleteFamilyMember(member)
        
        return PerformanceResult(
            ruleCreationTime: creationDuration,
            ruleQueryTime: queryDuration,
            rulesCreated: batchResult.success ? largeRuleSet.count : 0,
            isAcceptable: creationDuration < 10.0 && queryDuration < 1.0
        )
    }
}

// MARK: - 测试结果数据结构

struct SyncTestResult {
    let allPassed: Bool
    let testResults: [String]
    let summary: String
}

struct PerformanceResult {
    let ruleCreationTime: TimeInterval
    let ruleQueryTime: TimeInterval
    let rulesCreated: Int
    let isAcceptable: Bool
    
    var summary: String {
        return """
        性能测试结果:
        - 规则创建: \(rulesCreated)条规则，耗时\(String(format: "%.2f", ruleCreationTime))秒
        - 规则查询: 耗时\(String(format: "%.3f", ruleQueryTime))秒
        - 性能评级: \(isAcceptable ? "优秀" : "需要优化")
        """
    }
}
