产品需求文档 V1.0
应用名称：转团团
应用目标：帮助家庭成员通过可视化积分与成长记录系统，鼓励孩子良好行为，同时利用 AI 分析工具生成个性化的行为与成长报告，提升家庭教育质量。

一、页面结构
1. 启动页 & 登录页

* 启动页（Splash）
  * 应用 Logo
  * 品牌标语（如“积分培养好习惯”）
  * 1\~2 秒自动跳转

* 登录页
  * Apple ID 登录按钮
  * 用户协议 & 隐私政策勾选
  * 登录后进入首页

2. 首页（家庭成员管理）
结构与功能：
* 添加成员按钮
    * 输入名称和初始积分
    * 选择角色（爸爸、妈妈、儿子、女儿、其他）
    * 输入出生日期，用于计算年龄
    * 系统根据角色自动分配头像
* 抽奖道具配置按钮
    * 点击后弹出已添加的成员列表
    * 选择成员后弹出道具选择界面
    * 选择道具后进入道具配置界面，可设置奖品内容和消耗积分
    * 配置完成后返回首页
* 全员操作按钮（加分/扣分）
    * 点击后弹出规则选择框（常用规则 + 自定义）
    * 操作后所有成员的积分记录中加入对应记录
* 全家总积分统计
    * 显示累计加分/扣分数据
    * 可选时间段筛选（如本周、本月、自定义时间范围）
* 成员卡片展示
    * 矩形双列展示
    * 显示：姓名、当前积分、系统头像、编号
    * 点击卡片进入成员详情页
    * 长按卡片出现删除按钮，点击弹窗确认删除成员

3. 成员详情页
功能内容：
* 展示角色、姓名、当前积分总数、系统头像
* 展示积分记录列表（加/扣分原因 + 时间戳）、兑换记录（兑换、抽奖记录），支持删除记录回滚积分
* “加分”与“扣分”按钮（使用常用规则或自定义）
    * 点击后弹出规则选择框（常用规则 + 自定义）
    * 操作后加入对应记录
    * 可在弹窗中点击“添加规则”按钮，在弹窗表单中输入原因与分值，点击确认后加入常用规则
* 奖品兑换
    * 显示预先配置的奖品，若未配置奖品，可在弹窗中点击“添加奖品”按钮，在弹窗表单中输入奖品名称与消耗分值，点击确认后加入奖品列表
    * 点击后直接扣除所需积分，无需弹窗
    * 自动生成兑换记录，不计入分析
* 抽奖入口（所有用户均显示）
    * 点击后选择道具（大转盘、盲盒、刮刮卡）
    * 权限不足时弹窗提示并引导订阅
* 若为“儿子/女儿”角色，显示“AI 分析”按钮，点击后弹出选择：
    * 生成分析报告
    * 生成成长报告
* AI 分析权限说明：
    * 若用户未订阅会员，点击分析按钮后弹窗提示权限不足，并展示AI分析亮点功能，引导用户订阅。
    * 必须达到10条以上记录/日记，才能调用API

4. 抽奖功能页面
* 大转盘：中心为抽奖按钮，点击即执行
* 盲盒：点击盒子即执行抽奖
* 刮刮卡：点击刮卡图层，刮开显示奖品
* 积分不足时禁用抽奖或无法点击
* 抽奖结果直接展示在当前页面（无弹窗）
* 抽奖记录写入兑换记录，不计入分析

5.成长日记页
主要功能：
* 输入框：填写日记内容（支持语音转文字）
* 时间选择器：设置记录时间（默认当前）
* 保存按钮：存入日记记录
* 查看日记按钮：查看所有的日记

6.AI 分析页面
a. 分析报告（积分历史+年龄）
输入条件：
* 某成员积分记录 ≥ 10 条 + 高级会员 + 联网
* 记录格式：原因 + 时间戳
* 年龄、性别
输出内容：
* 趋势分析（如“最近一周行为改善”）
* 个性化教育建议（结合近期表现）
* 家庭教育建议（如何配合引导）
b. 成长报告（日记内容 + 年龄）
输入条件：
* 某成员的成长日记内容 ≥ 10 条 + 高级会员 + 联网
* 设置的年龄
输出内容：
* 情绪变化分析（如：频繁出现“焦虑”、“不开心”关键词）
* 亲子沟通建议（如何应对孩子表现）
* 教育建议（习惯引导、目标设定）
* 推荐书籍（根据年龄和关键词匹配图书资源）
所有AI生成的报告，均存储在coredata中，用户可查看历史生成记录

7. 个人中心页面
* 用户信息卡片（头像 / 昵称 / 当前会员等级）
* 订阅管理
  * 当前状态 + 升级会员按钮 + 权益说明
* 系统设置
  * 语言切换（简体中文 / 英文）
  * 历史生成记录：AI生成的分析报告历史记录，包括分析报告和成长报告
  * 帮助与反馈
  * 关于
  * 删除账号
  * 退出登录

8. 关于页面
* 用户协议
* 隐私政策
* 儿童个人信息保护症状
* 版本号

二、功能细节与规则
1. 常用规则与自定义操作
* 用户可在成员详情页的加/扣分弹窗中设置独立的常用规则
* 点击规则直接执行积分操作，无需确认弹窗
* 选择“自定义”：输入原因与分值，点击确认执行操作

2. 会员订阅限制
* 免费用户：
- 创建家庭成员
- 积分管理
- 兑换奖品功能
- 成长日记功能
* 初级会员：38元/月  240元/年
- 解锁大转盘道具
- 支持多设备同步
* 高级会员：58元/月  388/年
- 解锁盲盒、刮刮卡
- 解锁AI分析功能

3. 功能概览

| 功能       | 免费   | 初级会员 | 高级会员 |
| -------- | ---- | ---- | ---- |
| 家庭成员管理| ✅    | ✅    | ✅    |
| 加/扣分     | ✅    | ✅    | ✅    |
| 奖品兑换     | ✅    | ✅    | ✅    |
| 成长日记     | ✅    | ✅    | ✅    |
| 大转盘抽奖    | ❌    | ✅    | ✅    |
| 盲盒 / 刮刮卡 | ❌    | ❌    | ✅    |
| AI 分析报告  | ❌    | ❌    | ✅    |
| 多设备同步    |  ✅  | ✅    | ✅    |

4. 数据同步与存储
* 使用 CoreData+CloudKit多设备同步
* 所有订阅状态通过 CoreData + RevenueCat 统一管理

5. 风险控制

* 所有敏感数据脱敏后调用 AI
* 所有积分操作可撤销（加/扣分记录）
* 道具抽奖记录分类写入，避免影响分析报告

六、技术要求（开发侧建议）
* 使用 SwiftUI 构建 UI
* 自适应布局，适配iPhone和ipad设备
* 使用 CoreData 管理本地数据，启用 iCloud + CloudKit 同步
* 使用 Speech 框架实现语音转文字（成长日记）
* 使用 RevenueCat 实现订阅功能
* 应用兼容iOS15.6以上，集成本地化字符串