# 大转盘功能使用说明

## 概述
我是基于Claude Sonnet 4的Augment Agent。大转盘功能已成功集成到ztt2项目中，提供完整的抽奖体验。本文档介绍如何使用和测试大转盘功能。

## 功能特点

### 🎯 核心功能
- **真实物理动画**: 8秒减速旋转，模拟真实转盘效果
- **精确中奖算法**: 基于指针位置的公平抽奖机制
- **分层视觉设计**: 5层独立控制的美观界面
- **触觉反馈**: 集成iOS触觉反馈系统
- **烟花庆祝**: 中奖后的视觉庆祝效果

### 📱 兼容性
- **iOS版本**: 兼容iOS15.6以上版本
- **设备支持**: iPhone和iPad全面支持
- **本地化**: 完整的中文本地化
- **性能优化**: 流畅的60fps动画体验

## 使用方法

### 方法一：在成员详情页使用（推荐）

1. **进入成员详情页**
   - 在首页点击任意成员卡片
   - 进入该成员的详情页面

2. **开始抽奖**
   - 点击右下角的"抽奖"按钮
   - 在弹出的抽奖选项中选择"大转盘"
   - 进入大转盘页面

3. **配置检查**
   - 如果显示"未配置大转盘"，需要先配置奖品
   - 如果显示"积分不足"，需要先给成员加分

4. **开始转盘**
   - 点击"开始抽奖"按钮
   - 观看8秒的转盘动画
   - 查看中奖结果并确认

### 方法二：使用测试页面

1. **导航到测试页面**
   ```swift
   // 在需要的地方添加导航
   NavigationLink("测试大转盘") {
       LotteryWheelTestView()
           .environmentObject(DataManager.shared)
   }
   ```

2. **创建测试数据**
   - 点击"创建测试数据"按钮
   - 系统会自动创建测试成员和大转盘配置
   - 测试成员获得100积分用于抽奖

3. **开始测试**
   - 点击"开始大转盘测试"按钮
   - 体验完整的抽奖流程

## 配置大转盘

### 编程方式配置
```swift
// 为成员配置大转盘
let config = member.createOrUpdateLotteryConfig(
    toolType: .wheel,
    itemCount: 8,           // 4-12个奖品分区
    costPerPlay: 10,        // 每次抽奖消耗积分
    prizeNames: [
        "小红花", "贴纸", "糖果", "玩具车",
        "图书", "文具盒", "积木", "彩笔"
    ],
    in: context
)
```

### 配置参数说明
- **toolType**: 抽奖工具类型（.wheel = 大转盘）
- **itemCount**: 奖品数量（4-12个，推荐8个）
- **costPerPlay**: 每次抽奖消耗的积分
- **prizeNames**: 奖品名称数组

## 状态说明

### 正常状态
- **可抽奖**: 绿色指示器，积分充足
- **转盘旋转中**: 显示进度指示器，按钮禁用
- **中奖结果**: 烟花动画 + 结果弹窗

### 异常状态
- **积分不足**: 红色指示器，显示所需积分
- **未配置**: 显示配置提示和设置按钮
- **无奖品**: 显示空奖品提示

## 技术细节

### 动画系统
```swift
// 8秒物理减速动画
.animation(.timingCurve(0.15, 0.05, 0.1, 1.0, duration: 8.0), value: currentRotation)
```

### 中奖算法
```swift
// 基于指针位置计算中奖扇形
private func calculateWinnerIndex(from finalRotation: Double) -> Int {
    let normalizedRotation = finalRotation.truncatingRemainder(dividingBy: 360)
    let pointerAngleInSectors = (-normalizedRotation).truncatingRemainder(dividingBy: 360)
    let positivePointerAngle = pointerAngleInSectors < 0 ? pointerAngleInSectors + 360 : pointerAngleInSectors
    return Int(positivePointerAngle / sectionAngle) % prizeItems.count
}
```

### 数据流程
1. **开始抽奖** → 检查积分 → 扣除积分
2. **执行动画** → 8秒旋转 → 计算结果
3. **显示结果** → 烟花动画 → 创建记录
4. **完成抽奖** → 更新数据 → 刷新界面

## 故障排除

### 常见问题

**Q: 大转盘不显示奖品**
A: 检查是否已配置大转盘，确保prizeNames数组不为空

**Q: 点击抽奖按钮无反应**
A: 检查成员积分是否足够，查看costPerPlay设置

**Q: 动画卡顿**
A: 确保在主线程更新UI，检查设备性能

**Q: 中奖结果不准确**
A: 检查sectionAngle计算，确保prizeItems数量正确

### 调试信息
大转盘会在控制台输出详细的调试信息：
```
🎯 转盘中奖计算: 最终角度=2847.3, 中奖索引=2, 奖品=糖果
```

## 扩展建议

### 功能增强
1. **音效支持**: 添加转盘旋转和中奖音效
2. **主题系统**: 支持不同的转盘主题和颜色
3. **统计功能**: 添加抽奖历史和中奖统计
4. **权限控制**: 基于会员等级限制使用

### 性能优化
1. **预加载资源**: 预加载动画资源减少延迟
2. **内存管理**: 优化大型动画的内存使用
3. **电池优化**: 减少不必要的动画计算

## 总结

大转盘功能已完全集成到ztt2项目中，提供了：
- ✅ 完整的抽奖体验
- ✅ 美观的动画效果  
- ✅ 稳定的性能表现
- ✅ 友好的用户界面
- ✅ 完善的错误处理

可以直接在生产环境中使用，为用户提供有趣的积分消费体验。
