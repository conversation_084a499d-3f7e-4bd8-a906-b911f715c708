//
//  DataModelTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/7/30.
//

import XCTest
import CoreData
@testable import ztt2

final class DataModelTests: XCTestCase {
    
    var persistenceController: PersistenceController!
    var testContext: NSManagedObjectContext!

    override func setUpWithError() throws {
        // 使用内存存储进行测试
        persistenceController = PersistenceController(inMemory: true)
        testContext = persistenceController.container.viewContext
    }
    
    override func tearDownWithError() throws {
        persistenceController = nil
        testContext = nil
    }
    
    // MARK: - 基础数据模型测试

    func testCreateUser() throws {
        // 测试创建用户
        let user = User(context: testContext)
        user.id = UUID()
        user.nickname = "测试用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()

        try testContext.save()

        XCTAssertNotNil(user.id)
        XCTAssertEqual(user.nickname, "测试用户")
        XCTAssertEqual(user.email, "<EMAIL>")
    }

    func testCreateMember() throws {
        // 测试创建成员
        let user = User(context: testContext)
        user.id = UUID()
        user.createdAt = Date()

        let member = Member(context: testContext)
        member.id = UUID()
        member.name = "测试成员"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        member.currentPoints = 100
        member.memberNumber = 1
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user

        try testContext.save()

        XCTAssertNotNil(member)
        XCTAssertEqual(member.name, "测试成员")
        XCTAssertEqual(member.role, "son")
        XCTAssertEqual(member.currentPoints, 100)
        XCTAssertEqual(member.memberNumber, 1)
        XCTAssertEqual(member.age, 8)
    }
    
    func testMemberAgeCalculation() throws {
        let birthDate = Calendar.current.date(byAdding: .year, value: -10, to: Date())!

        let member = Member(context: testContext)
        member.id = UUID()
        member.name = "测试孩子"
        member.role = "daughter"
        member.birthDate = birthDate
        member.currentPoints = 0
        member.memberNumber = 1
        member.createdAt = Date()
        member.updatedAt = Date()

        try testContext.save()

        XCTAssertEqual(member.age, 10)
        XCTAssertTrue(member.isChild)
    }
    
    // MARK: - 积分系统测试

    func testPointRecord() throws {
        let member = Member(context: testContext)
        member.id = UUID()
        member.name = "积分测试"
        member.role = "son"
        member.currentPoints = 50
        member.memberNumber = 1
        member.createdAt = Date()
        member.updatedAt = Date()

        let pointRecord = PointRecord(context: testContext)
        pointRecord.id = UUID()
        pointRecord.reason = "完成作业"
        pointRecord.value = 10
        pointRecord.timestamp = Date()
        pointRecord.recordType = "behavior"
        pointRecord.isReversed = false
        pointRecord.member = member

        try testContext.save()

        XCTAssertEqual(pointRecord.reason, "完成作业")
        XCTAssertEqual(pointRecord.value, 10)
        XCTAssertEqual(pointRecord.recordType, "behavior")
        XCTAssertFalse(pointRecord.isReversed)
        XCTAssertEqual(member.allPointRecords.count, 1)
    }
    
    func testSubscription() throws {
        let user = User(context: testContext)
        user.id = UUID()
        user.createdAt = Date()

        let subscription = Subscription(context: testContext)
        subscription.id = UUID()
        subscription.subscriptionType = "premium"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        try testContext.save()

        XCTAssertEqual(subscription.subscriptionType, "premium")
        XCTAssertTrue(subscription.isActive)
        XCTAssertEqual(subscription.subscriptionTypeDisplayName, "高级会员")
        XCTAssertEqual(user.subscriptionType, "premium")
        XCTAssertTrue(user.isPremiumMember)
    }
}
}
