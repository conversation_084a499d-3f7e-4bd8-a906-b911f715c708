//
//  GrowthDiaryViewModelTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/7/30.
//

import XCTest
import CoreData
@testable import ztt2

class GrowthDiaryViewModelTests: XCTestCase {
    
    var viewModel: GrowthDiaryViewModel!
    var dataManager: DataManager!
    var testMember: Member!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // 使用内存存储进行测试
        dataManager = DataManager.shared
        viewModel = GrowthDiaryViewModel()
        
        // 创建测试成员
        testMember = dataManager.createMember(name: "测试儿子", role: "son", birthDate: Date())
    }
    
    override func tearDownWithError() throws {
        if let member = testMember {
            dataManager.deleteMember(member)
        }
        viewModel = nil
        dataManager = nil
        testMember = nil
        try super.tearDownWithError()
    }
    
    func testGetChildren() throws {
        // 创建不同角色的成员
        let daughter = dataManager.createMember(name: "测试女儿", role: "daughter", birthDate: Date())
        let father = dataManager.createMember(name: "测试爸爸", role: "father", birthDate: Date())
        
        let children = viewModel.getChildren()
        
        // 验证只返回儿子和女儿
        XCTAssertTrue(children.contains { $0.name == "测试儿子" })
        XCTAssertTrue(children.contains { $0.name == "测试女儿" })
        XCTAssertFalse(children.contains { $0.name == "测试爸爸" })
        
        // 清理
        if let daughter = daughter {
            dataManager.deleteMember(daughter)
        }
        if let father = father {
            dataManager.deleteMember(father)
        }
    }
    
    func testSaveDiary() throws {
        let expectation = self.expectation(description: "Save diary")
        let content = "今天学会了新的单词"
        let timestamp = Date()
        
        viewModel.saveDiary(content: content, timestamp: timestamp, for: testMember) { success, error in
            XCTAssertTrue(success)
            XCTAssertNil(error)
            expectation.fulfill()
        }
        
        waitForExpectations(timeout: 2.0)
        
        // 验证日记已保存
        let diaryEntries = viewModel.getDiaryEntries(for: testMember)
        XCTAssertEqual(diaryEntries.count, 1)
        XCTAssertEqual(diaryEntries.first?.content, content)
    }
    
    func testSaveDiaryWithEmptyContent() throws {
        let expectation = self.expectation(description: "Save empty diary")
        
        viewModel.saveDiary(content: "", timestamp: Date(), for: testMember) { success, error in
            XCTAssertFalse(success)
            XCTAssertNotNil(error)
            XCTAssertEqual(error, "日记内容不能为空")
            expectation.fulfill()
        }
        
        waitForExpectations(timeout: 2.0)
    }
    
    func testUpdateDiary() throws {
        // 先创建一个日记
        let originalContent = "原始内容"
        let entry = dataManager.createDiaryEntry(for: testMember, content: originalContent)
        
        let expectation = self.expectation(description: "Update diary")
        let newContent = "更新后的内容"
        
        viewModel.updateDiary(entry, content: newContent) { success, error in
            XCTAssertTrue(success)
            XCTAssertNil(error)
            expectation.fulfill()
        }
        
        waitForExpectations(timeout: 2.0)
        
        // 验证内容已更新
        XCTAssertEqual(entry.content, newContent)
    }
    
    func testDeleteDiary() throws {
        // 先创建一个日记
        let entry = dataManager.createDiaryEntry(for: testMember, content: "要删除的日记")
        
        let expectation = self.expectation(description: "Delete diary")
        
        viewModel.deleteDiary(entry) { success, error in
            XCTAssertTrue(success)
            XCTAssertNil(error)
            expectation.fulfill()
        }
        
        waitForExpectations(timeout: 2.0)
        
        // 验证日记已删除
        let diaryEntries = viewModel.getDiaryEntries(for: testMember)
        XCTAssertEqual(diaryEntries.count, 0)
    }
    
    func testGetDiaryStatistics() throws {
        // 创建多个日记条目
        let today = Date()
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!
        
        dataManager.createDiaryEntry(for: testMember, content: "今天的日记1", timestamp: today)
        dataManager.createDiaryEntry(for: testMember, content: "今天的日记2", timestamp: today)
        dataManager.createDiaryEntry(for: testMember, content: "昨天的日记", timestamp: yesterday)
        
        let statistics = viewModel.getDiaryStatistics(for: testMember)
        
        XCTAssertEqual(statistics.totalEntries, 3)
        XCTAssertEqual(statistics.uniqueDays, 2)
    }
    
    func testFormatDate() throws {
        let date = Calendar.current.date(from: DateComponents(year: 2025, month: 7, day: 30))!
        let formattedDate = viewModel.formatDate(date, style: .medium)
        
        XCTAssertFalse(formattedDate.isEmpty)
        XCTAssertTrue(formattedDate.contains("2025"))
    }
    
    func testCanGenerateAIReport() throws {
        // 测试分析报告权限
        XCTAssertFalse(viewModel.canGenerateAIReport(for: testMember, reportType: "analysis"))
        
        // 添加足够的积分记录
        for i in 1...15 {
            dataManager.addPointRecord(to: testMember, reason: "测试记录\(i)", value: 10)
        }
        
        XCTAssertTrue(viewModel.canGenerateAIReport(for: testMember, reportType: "analysis"))
        
        // 测试成长报告权限
        XCTAssertFalse(viewModel.canGenerateAIReport(for: testMember, reportType: "growth"))
        
        // 添加足够的日记条目
        for i in 1...15 {
            dataManager.createDiaryEntry(for: testMember, content: "测试日记\(i)")
        }
        
        XCTAssertTrue(viewModel.canGenerateAIReport(for: testMember, reportType: "growth"))
    }
}
