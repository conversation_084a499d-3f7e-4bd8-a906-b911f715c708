//
//  GrowthDiaryViewModel.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/30.
//

import Foundation
import Combine
import CoreData

/**
 * 成长日记视图模型
 * 管理成长日记的数据操作和业务逻辑
 */
class GrowthDiaryViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var diaryEntries: [DiaryEntry] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String = ""
    @Published var showError: Bool = false
    
    // MARK: - Private Properties
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        observeDataChanges()
    }
    
    // MARK: - Data Observation
    private func observeDataChanges() {
        // 监听数据变化
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    self?.objectWillChange.send()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /**
     * 获取儿子和女儿角色的成员
     */
    func getChildren() -> [Member] {
        return dataManager.members.filter { $0.isChild }
    }
    
    /**
     * 保存日记条目
     */
    func saveDiary(content: String, timestamp: Date, for member: Member, completion: @escaping (Bool, String?) -> Void) {
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedContent.isEmpty else {
            completion(false, "日记内容不能为空")
            return
        }
        
        isLoading = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            guard let self = self else { return }
            
            let _ = self.dataManager.createDiaryEntry(for: member, content: trimmedContent, timestamp: timestamp)
            
            self.isLoading = false
            completion(true, nil)
        }
    }
    
    /**
     * 更新日记条目
     */
    func updateDiary(_ entry: DiaryEntry, content: String? = nil, timestamp: Date? = nil, completion: @escaping (Bool, String?) -> Void) {
        if let content = content {
            let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedContent.isEmpty else {
                completion(false, "日记内容不能为空")
                return
            }
        }
        
        isLoading = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            guard let self = self else { return }
            
            self.dataManager.updateDiaryEntry(entry, content: content, timestamp: timestamp)
            
            self.isLoading = false
            completion(true, nil)
        }
    }
    
    /**
     * 删除日记条目
     */
    func deleteDiary(_ entry: DiaryEntry, completion: @escaping (Bool, String?) -> Void) {
        isLoading = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            guard let self = self else { return }
            
            self.dataManager.deleteDiaryEntry(entry)
            
            self.isLoading = false
            completion(true, nil)
        }
    }
    
    /**
     * 获取成员的日记条目
     */
    func getDiaryEntries(for member: Member) -> [DiaryEntry] {
        return member.allDiaryEntries
    }
    
    /**
     * 获取成员日记统计信息
     */
    func getDiaryStatistics(for member: Member) -> (totalEntries: Int, uniqueDays: Int) {
        let entries = member.allDiaryEntries
        let dates = entries.compactMap { $0.timestamp }
        let uniqueDates = Set(dates.map { Calendar.current.startOfDay(for: $0) })
        
        return (totalEntries: entries.count, uniqueDays: uniqueDates.count)
    }
    
    /**
     * 检查成员是否可以生成AI报告
     */
    func canGenerateAIReport(for member: Member, reportType: String) -> Bool {
        switch reportType {
        case "analysis":
            return member.canGenerateAnalysisReport
        case "growth":
            return member.canGenerateGrowthReport
        default:
            return false
        }
    }
    
    /**
     * 格式化日期显示
     */
    func formatDate(_ date: Date, style: DateFormatter.Style = .medium) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = style
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    /**
     * 格式化时间显示
     */
    func formatDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy/M/d HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    // MARK: - Error Handling
    private func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        showError = true
        isLoading = false
    }
}
