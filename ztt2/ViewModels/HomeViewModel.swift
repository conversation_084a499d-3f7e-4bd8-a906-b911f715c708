//
//  HomeViewModel.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI
import Combine
import CoreData

/**
 * 首页视图模型
 * 管理首页的状态和业务逻辑
 */
class HomeViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 当前选中的Tab索引
    @Published var selectedTabIndex: Int = 0

    /// 家庭成员列表 (从Core Data获取)
    @Published var members: [Member] = []

    /// 全家总积分
    @Published var totalFamilyScore: Int = 0

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    // MARK: - Private Properties

    private var cancellables = Set<AnyCancellable>()
    private let dataManager = DataManager.shared

    // MARK: - Initialization

    init() {
        setupDataBinding()
        // 延迟加载数据，避免启动时的问题
        DispatchQueue.main.async {
            self.loadMembers()
        }
    }

    // MARK: - Data Binding

    private func setupDataBinding() {
        // 监听DataManager中的成员变化
        dataManager.$members
            .receive(on: DispatchQueue.main)
            .sink { [weak self] members in
                self?.members = members
                self?.calculateTotalScore()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods

    /**
     * 选择Tab
     * @param index Tab索引
     */
    func selectTab(_ index: Int) {
        selectedTabIndex = index
    }

    /**
     * 添加家庭成员
     * @param name 成员姓名
     * @param role 家庭角色
     * @param birthDate 出生日期
     * @param initialPoints 初始积分
     */
    func addMember(name: String, role: String, birthDate: Date?, initialPoints: Int) {
        isLoading = true
        errorMessage = nil

        if dataManager.createMember(
            name: name,
            role: role,
            birthDate: birthDate,
            initialPoints: Int32(initialPoints)
        ) != nil {
            // 强制刷新成员列表
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.members = self.dataManager.members
            }
        } else {
            errorMessage = "添加成员失败"
        }

        isLoading = false
    }

    /**
     * 删除家庭成员
     * @param member 要删除的成员
     */
    func deleteMember(_ member: Member) {
        isLoading = true
        errorMessage = nil

        dataManager.deleteMember(member)
        print("成功删除成员: \(member.displayName)")

        isLoading = false
    }

    /**
     * 全家加分操作
     * @param reason 加分原因
     * @param value 加分数值
     */
    func addPointsToAllMembers(reason: String, value: Int) {
        isLoading = true
        errorMessage = nil

        for member in members {
            dataManager.addPointRecord(to: member, reason: reason, value: Int32(value))
        }

        isLoading = false
        print("全家加分完成: \(reason) +\(value)分")
    }

    /**
     * 全家扣分操作
     * @param reason 扣分原因
     * @param value 扣分数值
     */
    func deductPointsFromAllMembers(reason: String, value: Int) {
        isLoading = true
        errorMessage = nil

        for member in members {
            dataManager.addPointRecord(to: member, reason: reason, value: Int32(-value))
        }

        isLoading = false
        print("全家扣分完成: \(reason) -\(value)分")
    }

    /**
     * 刷新数据
     */
    func refresh() {
        loadMembers()
    }
    
    // MARK: - Private Methods

    /**
     * 加载家庭成员数据
     */
    private func loadMembers() {
        isLoading = true
        errorMessage = nil

        // DataManager会自动通过数据绑定更新members数组
        // 这里只需要触发数据更新即可

        isLoading = false
    }

    /**
     * 计算全家总积分
     */
    private func calculateTotalScore() {
        totalFamilyScore = members.reduce(0) { $0 + Int($1.currentPoints) }
    }

    /**
     * 获取指定时间范围内的总积分
     * @param dateRange 时间范围类型
     * @return 总积分
     */
    func getTotalScoreForDateRange(_ dateRange: DateRangeType) -> Int {
        let (startDate, endDate) = getDateRangeForType(dateRange)

        var totalScore = 0
        for member in members {
            let memberScore = dataManager.getMemberPointsInDateRange(
                member: member,
                startDate: startDate,
                endDate: endDate
            )
            totalScore += memberScore
        }

        return totalScore
    }

    /**
     * 根据时间范围类型获取具体日期范围
     */
    private func getDateRangeForType(_ type: DateRangeType) -> (Date, Date) {
        let calendar = Calendar.current
        let now = Date()

        switch type {
        case .thisWeek:
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            let endOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.end ?? now
            return (startOfWeek, endOfWeek)
        case .thisMonth:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            let endOfMonth = calendar.dateInterval(of: .month, for: now)?.end ?? now
            return (startOfMonth, endOfMonth)
        case .custom(let start, let end):
            // 确保结束时间是当天的23:59:59
            let endOfDay = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: end) ?? end
            return (start, endOfDay)
        }
    }
}
