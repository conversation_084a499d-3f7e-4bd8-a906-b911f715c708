//
//  BlindBoxViewModel.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import Foundation

/**
 * 盲盒游戏视图模型
 * 管理盲盒游戏的业务逻辑、动画状态和数据操作
 * 基于ztt1项目的BlindBoxViewModel适配到ztt2项目
 */
@MainActor
class BlindBoxViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var boxItems: [BlindBoxItem] = []
    @Published var animationStates: [UUID: ExplosionState] = [:]
    @Published var particles: [ParticleItem] = []
    @Published var showResult = false
    @Published var resultPrize = ""
    @Published var showInsufficientPoints = false
    @Published var isLoading = false
    
    // MARK: - Configuration Properties
    
    private var config: BlindBoxConfig?
    private let dataManager: DataManager
    
    // MARK: - Animation Properties
    
    private var particleTimer: Timer?
    private let maxParticles = 50
    private let particleLifespan: Double = 2.0
    
    // MARK: - Computed Properties
    
    /**
     * 每次开箱消耗的积分
     */
    var costPerOpen: Int {
        return config?.costPerOpen ?? 10
    }
    
    /**
     * 当前成员的积分
     */
    var currentPoints: Int {
        return Int(config?.member.currentPoints ?? 0)
    }
    
    /**
     * 是否有足够积分开箱
     */
    var canAffordOpening: Bool {
        return currentPoints >= costPerOpen
    }
    
    /**
     * 盲盒总数
     */
    var totalBoxCount: Int {
        return boxItems.count
    }
    
    /**
     * 已开启的盲盒数量
     */
    var openedBoxCount: Int {
        return boxItems.filter { $0.isOpened }.count
    }
    
    /**
     * 剩余盲盒数量
     */
    var remainingBoxCount: Int {
        return totalBoxCount - openedBoxCount
    }
    
    // MARK: - Initializer
    
    init(dataManager: DataManager) {
        self.dataManager = dataManager
    }
    
    // MARK: - Public Methods
    
    /**
     * 加载盲盒配置
     */
    func loadBlindBoxConfig(with config: BlindBoxConfig) {
        self.config = config
        generateBlindBoxItems()
    }
    
    /**
     * 开启指定索引的盲盒
     */
    func openBlindBox(at index: Int) -> Bool {
        guard index < boxItems.count else { return false }
        guard boxItems[index].isClickable else { return false }
        guard canAffordOpening else {
            showInsufficientPoints = true
            return false
        }
        
        // 开始动画
        boxItems[index].startOpening()
        animationStates[boxItems[index].id] = .exploding
        
        // 随机选择奖品
        let selectedPrize = getRandomPrize()
        
        // 执行开箱动画序列
        executeOpeningAnimation(at: index, prize: selectedPrize)
        
        return true
    }
    
    /**
     * 确认开箱结果
     */
    func confirmResult() {
        showResult = false
        resultPrize = ""
    }
    
    /**
     * 重置所有盲盒状态
     */
    func resetAllBoxes() {
        for i in 0..<boxItems.count {
            boxItems[i].resetAnimation()
            boxItems[i].isOpened = false
        }
        animationStates.removeAll()
        particles.removeAll()
        stopParticleAnimation()
    }
    
    /**
     * 刷新数据
     */
    func refresh() {
        // 重新加载成员数据
        if let config = config {
            // 更新成员积分信息
            self.config = BlindBoxConfig(
                boxCount: config.boxCount,
                costPerOpen: config.costPerOpen,
                prizes: config.prizes,
                member: config.member // 这里应该从DataManager重新获取最新的成员数据
            )
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 生成盲盒项目
     */
    private func generateBlindBoxItems() {
        guard let config = config else { return }
        
        boxItems = (0..<config.boxCount).map { index in
            BlindBoxItem.create(
                index: index,
                prizeName: config.prizes[index % config.prizes.count]
            )
        }
    }
    
    /**
     * 随机选择奖品
     */
    private func getRandomPrize() -> String {
        guard let config = config else { return "默认奖品" }
        return config.prizes.randomElement() ?? "默认奖品"
    }
    
    /**
     * 执行开箱动画序列
     */
    private func executeOpeningAnimation(at index: Int, prize: String) {
        let boxId = boxItems[index].id
        
        // 阶段1: 预备放大 (0.1秒)
        withAnimation(.easeOut(duration: 0.1)) {
            boxItems[index].scaleEffect = 1.2
        }
        
        // 阶段2: 爆炸分解 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.3)) {
                self.animationStates[boxId] = .exploding
            }
            
            // 生成粒子
            self.generateParticles(at: self.boxItems[index].position)
        }
        
        // 阶段3: 显示奖品并立即生成记录 (0.2秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeIn(duration: 0.2)) {
                // 更新盲盒显示的奖品名称为实际获得的奖品
                self.boxItems[index] = BlindBoxItem.createWithPrize(
                    index: self.boxItems[index].index,
                    prizeName: prize,
                    isOpened: true,
                    explosionState: .completed
                )
                self.animationStates[boxId] = .completed
            }
            
            // 立即扣除积分并生成记录
            let success = self.deductPointsForOpening()
            if success {
                // 创建抽奖记录
                self.createLotteryRecord(prize: prize, cost: self.costPerOpen)
                print("✅ 盲盒开启成功: 获得 \(prize)，消耗 \(self.costPerOpen) 积分")
            } else {
                print("❌ 盲盒开启失败: 积分扣除失败")
            }
        }
        
        // 阶段4: 显示结果弹窗 (0.2秒后)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.resultPrize = prize
            self.showResult = true
        }
    }
    
    /**
     * 生成粒子效果
     */
    private func generateParticles(at position: CGPoint) {
        let particleCount = Int.random(in: 30...maxParticles)
        let colors: [Color] = [.yellow, .orange, .pink, .purple, .blue, .green]
        
        for _ in 0..<particleCount {
            let angle = Double.random(in: 0...(2 * .pi))
            let speed = CGFloat.random(in: 150...300)
            let velocity = CGPoint(
                x: CGFloat(Foundation.cos(angle)) * speed,
                y: CGFloat(Foundation.sin(angle)) * speed
            )
            
            let particle = ParticleItem(
                startPosition: position,
                currentPosition: position,
                velocity: velocity,
                color: colors.randomElement() ?? .yellow,
                size: CGFloat.random(in: 4...8),
                lifespan: particleLifespan
            )
            
            particles.append(particle)
        }
        
        startParticleAnimation()
    }
    
    /**
     * 开始粒子动画
     */
    private func startParticleAnimation() {
        guard particleTimer == nil else { return }
        
        particleTimer = Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { _ in
            Task { @MainActor in
                self.updateParticles()
            }
        }
    }
    
    /**
     * 停止粒子动画
     */
    private func stopParticleAnimation() {
        particleTimer?.invalidate()
        particleTimer = nil
    }
    
    /**
     * 更新粒子状态
     */
    private func updateParticles() {
        let deltaTime: Double = 0.016
        
        for i in particles.indices.reversed() {
            particles[i].age += deltaTime
            particles[i].currentPosition.x += particles[i].velocity.x * deltaTime
            particles[i].currentPosition.y += particles[i].velocity.y * deltaTime
            
            // 重力效果
            particles[i].velocity.y += 300 * deltaTime
            
            // 移除死亡粒子
            if !particles[i].isAlive {
                particles.remove(at: i)
            }
        }
        
        // 如果没有粒子了，停止动画
        if particles.isEmpty {
            stopParticleAnimation()
        }
    }
    
    /**
     * 扣除开箱积分
     */
    private func deductPointsForOpening() -> Bool {
        guard let config = config else { return false }

        // 检查积分是否足够
        guard config.member.currentPoints >= costPerOpen else { return false }

        // 直接扣除积分
        config.member.currentPoints -= Int32(costPerOpen)
        config.member.updatedAt = Date()

        // 保存数据
        dataManager.save()

        return true
    }

    /**
     * 创建抽奖记录
     */
    private func createLotteryRecord(prize: String, cost: Int) {
        guard let config = config else { return }

        // 使用DataManager创建抽奖记录
        dataManager.createLotteryRecord(
            for: config.member,
            toolType: "blind_box.record.type".localized,
            prizeResult: prize,
            cost: Int32(cost)
        )

        // 创建兑换记录（盲盒获得的奖品）
        dataManager.createRedemptionRecord(
            for: config.member,
            prizeName: prize,
            cost: 0, // 抽奖获得的奖品成本为0
            source: "lottery"
        )
    }
}

// MARK: - Extensions

extension BlindBoxViewModel {
    
    /**
     * 检查是否所有盲盒都已开启
     */
    var isAllBoxesOpened: Bool {
        return boxItems.allSatisfy { $0.isOpened }
    }
    
    /**
     * 获取盲盒开启进度（0.0 - 1.0）
     */
    var openingProgress: Double {
        guard totalBoxCount > 0 else { return 0.0 }
        return Double(openedBoxCount) / Double(totalBoxCount)
    }
}
