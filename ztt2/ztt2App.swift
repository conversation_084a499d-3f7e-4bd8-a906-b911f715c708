//
//  ztt2App.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI

@main
struct ztt2App: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var dataManager = DataManager.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(dataManager)
        }
    }
}
