//
//  MemberPointsModels.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation

/**
 * 成员积分操作类型
 */
enum MemberPointsOperationType: String, CaseIterable, Codable {
    case add = "add"
    case deduct = "deduct"
    
    var displayName: String {
        switch self {
        case .add:
            return "加分"
        case .deduct:
            return "扣分"
        }
    }
    
    var colorHex: String {
        switch self {
        case .add:
            return "#26C34B"
        case .deduct:
            return "#FF5B5B"
        }
    }
    
    var iconName: String {
        switch self {
        case .add:
            return "plus.circle.fill"
        case .deduct:
            return "minus.circle.fill"
        }
    }
}

/**
 * 成员积分规则数据模型（UI层使用）
 */
struct MemberPointsRule: Identifiable, Codable {
    let id: UUID
    let name: String
    let value: Int
    let type: MemberPointsOperationType
    let createdAt: Date

    init(id: UUID = UUID(), name: String, value: Int, type: MemberPointsOperationType) {
        self.id = id
        self.name = name
        self.value = value
        self.type = type
        self.createdAt = Date()
    }
}

/**
 * 成员积分表单数据模型
 */
struct MemberPointsFormData {
    var items: [FormItem]
    let operationType: MemberPointsOperationType
    
    init(operationType: MemberPointsOperationType) {
        self.operationType = operationType
        self.items = [FormItem()]
    }
    
    /**
     * 表单项数据模型
     */
    struct FormItem: Identifiable {
        let id = UUID()
        var name: String = ""
        var value: String = ""
        var saveAsRule: Bool = false
        
        var isValid: Bool {
            return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                   !value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                   Int(value) != nil &&
                   Int(value)! > 0
        }
    }
    
    // MARK: - 计算属性
    
    var validItems: [FormItem] {
        return items.filter { $0.isValid }
    }
    
    var hasValidData: Bool {
        return !validItems.isEmpty
    }
    
    var canAddMoreItems: Bool {
        return items.count < 5
    }
    
    var canDeleteItems: Bool {
        return items.count > 1
    }
    
    var totalPointsChange: Int {
        let total = validItems.compactMap { Int($0.value) }.reduce(0, +)
        return operationType == .add ? total : -total
    }
    
    // MARK: - 方法
    
    mutating func addNewItem() {
        if canAddMoreItems {
            items.append(FormItem())
        }
    }
    
    mutating func removeItem(at index: Int) {
        if canDeleteItems && index < items.count {
            items.remove(at: index)
        }
    }
    
    func validateItems() -> MemberPointsFormValidationResult {
        var errorMessages: [String] = []
        
        if items.isEmpty {
            errorMessages.append("至少需要添加一项操作")
        }
        
        if validItems.isEmpty {
            errorMessages.append("请填写完整的操作信息")
        }
        
        for (index, item) in items.enumerated() {
            if !item.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                if item.value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    errorMessages.append("第\(index + 1)项缺少分值")
                } else if Int(item.value) == nil {
                    errorMessages.append("第\(index + 1)项分值格式不正确")
                } else if Int(item.value)! <= 0 {
                    errorMessages.append("第\(index + 1)项分值必须大于0")
                }
            }
        }
        
        return MemberPointsFormValidationResult(
            isValid: errorMessages.isEmpty,
            errorMessages: errorMessages
        )
    }
}

/**
 * 表单验证结果
 */
struct MemberPointsFormValidationResult {
    let isValid: Bool
    let errorMessages: [String]
}

/**
 * 积分操作记录
 */
struct MemberPointsOperation: Identifiable {
    let id = UUID()
    let memberId: String
    let name: String
    let value: Int
    let type: MemberPointsOperationType
    let timestamp: Date

    init(memberId: String, name: String, value: Int, type: MemberPointsOperationType) {
        self.memberId = memberId
        self.name = name
        self.value = value
        self.type = type
        self.timestamp = Date()
    }
}

// MARK: - 兑换奖品相关数据模型

/**
 * 奖品数据模型
 */
struct MemberReward: Identifiable, Codable {
    let id: UUID
    let name: String
    let pointsCost: Int
    let description: String?
    let isCustom: Bool
    let createdAt: Date

    init(id: UUID = UUID(), name: String, pointsCost: Int, description: String? = nil, isCustom: Bool = false) {
        self.id = id
        self.name = name
        self.pointsCost = pointsCost
        self.description = description
        self.isCustom = isCustom
        self.createdAt = Date()
    }
}

/**
 * 兑换记录数据模型
 */
struct MemberExchangeRecord: Identifiable, Codable {
    let id: UUID
    let memberId: String
    let rewardName: String
    let pointsCost: Int
    let timestamp: Date
    let type: ExchangeType

    enum ExchangeType: String, Codable {
        case direct = "direct"      // 直接兑换
        case lottery = "lottery"    // 抽奖获得
    }

    init(memberId: String, rewardName: String, pointsCost: Int, type: ExchangeType = .direct) {
        self.id = UUID()
        self.memberId = memberId
        self.rewardName = rewardName
        self.pointsCost = pointsCost
        self.type = type
        self.timestamp = Date()
    }
}

/**
 * 添加奖品表单数据模型
 */
struct AddRewardFormData {
    var name: String = ""
    var pointsCost: String = ""
    var description: String = ""

    var isValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !pointsCost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               Int(pointsCost) != nil &&
               Int(pointsCost)! > 0
    }

    var pointsCostValue: Int {
        return Int(pointsCost) ?? 0
    }
}

/**
 * 批量奖品表单数据模型
 * 基于MemberPointsFormData设计，支持批量添加奖品
 */
struct MemberRewardFormData {
    var items: [RewardFormItem]

    init() {
        self.items = [RewardFormItem()]
    }

    /**
     * 奖品表单项数据模型
     */
    struct RewardFormItem: Identifiable {
        let id = UUID()
        var name: String = ""
        var pointsCost: String = ""

        var isValid: Bool {
            return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                   !pointsCost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                   Int(pointsCost) != nil &&
                   Int(pointsCost)! > 0
        }

        var pointsCostValue: Int {
            return Int(pointsCost) ?? 0
        }

        var formattedName: String {
            return name.trimmingCharacters(in: .whitespacesAndNewlines)
        }
    }

    // MARK: - 计算属性

    var validItems: [RewardFormItem] {
        return items.filter { $0.isValid }
    }

    var hasValidData: Bool {
        return !validItems.isEmpty
    }

    var canAddMoreItems: Bool {
        return items.count < 5
    }

    // MARK: - 方法

    mutating func addItem() {
        if canAddMoreItems {
            items.append(RewardFormItem())
        }
    }

    mutating func removeItem(at index: Int) {
        if items.count > 1 && index >= 0 && index < items.count {
            items.remove(at: index)
        }
    }
}

/**
 * 批量奖品表单验证结果
 */
struct MemberRewardFormValidationResult {
    let isValid: Bool
    let validRewards: [MemberRewardFormData.RewardFormItem]
    let errorMessages: [String]

    static let empty = MemberRewardFormValidationResult(
        isValid: false,
        validRewards: [],
        errorMessages: []
    )
}

// MARK: - 批量验证扩展
extension Array where Element == MemberRewardFormData.RewardFormItem {

    /**
     * 批量验证奖品表单数据
     */
    func validateBatch() -> MemberRewardFormValidationResult {
        var errorMessages: [String] = []
        var validRewards: [MemberRewardFormData.RewardFormItem] = []

        // 检查内部重复
        var seenNames: [String: Int] = [:]

        for (index, item) in self.enumerated() {
            let itemNumber = index + 1
            let trimmedName = item.formattedName

            // 跳过空项
            if trimmedName.isEmpty && item.pointsCost.isEmpty {
                continue
            }

            // 验证名称
            if trimmedName.isEmpty {
                errorMessages.append("第\(itemNumber)项：奖品名称不能为空")
                continue
            }

            // 验证积分消耗
            if item.pointsCost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                errorMessages.append("第\(itemNumber)项：积分消耗不能为空")
                continue
            }

            guard let pointsCost = Int(item.pointsCost), pointsCost > 0 else {
                errorMessages.append("第\(itemNumber)项：积分消耗必须是大于0的数字")
                continue
            }

            // 检查重复名称
            let lowerName = trimmedName.lowercased()
            if let existingIndex = seenNames[lowerName] {
                errorMessages.append("第\(itemNumber)项：奖品名称「\(trimmedName)」与第\(existingIndex)项重复")
                continue
            }
            seenNames[lowerName] = itemNumber

            // 验证通过，添加到有效列表
            validRewards.append(item)
        }

        // 检查是否有有效数据
        if validRewards.isEmpty && errorMessages.isEmpty {
            errorMessages.append("请至少填写一个有效的奖品信息")
        }

        return MemberRewardFormValidationResult(
            isValid: errorMessages.isEmpty && !validRewards.isEmpty,
            validRewards: validRewards,
            errorMessages: errorMessages
        )
    }
}
