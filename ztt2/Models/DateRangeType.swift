//
//  DateRangeType.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation

/**
 * 日期范围类型枚举
 * 支持本周、本月和自定义时间范围
 */
enum DateRangeType: Equatable {
    case thisWeek      // 本周
    case thisMonth     // 本月
    case custom(start: Date, end: Date)  // 自定义范围
    
    /**
     * 从字符串和日期创建DateRangeType
     */
    static func from(string: String, customStart: Date?, customEnd: Date?) -> DateRangeType {
        switch string {
        case "thisWeek":
            return .thisWeek
        case "thisMonth":
            return .thisMonth
        case "custom":
            if let start = customStart, let end = customEnd {
                return .custom(start: start, end: end)
            } else {
                return .thisMonth // 默认为本月
            }
        default:
            return .thisMonth
        }
    }
    
    /**
     * 转换为字符串用于存储
     */
    var stringValue: String {
        switch self {
        case .thisWeek:
            return "thisWeek"
        case .thisMonth:
            return "thisMonth"
        case .custom:
            return "custom"
        }
    }
    
    /**
     * 获取显示文本
     */
    var displayText: String {
        switch self {
        case .thisWeek:
            return "date_range.this_week".localized
        case .thisMonth:
            return "date_range.this_month".localized
        case .custom(let start, let end):
            let formatter = DateFormatter()
            formatter.dateFormat = "M月d日"
            return "\(formatter.string(from: start)) - \(formatter.string(from: end))"
        }
    }
    
    /**
     * 获取实际的日期范围
     */
    var dateRange: (start: Date, end: Date) {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .thisWeek:
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            let endOfWeek = calendar.date(byAdding: .day, value: 6, to: startOfWeek) ?? now
            let endOfDay = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: endOfWeek) ?? endOfWeek
            return (start: startOfWeek, end: endOfDay)
            
        case .thisMonth:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            let endOfMonth = calendar.dateInterval(of: .month, for: now)?.end ?? now
            return (start: startOfMonth, end: endOfMonth)
            
        case .custom(let start, let end):
            // 确保结束时间是当天的23:59:59
            let endOfDay = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: end) ?? end
            return (start: start, end: endOfDay)
        }
    }
    
    /**
     * 自定义范围的开始日期（如果适用）
     */
    var customStartDate: Date? {
        if case .custom(let start, _) = self {
            return start
        }
        return nil
    }
    
    /**
     * 自定义范围的结束日期（如果适用）
     */
    var customEndDate: Date? {
        if case .custom(_, let end) = self {
            return end
        }
        return nil
    }
}
