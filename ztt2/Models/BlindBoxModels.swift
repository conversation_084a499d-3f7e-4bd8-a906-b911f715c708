//
//  BlindBoxModels.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import Foundation

/**
 * 盲盒项目数据模型
 * 用于管理单个盲盒的状态、动画和位置信息
 * 基于ztt1项目的BlindBoxItem模型适配到ztt2项目
 */
struct BlindBoxItem: Identifiable {
    
    // MARK: - Identifiable
    let id = UUID()
    
    // MARK: - Basic Properties
    let index: Int
    let prizeName: String
    
    // MARK: - State Properties
    var isOpened: Bool = false
    var openingProgress: Double = 0.0
    
    // MARK: - Animation Properties
    var position: CGPoint = .zero
    var floatingOffset: CGFloat = 0.0
    var rotationAngle: Double = 0.0
    var explosionState: ExplosionState = .idle
    var scaleEffect: CGFloat = 1.0
    
    // MARK: - Timing Properties
    var floatingDelay: Double {
        return Double(index) * 0.1
    }
    
    var floatingDuration: Double {
        return 0.8 + Double(index % 3) * 0.2
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取盲盒显示标题
     */
    var displayTitle: String {
        return "blind_box.item_title".localized.replacingOccurrences(of: "%d", with: "\(index + 1)")
    }
    
    /**
     * 检查是否可以点击
     */
    var isClickable: Bool {
        return !isOpened && explosionState == .idle
    }
    
    /**
     * 获取当前透明度
     */
    var opacity: Double {
        switch explosionState {
        case .idle:
            return isOpened ? 0.5 : 1.0
        case .exploding:
            return 0.8
        case .completed:
            return 1.0
        }
    }
}

/**
 * 盲盒爆炸动画状态枚举
 */
enum ExplosionState: CaseIterable {
    case idle       // 静止状态
    case exploding  // 爆炸进行中
    case completed  // 爆炸完成
    
    var description: String {
        switch self {
        case .idle:
            return "blind_box.state.idle".localized
        case .exploding:
            return "blind_box.state.exploding".localized
        case .completed:
            return "blind_box.state.completed".localized
        }
    }
}

/**
 * 粒子项目数据模型
 * 用于爆炸效果的粒子系统
 */
struct ParticleItem: Identifiable {
    let id = UUID()
    let startPosition: CGPoint
    var currentPosition: CGPoint
    var velocity: CGPoint
    let color: Color
    let size: CGFloat
    var lifespan: Double
    var age: Double = 0.0
    
    /**
     * 获取当前透明度（基于生命周期）
     */
    var opacity: Double {
        return max(0, 1 - (age / lifespan))
    }
    
    /**
     * 检查粒子是否存活
     */
    var isAlive: Bool {
        return age < lifespan
    }
}

/**
 * 立方体面片数据模型
 * 用于爆炸动画的立方体分解效果
 */
struct CubeFace: Identifiable {
    let id = UUID()
    let type: CubeFaceType
    var offset: CGPoint = .zero
    var rotation: Double = 0.0
    var scale: CGFloat = 1.0
    var velocity: CGPoint = .zero
    var angularVelocity: Double = 0.0
}

/**
 * 立方体面片类型枚举
 */
enum CubeFaceType: CaseIterable {
    case front, back, left, right, top, bottom
    
    var baseColor: Color {
        switch self {
        case .front:
            return Color(hex: "#ff6b6b")
        case .back:
            return Color(hex: "#4ecdc4")
        case .left:
            return Color(hex: "#45b7d1")
        case .right:
            return Color(hex: "#96ceb4")
        case .top:
            return Color(hex: "#feca57")
        case .bottom:
            return Color(hex: "#ff9ff3")
        }
    }
}

// MARK: - BlindBoxItem Extensions

extension BlindBoxItem {
    
    /**
     * 创建新的盲盒项目
     */
    static func create(index: Int, prizeName: String) -> BlindBoxItem {
        return BlindBoxItem(
            index: index,
            prizeName: prizeName
        )
    }
    
    /**
     * 创建已开启状态的盲盒项目（用于开箱后更新显示奖品）
     */
    static func createWithPrize(index: Int, prizeName: String, isOpened: Bool, explosionState: ExplosionState) -> BlindBoxItem {
        var item = BlindBoxItem(
            index: index,
            prizeName: prizeName
        )
        item.isOpened = isOpened
        item.explosionState = explosionState
        item.openingProgress = isOpened ? 1.0 : 0.0
        return item
    }
    
    /**
     * 开始开启动画
     */
    mutating func startOpening() {
        guard isClickable else { return }
        explosionState = .exploding
        openingProgress = 0.0
    }
    
    /**
     * 完成开启动画
     */
    mutating func completeOpening(with prize: String) {
        explosionState = .completed
        openingProgress = 1.0
        isOpened = true
    }
    
    /**
     * 重置动画状态
     */
    mutating func resetAnimation() {
        explosionState = .idle
        openingProgress = 0.0
        scaleEffect = 1.0
        floatingOffset = 0.0
        rotationAngle = 0.0
    }
}

/**
 * 盲盒配置数据模型
 * 用于存储盲盒游戏的配置信息
 */
struct BlindBoxConfig {
    let boxCount: Int
    let costPerOpen: Int
    let prizes: [String]
    let member: Member
    
    /**
     * 验证配置是否有效
     */
    var isValid: Bool {
        return boxCount >= 2 && boxCount <= 20 &&
               costPerOpen > 0 &&
               prizes.count == boxCount &&
               !prizes.contains { $0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
    }
}
