//
//  Color+Extensions.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * Color扩展 - 支持十六进制颜色值
 */
extension Color {
    /**
     * 通过十六进制字符串创建Color
     * @param hex 十六进制颜色值，支持 #RRGGBB 格式
     */
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

/**
 * 预定义的应用颜色
 */
extension Color {
    static let appPrimary = DesignSystem.Colors.primary
    static let appPrimaryLight = DesignSystem.Colors.primaryLight
    static let appSecondary = DesignSystem.Colors.secondary
    static let appBackground = DesignSystem.Colors.background
    static let appTextPrimary = DesignSystem.Colors.textPrimary
    static let appTextSecondary = DesignSystem.Colors.textSecondary
    static let appTextScore = DesignSystem.Colors.textScore
}
