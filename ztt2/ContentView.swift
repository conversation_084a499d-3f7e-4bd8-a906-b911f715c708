//
//  ContentView.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var isLoading = true
    @State private var currentUser: User?

    var body: some View {
        Group {
            if isLoading {
                // 启动加载界面
                VStack(spacing: 20) {
                    Image(systemName: "person.3.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)

                    Text("转团团")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("家庭积分管理助手")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    ProgressView()
                        .scaleEffect(1.2)
                        .padding(.top)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(.systemBackground))
            } else {
                // 主界面
                MainTabView()
                    .environment(\.managedObjectContext, viewContext)
            }
        }
        .onAppear {
            initializeApp()
        }
    }

    private func initializeApp() {
        // 模拟初始化过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // 创建或获取默认用户
            currentUser = PersistenceController.shared.createDefaultUserIfNeeded()

            withAnimation(.easeInOut(duration: 0.5)) {
                isLoading = false
            }

            print("应用初始化完成，用户: \(currentUser?.nickname ?? "未知")")
        }
    }
}



#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
