//
//  Persistence.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import CoreData
import CloudKit

struct PersistenceController {
    static let shared = PersistenceController()

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // 创建示例用户
        let user = User(context: viewContext)
        user.id = UUID()
        user.nickname = "示例用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()

        // 创建示例订阅
        let subscription = Subscription(context: viewContext)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        // 创建示例家庭成员
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        member.memberNumber = 1
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentContainer

    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "ztt2")

        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        } else {
            // 配置持久化存储描述
            if let storeDescription = container.persistentStoreDescriptions.first {
                // 禁用持久化历史记录跟踪，避免权限问题
                storeDescription.setOption(false as NSNumber, forKey: NSPersistentHistoryTrackingKey)
                storeDescription.setOption(false as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

                // 确保自动迁移
                storeDescription.setOption(true as NSNumber, forKey: NSMigratePersistentStoresAutomaticallyOption)
                storeDescription.setOption(true as NSNumber, forKey: NSInferMappingModelAutomaticallyOption)

                // 确保数据库目录存在
                if let storeURL = storeDescription.url {
                    let storeDirectory = storeURL.deletingLastPathComponent()
                    do {
                        try FileManager.default.createDirectory(at: storeDirectory, withIntermediateDirectories: true, attributes: nil)
                        print("Core Data store directory created: \(storeDirectory.path)")
                    } catch {
                        print("Failed to create store directory: \(error)")
                    }
                }

                print("Core Data store URL: \(storeDescription.url?.path ?? "Unknown")")
            }
        }

        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                /*
                 典型的错误原因包括：
                 * 父目录不存在、无法创建或不允许写入
                 * 由于权限或设备锁定时的数据保护，无法访问持久存储
                 * 设备空间不足
                 * 存储无法迁移到当前模型版本
                 * CloudKit 配置问题
                 检查错误消息以确定实际问题
                 */
                print("Core Data error: \(error), \(error.userInfo)")

                // 如果是权限错误，记录错误信息
                if error.code == 513 { // NSCocoaErrorDomain Code=513
                    print("检测到权限错误，可能需要重新安装应用")
                    return
                }

                fatalError("Unresolved error \(error), \(error.userInfo)")
            } else {
                print("Core Data store loaded successfully: \(storeDescription.url?.path ?? "Unknown")")
            }
        })

        // 配置视图上下文
        container.viewContext.automaticallyMergesChangesFromParent = true
    }


}

// MARK: - 数据操作扩展
extension PersistenceController {

    /// 保存上下文
    func save() {
        let context = container.viewContext

        if context.hasChanges {
            do {
                try context.save()
            } catch {
                let nsError = error as NSError
                print("Core Data保存失败: \(nsError.localizedDescription)")

                // 检查是否是文件不存在的错误，尝试重新初始化
                if nsError.code == 134030 || nsError.code == 4 {
                    container.loadPersistentStores { (storeDescription, error) in
                        if error == nil {
                            // 重新尝试保存
                            DispatchQueue.main.async {
                                do {
                                    try context.save()
                                } catch {
                                    print("重新保存失败: \(error.localizedDescription)")
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// 获取当前用户
    func getCurrentUser() -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.fetchLimit = 1

        do {
            let users = try container.viewContext.fetch(request)
            return users.first
        } catch {
            print("Failed to fetch user: \(error)")
            return nil
        }
    }

    /// 创建默认用户（如果不存在）
    func createDefaultUserIfNeeded() -> User {
        if let existingUser = getCurrentUser() {
            return existingUser
        }

        let user = User(context: container.viewContext)
        user.id = UUID()
        user.nickname = "家长"
        user.createdAt = Date()

        // 创建默认订阅
        let subscription = Subscription(context: container.viewContext)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        save()
        return user
    }
}
