//
//  BlindBoxConfigTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒配置功能测试视图
 * 用于测试盲盒配置的保存、加载和验证功能
 */
struct BlindBoxConfigTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var selectedMember: Member?
    @State private var showBlindBoxConfig = false
    @State private var testResults: [String] = []
    @State private var currentStep = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("盲盒配置功能测试")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 成员选择
                memberSelectionSection
                
                // 测试步骤
                testStepsSection
                
                // 测试结果
                testResultsSection
                
                Spacer()
            }
            .padding()
            .navigationTitle("盲盒配置测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .overlay(
            // 盲盒配置弹窗
            BlindBoxConfigPopupView(
                isPresented: $showBlindBoxConfig,
                selectedMember: selectedMember,
                onSave: { configData in
                    handleBlindBoxConfigSave(configData)
                },
                onCancel: {
                    showBlindBoxConfig = false
                    addTestResult("❌ 用户取消了配置")
                }
            )
        )
    }
    
    // MARK: - 子视图
    
    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("1. 选择测试成员")
                .font(.headline)
                .foregroundColor(.primary)
            
            if dataManager.members.isEmpty {
                Text("暂无成员，请先添加成员")
                    .foregroundColor(.secondary)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(dataManager.members, id: \.self) { member in
                            memberCard(member: member)
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
        }
    }
    
    private func memberCard(member: Member) -> some View {
        VStack(spacing: 8) {
            Text(member.name ?? "未知")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary)
            
            Text("积分: \(member.currentPoints)")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedMember == member ? Color.blue.opacity(0.2) : Color.gray.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(selectedMember == member ? Color.blue : Color.clear, lineWidth: 2)
                )
        )
        .onTapGesture {
            selectedMember = member
            currentStep = max(currentStep, 1)
            addTestResult("✅ 选择成员: \(member.name ?? "未知")")
        }
    }
    
    private var testStepsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("2. 测试步骤")
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                testStepButton(
                    title: "打开盲盒配置",
                    step: 2,
                    enabled: selectedMember != nil,
                    action: openBlindBoxConfig
                )
                
                testStepButton(
                    title: "检查现有配置",
                    step: 3,
                    enabled: selectedMember != nil,
                    action: checkExistingConfig
                )
                
                testStepButton(
                    title: "清除配置数据",
                    step: 4,
                    enabled: selectedMember != nil,
                    action: clearConfigData
                )
            }
        }
    }
    
    private func testStepButton(title: String, step: Int, enabled: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(enabled ? .white : .gray)
                
                Spacer()
                
                if currentStep >= step {
                    Image(systemName: "checkmark")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(enabled ? Color.blue : Color.gray.opacity(0.3))
            )
        }
        .disabled(!enabled)
    }
    
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            testResultsHeader
            testResultsList
        }
    }

    private var testResultsHeader: some View {
        HStack {
            Text("3. 测试结果")
                .font(.headline)
                .foregroundColor(.primary)

            Spacer()

            Button("清空") {
                testResults.removeAll()
                currentStep = 0
            }
            .font(.system(size: 14))
            .foregroundColor(.blue)
        }
    }

    private var testResultsList: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 4) {
                ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                    testResultRow(index: index, result: result)
                }
            }
        }
        .frame(maxHeight: 200)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }

    private func testResultRow(index: Int, result: String) -> some View {
        Text("\(index + 1). \(result)")
            .font(.system(size: 12))
            .foregroundColor(.primary)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(4)
    }
    
    // MARK: - 测试方法
    
    private func openBlindBoxConfig() {
        guard let member = selectedMember else { return }
        
        addTestResult("🎯 打开盲盒配置弹窗 - 成员: \(member.name ?? "未知")")
        showBlindBoxConfig = true
        currentStep = max(currentStep, 2)
    }
    
    private func checkExistingConfig() {
        guard let member = selectedMember else { return }
        
        if let config = DataManager.shared.getBlindBoxConfig(for: member) {
            currentStep = max(currentStep, 3)
            addTestResult("📥 找到现有配置: 盲盒数=\(config.itemCount), 积分=\(config.costPerPlay)")
            
            let items = config.allItems
            for item in items {
                addTestResult("   - 盲盒\(item.itemIndex + 1): \(item.formattedPrizeName)")
            }
        } else {
            addTestResult("❌ 未找到现有配置")
        }
    }
    
    private func clearConfigData() {
        guard let member = selectedMember else { return }
        
        if let config = DataManager.shared.getBlindBoxConfig(for: member) {
            // 使用DataManager的公共方法删除配置
            let context = dataManager.persistenceController.container.viewContext
            context.delete(config)
            dataManager.save()
            addTestResult("🗑️ 已清除盲盒配置数据")
            currentStep = max(currentStep, 4)
        } else {
            addTestResult("ℹ️ 没有配置数据需要清除")
        }
    }
    
    private func handleBlindBoxConfigSave(_ configData: BlindBoxConfigData) {
        guard let member = selectedMember else { return }
        
        addTestResult("💾 开始保存配置...")
        addTestResult("   - 盲盒数量: \(configData.boxCount)")
        addTestResult("   - 消耗积分: \(configData.costPerPlay)")
        addTestResult("   - 奖品列表: \(configData.boxPrizes)")
        
        let savedConfig = DataManager.shared.saveBlindBoxConfig(
            for: member,
            boxCount: configData.boxCount,
            costPerPlay: configData.costPerPlay,
            boxPrizes: configData.boxPrizes
        )
        
        showBlindBoxConfig = false
        
        if savedConfig != nil {
            addTestResult("✅ 配置保存成功")
            currentStep = max(currentStep, 3)
        } else {
            addTestResult("❌ 配置保存失败")
        }
    }
    
    private func addTestResult(_ message: String) {
        testResults.append(message)
        print("🧪 盲盒配置测试: \(message)")
    }
}

struct BlindBoxConfigTestView_Previews: PreviewProvider {
    static var previews: some View {
        BlindBoxConfigTestView()
            .environmentObject(DataManager.shared)
    }
}
