//
//  SubscriptionView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 订阅页面视图
 */
struct SubscriptionView: View {
    
    // MARK: - Properties
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 顶部导航栏
            HStack {
                Button(action: onDismiss) {
                    Image(systemName: "xmark")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                Spacer()
                
                Text("会员订阅")
                    .font(.system(
                        size: DesignSystem.Typography.HeadingMedium.fontSize,
                        weight: DesignSystem.Typography.HeadingMedium.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 占位，保持对称
                Color.clear
                    .frame(width: 18, height: 18)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.top, DesignSystem.Spacing.lg)
            
            // 会员特权介绍
            VStack(spacing: DesignSystem.Spacing.lg) {
                // 标题
                VStack(spacing: DesignSystem.Spacing.md) {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 60))
                        .foregroundColor(DesignSystem.Colors.secondary)
                    
                    Text("解锁全部功能")
                        .font(.system(
                            size: DesignSystem.Typography.HeadingLarge.fontSize,
                            weight: DesignSystem.Typography.HeadingLarge.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("升级会员，享受更多特权")
                        .font(.system(
                            size: DesignSystem.Typography.Body.fontSize,
                            weight: DesignSystem.Typography.Body.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                // 功能对比表
                VStack(spacing: DesignSystem.Spacing.md) {
                    FeatureRow(
                        feature: "家庭成员管理",
                        free: true,
                        basic: true,
                        premium: true
                    )
                    
                    FeatureRow(
                        feature: "积分管理",
                        free: true,
                        basic: true,
                        premium: true
                    )
                    
                    FeatureRow(
                        feature: "奖品兑换",
                        free: true,
                        basic: true,
                        premium: true
                    )
                    
                    FeatureRow(
                        feature: "成长日记",
                        free: true,
                        basic: true,
                        premium: true
                    )
                    
                    FeatureRow(
                        feature: "大转盘抽奖",
                        free: false,
                        basic: true,
                        premium: true
                    )
                    
                    FeatureRow(
                        feature: "盲盒/刮刮卡",
                        free: false,
                        basic: false,
                        premium: true
                    )
                    
                    FeatureRow(
                        feature: "AI 分析报告",
                        free: false,
                        basic: false,
                        premium: true
                    )
                    
                    FeatureRow(
                        feature: "多设备同步",
                        free: true,
                        basic: true,
                        premium: true
                    )
                }
                .padding(DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.CornerRadius.medium)
                .padding(.horizontal, DesignSystem.Spacing.md)
            }
            
            Spacer()
            
            // 订阅选项
            VStack(spacing: DesignSystem.Spacing.md) {
                // 初级会员
                SubscriptionOption(
                    title: "初级会员",
                    monthlyPrice: "38元/月",
                    yearlyPrice: "240元/年",
                    features: ["解锁大转盘道具", "支持多设备同步"],
                    isRecommended: false
                ) {
                    // TODO: 实现初级会员订阅
                }
                
                // 高级会员（推荐）
                SubscriptionOption(
                    title: "高级会员",
                    monthlyPrice: "58元/月",
                    yearlyPrice: "388元/年",
                    features: ["解锁所有抽奖道具", "AI 分析功能", "多设备同步"],
                    isRecommended: true
                ) {
                    // TODO: 实现高级会员订阅
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.bottom, DesignSystem.Spacing.lg)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.background)
    }
}

// MARK: - Feature Row Component
private struct FeatureRow: View {
    let feature: String
    let free: Bool
    let basic: Bool
    let premium: Bool
    
    var body: some View {
        HStack {
            Text(feature)
                .font(.system(
                    size: DesignSystem.Typography.Body.fontSize,
                    weight: DesignSystem.Typography.Body.fontWeight
                ))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: DesignSystem.Spacing.lg) {
                Image(systemName: free ? "checkmark.circle.fill" : "xmark.circle")
                    .foregroundColor(free ? DesignSystem.Colors.textPositive : DesignSystem.Colors.textSecondary)
                
                Image(systemName: basic ? "checkmark.circle.fill" : "xmark.circle")
                    .foregroundColor(basic ? DesignSystem.Colors.textPositive : DesignSystem.Colors.textSecondary)
                
                Image(systemName: premium ? "checkmark.circle.fill" : "xmark.circle")
                    .foregroundColor(premium ? DesignSystem.Colors.textPositive : DesignSystem.Colors.textSecondary)
            }
        }
        .padding(.vertical, DesignSystem.Spacing.xs)
    }
}

// MARK: - Subscription Option Component
private struct SubscriptionOption: View {
    let title: String
    let monthlyPrice: String
    let yearlyPrice: String
    let features: [String]
    let isRecommended: Bool
    let onSubscribe: () -> Void
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // 标题和推荐标签
            HStack {
                Text(title)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingMedium.fontSize,
                        weight: DesignSystem.Typography.HeadingMedium.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                if isRecommended {
                    Text("推荐")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, DesignSystem.Spacing.sm)
                        .padding(.vertical, 2)
                        .background(DesignSystem.Colors.primary)
                        .cornerRadius(8)
                }
                
                Spacer()
            }
            
            // 价格
            HStack {
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text(monthlyPrice)
                        .font(.system(
                            size: DesignSystem.Typography.Body.fontSize,
                            weight: DesignSystem.Typography.Body.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(yearlyPrice)
                        .font(.system(
                            size: DesignSystem.Typography.Caption.fontSize,
                            weight: DesignSystem.Typography.Caption.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                Button(action: onSubscribe) {
                    Text("立即订阅")
                        .font(.system(
                            size: DesignSystem.Typography.Body.fontSize,
                            weight: DesignSystem.Typography.Body.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textButton)
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.vertical, DesignSystem.Spacing.sm)
                        .background(isRecommended ? DesignSystem.Colors.primary : DesignSystem.Colors.profileSubscriptionButtonBackground)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                }
            }
            
            // 功能列表
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                ForEach(features, id: \.self) { feature in
                    HStack(spacing: DesignSystem.Spacing.sm) {
                        Image(systemName: "checkmark")
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textPositive)
                        
                        Text(feature)
                            .font(.system(
                                size: DesignSystem.Typography.Caption.fontSize,
                                weight: DesignSystem.Typography.Caption.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Spacer()
                    }
                }
            }
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.cardBackground)
        .cornerRadius(DesignSystem.CornerRadius.medium)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                .stroke(isRecommended ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
        )
    }
}

// MARK: - Preview
#Preview {
    SubscriptionView {
        print("关闭订阅页面")
    }
}
