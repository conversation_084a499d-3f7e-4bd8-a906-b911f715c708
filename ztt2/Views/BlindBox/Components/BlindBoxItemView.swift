//
//  BlindBoxItemView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒单项视图组件
 * 显示单个盲盒的外观、状态和动画效果
 * 基于ztt1项目适配到ztt2项目，兼容iOS15.6以上
 */
struct BlindBoxItemView: View {
    
    // MARK: - Properties
    
    let item: BlindBoxItem
    let size: CGFloat
    let animationState: ExplosionState
    let onTapped: () -> Void
    
    @State private var isPressed = false
    @State private var explosionTriggered = false
    
    var body: some View {
        ZStack {
            // 盲盒主体
            blindBoxBody
            
            // 爆炸动画层
            if animationState == .exploding {
                ExplosionAnimationView(
                    isTriggered: $explosionTriggered,
                    explosionCenter: CGPoint(x: size/2, y: size/2),
                    explosionDuration: 1.2
                )
            }
            
            // 奖品显示层（开启后）
            if item.isOpened {
                prizeDisplayLayer
            }
        }
        .frame(width: size, height: size)
        .scaleEffect(item.scaleEffect * (isPressed ? 0.95 : 1.0))
        .opacity(item.opacity)
        .onTapGesture {
            if item.isClickable {
                onTapped()
            }
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            if item.isClickable {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = pressing
                }
            }
        }, perform: {})
        .onChange(of: animationState) { state in
            if state == .exploding {
                explosionTriggered = true
            }
        }
    }
    
    // MARK: - Blind Box Body
    
    private var blindBoxBody: some View {
        FloatingAnimationView(
            isEnabled: !item.isOpened && animationState == .idle,
            floatingOffset: 6,
            animationDuration: item.floatingDuration,
            animationDelay: item.floatingDelay,
            rotationAngle: 2
        ) {
            ZStack {
                // 盲盒背景
                RoundedRectangle(cornerRadius: 16)
                    .fill(blindBoxGradient)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    colors: [Color.white.opacity(0.3), Color.clear],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                    )
                    .shadow(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
                
                // 盲盒图标
                if !item.isOpened {
                    blindBoxIcon
                } else {
                    // 已开启状态的图标
                    openedBoxIcon
                }
                
                // 盲盒编号
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Text(item.displayTitle)
                            .font(.system(size: 10, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color.black.opacity(0.6))
                            )
                            .padding(.trailing, 8)
                            .padding(.bottom, 8)
                    }
                }
            }
        }
    }
    
    // MARK: - Blind Box Gradient
    
    private var blindBoxGradient: LinearGradient {
        if item.isOpened {
            return LinearGradient(
                colors: [
                    Color(hex: "#95e1d3").opacity(0.8),
                    Color(hex: "#f093fb").opacity(0.6)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                colors: [
                    Color(hex: "#667eea"),
                    Color(hex: "#764ba2")
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    // MARK: - Blind Box Icon
    
    private var blindBoxIcon: some View {
        VStack(spacing: 8) {
            Image("宝箱未打开")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: size * 0.4, height: size * 0.4)
                .foregroundColor(.white)
            
            Text("?")
                .font(.system(size: size * 0.15, weight: .bold))
                .foregroundColor(.white)
        }
    }
    
    // MARK: - Opened Box Icon
    
    private var openedBoxIcon: some View {
        VStack(spacing: 4) {
            Image("宝箱已打开")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: size * 0.35, height: size * 0.35)
                .foregroundColor(.white)
            
            Text("✓")
                .font(.system(size: size * 0.12, weight: .bold))
                .foregroundColor(.white)
        }
    }
    
    // MARK: - Prize Display Layer
    
    private var prizeDisplayLayer: some View {
        VStack {
            Spacer()
            
            // 奖品名称显示
            Text(item.prizeName)
                .font(.system(size: 11, weight: .semibold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .padding(.horizontal, 6)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(hex: "#ff6b6b").opacity(0.9))
                )
                .padding(.bottom, 12)
        }
        .transition(.asymmetric(
            insertion: .scale.combined(with: .opacity),
            removal: .opacity
        ))
    }
}

/**
 * 盲盒网格容器视图
 * 管理盲盒的网格布局和整体动画
 */
struct BlindBoxGridContainerView: View {
    
    @ObservedObject var viewModel: BlindBoxViewModel
    let geometry: GeometryProxy
    let onBoxTapped: (Int) -> Void
    
    private let columns = Array(repeating: GridItem(.flexible(), spacing: 16), count: 3)
    
    var body: some View {
        let availableWidth = geometry.size.width - 40
        let itemSize = (availableWidth - 32) / 3
        
        ScrollView {
            LazyVGrid(columns: columns, spacing: 16) {
                ForEach(Array(viewModel.boxItems.enumerated()), id: \.element.id) { index, item in
                    BlindBoxItemView(
                        item: item,
                        size: itemSize,
                        animationState: viewModel.animationStates[item.id] ?? .idle,
                        onTapped: {
                            onBoxTapped(index)
                        }
                    )
                    .onAppear {
                        // 设置盲盒位置信息（用于粒子效果）
                        let row = index / 3
                        let col = index % 3
                        let x = CGFloat(col) * (itemSize + 16) + itemSize / 2 + 20
                        let y = CGFloat(row) * (itemSize + 16) + itemSize / 2 + 100 // 考虑顶部统计区域
                        
                        viewModel.boxItems[index].position = CGPoint(x: x, y: y)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }
}

/**
 * 盲盒空状态视图
 * 当没有盲盒配置时显示
 */
struct BlindBoxEmptyStateView: View {
    
    let onBackTapped: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // 空状态图标
            Image("宝箱未打开")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 120, height: 120)
                .opacity(0.6)
            
            // 空状态文本
            VStack(spacing: 12) {
                Text("blind_box.empty.title".localized)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("blind_box.empty.message".localized)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            // 返回按钮
            Button(action: onBackTapped) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.left")
                        .font(.system(size: 14, weight: .semibold))
                    
                    Text("blind_box.empty.back_button".localized)
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .frame(width: 140, height: 44)
                .background(
                    LinearGradient(
                        colors: [Color(hex: "#ff6b6b"), Color(hex: "#ee5a52")],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(22)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct BlindBoxItemView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            BlindBoxItemView(
                item: BlindBoxItem.create(index: 0, prizeName: "测试奖品"),
                size: 100,
                animationState: .idle,
                onTapped: {}
            )

            BlindBoxItemView(
                item: BlindBoxItem.createWithPrize(
                    index: 1,
                    prizeName: "已获得奖品",
                    isOpened: true,
                    explosionState: .completed
                ),
                size: 100,
                animationState: .completed,
                onTapped: {}
            )
        }
        .padding()
    }
}
