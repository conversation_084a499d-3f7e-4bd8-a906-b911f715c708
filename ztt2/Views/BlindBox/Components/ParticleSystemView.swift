//
//  ParticleSystemView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 粒子系统组件
 * 实现各种粒子效果，包括庆祝、爆炸、火花等
 * 基于ztt1项目的ParticleSystemView适配到ztt2项目
 */
struct ParticleSystemView: View {
    
    // MARK: - Properties
    let particles: [ParticleItem]
    let isActive: Bool
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                particleView(particle: particle)
            }
        }
        .allowsHitTesting(false) // 粒子不接收触摸事件
    }
    
    // MARK: - Particle View
    
    private func particleView(particle: ParticleItem) -> some View {
        Circle()
            .fill(particle.color)
            .frame(width: particle.size, height: particle.size)
            .position(particle.currentPosition)
            .opacity(particle.opacity)
            .scaleEffect(particle.opacity) // 根据生命周期缩放
            .blur(radius: (1 - particle.opacity) * 2) // 死亡时模糊
    }
}

/**
 * 悬浮动画组件
 * 为盲盒提供轻微的悬浮效果
 */
struct FloatingAnimationView<Content: View>: View {
    
    // MARK: - Properties
    
    let content: Content
    let isEnabled: Bool
    let floatingOffset: CGFloat
    let animationDuration: Double
    let animationDelay: Double
    let rotationAngle: Double
    
    @State private var isFloating = false
    @State private var rotationState: Double = 0
    
    // MARK: - Initializer
    
    init(
        isEnabled: Bool = true,
        floatingOffset: CGFloat = 8,
        animationDuration: Double = 1.0,
        animationDelay: Double = 0,
        rotationAngle: Double = 3,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.isEnabled = isEnabled
        self.floatingOffset = floatingOffset
        self.animationDuration = animationDuration
        self.animationDelay = animationDelay
        self.rotationAngle = rotationAngle
    }
    
    var body: some View {
        content
            .offset(y: isEnabled ? (isFloating ? floatingOffset : -floatingOffset) : 0)
            .rotationEffect(.degrees(isEnabled ? rotationState : 0))
            .onAppear {
                if isEnabled {
                    startFloatingAnimation()
                }
            }
            .onDisappear {
                stopFloatingAnimation()
            }
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始悬浮动画
     */
    private func startFloatingAnimation() {
        // 延迟启动动画，避免所有盲盒同时开始
        DispatchQueue.main.asyncAfter(deadline: .now() + animationDelay) {
            // 垂直浮动动画
            withAnimation(
                .easeInOut(duration: animationDuration)
                .repeatForever(autoreverses: true)
            ) {
                isFloating.toggle()
            }
            
            // 旋转动画（稍微不同的时机）
            withAnimation(
                .easeInOut(duration: animationDuration * 1.2)
                .repeatForever(autoreverses: true)
            ) {
                rotationState = rotationAngle
            }
        }
    }
    
    /**
     * 停止悬浮动画
     */
    private func stopFloatingAnimation() {
        withAnimation(.easeOut(duration: 0.3)) {
            isFloating = false
            rotationState = 0
        }
    }
}

/**
 * 盲盒结果展示动画组件
 * 显示开箱结果的弹窗动画
 */
struct BlindBoxResultAnimationView: View {
    
    // MARK: - Properties
    
    let prizeName: String
    let isPresented: Bool
    let onDismiss: () -> Void
    
    @State private var showResult = false
    @State private var scaleEffect: CGFloat = 0.1
    @State private var rotationAngle: Double = 0
    @State private var celebrationParticles: [ParticleItem] = []
    @State private var particleTimer: Timer?
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissResult()
                }
            
            // 结果卡片
            if showResult {
                resultCard
            }
            
            // 庆祝粒子
            ParticleSystemView(particles: celebrationParticles, isActive: true)
        }
        .opacity(isPresented ? 1 : 0)
        .onAppear {
            if isPresented {
                startPresentationAnimation()
            }
        }
        .onDisappear {
            stopCelebration()
        }
    }
    
    // MARK: - Result Card
    
    private var resultCard: some View {
        VStack(spacing: 20) {
            // 奖品图标
            Image("宝箱已打开")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 80, height: 80)
                .rotationEffect(.degrees(rotationAngle))
            
            // 恭喜文本
            Text("blind_box.result.congratulations".localized)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            // 奖品名称
            Text(prizeName)
                .font(.title)
                .fontWeight(.heavy)
                .foregroundColor(Color(hex: "#ff6b6b"))
                .multilineTextAlignment(.center)
            
            // 确认按钮
            Button(action: dismissResult) {
                Text("blind_box.result.confirm".localized)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            colors: [Color(hex: "#ff6b6b"), Color(hex: "#ee5a52")],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
            }
        }
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
        )
        .scaleEffect(scaleEffect)
        .padding(.horizontal, 40)
    }
    
    // MARK: - Animation Methods
    
    /**
     * 开始展示动画
     */
    private func startPresentationAnimation() {
        // 延迟显示内容
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                showResult = true
                scaleEffect = 1.0
            }
        }
        
        // 旋转入场
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeOut(duration: 0.6)) {
                rotationAngle = 360
            }
        }
        
        // 弹跳效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                bounceEffect()
            }
        }
        
        // 启动庆祝动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            startCelebration()
        }
    }
    
    /**
     * 弹跳效果
     */
    private func bounceEffect() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.5)) {
            scaleEffect = 1.1
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                scaleEffect = 1.0
            }
        }
    }
    
    /**
     * 开始庆祝动画
     */
    private func startCelebration() {
        particleTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            generateCelebrationParticles()
            updateParticles()
        }
    }
    
    /**
     * 停止庆祝动画
     */
    private func stopCelebration() {
        particleTimer?.invalidate()
        particleTimer = nil
        celebrationParticles.removeAll()
    }
    
    /**
     * 生成庆祝粒子
     */
    private func generateCelebrationParticles() {
        let colors: [Color] = [.yellow, .orange, .pink, .purple, .blue, .green]
        
        for _ in 0..<5 {
            let angle = Double.random(in: 0...(2 * .pi))
            let speed = CGFloat.random(in: 100...200)
            let velocity = CGPoint(
                x: CGFloat(cos(angle)) * speed,
                y: CGFloat(sin(angle)) * speed
            )
            
            let particle = ParticleItem(
                startPosition: CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2),
                currentPosition: CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2),
                velocity: velocity,
                color: colors.randomElement() ?? .yellow,
                size: CGFloat.random(in: 4...8),
                lifespan: 2.0
            )
            
            celebrationParticles.append(particle)
        }
    }
    
    /**
     * 更新粒子位置
     */
    private func updateParticles() {
        for i in celebrationParticles.indices.reversed() {
            celebrationParticles[i].age += 0.1
            celebrationParticles[i].currentPosition.x += celebrationParticles[i].velocity.x * 0.1
            celebrationParticles[i].currentPosition.y += celebrationParticles[i].velocity.y * 0.1
            
            // 重力效果
            celebrationParticles[i].velocity.y += 50 * 0.1
            
            // 移除死亡粒子
            if !celebrationParticles[i].isAlive {
                celebrationParticles.remove(at: i)
            }
        }
    }
    
    /**
     * 关闭结果弹窗
     */
    private func dismissResult() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showResult = false
            scaleEffect = 0.1
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            onDismiss()
        }
    }
}

struct BlindBoxResultAnimationView_Previews: PreviewProvider {
    static var previews: some View {
        BlindBoxResultAnimationView(
            prizeName: "超级大奖",
            isPresented: true,
            onDismiss: {}
        )
    }
}
