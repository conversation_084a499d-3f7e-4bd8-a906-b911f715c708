//
//  ExplosionAnimationView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒爆炸动画组件
 * 实现立方体分解、闪光效果、冲击波等视觉效果
 * 基于ztt1项目的ExplosionAnimationView适配到ztt2项目
 */
struct ExplosionAnimationView: View {
    
    // MARK: - Properties
    
    @Binding var isTriggered: Bool
    let explosionCenter: CGPoint
    let explosionDuration: Double
    
    @State private var faces: [CubeFace] = []
    @State private var explosionProgress: Double = 0.0
    @State private var showFlash = false
    @State private var showShockwave = false
    @State private var animationCompleted = false
    
    // MARK: - Initializer
    
    init(isTriggered: Binding<Bool>, 
         explosionCenter: CGPoint = CGPoint(x: 50, y: 50),
         explosionDuration: Double = 1.2) {
        self._isTriggered = isTriggered
        self.explosionCenter = explosionCenter
        self.explosionDuration = explosionDuration
    }
    
    var body: some View {
        ZStack {
            // 立方体面片动画
            ForEach(faces) { face in
                cubeFaceView(face: face)
            }
            
            // 白色闪光效果
            if showFlash {
                flashOverlay
            }
            
            // 冲击波效果
            if showShockwave {
                shockwaveOverlay
            }
        }
        .onChange(of: isTriggered) { triggered in
            if triggered && !animationCompleted {
                startExplosionAnimation()
            }
        }
        .onDisappear {
            resetAnimation()
        }
    }
    
    // MARK: - Cube Face View
    
    private func cubeFaceView(face: CubeFace) -> some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(faceGradient(for: face.type))
            .frame(width: 40, height: 40)
            .rotation3DEffect(
                .degrees(face.rotation),
                axis: (x: 1, y: 1, z: 0)
            )
            .scaleEffect(face.scale)
            .offset(x: face.offset.x, y: face.offset.y)
            .opacity(max(0, 1 - explosionProgress))
    }
    
    /**
     * 面片渐变色
     */
    private func faceGradient(for type: CubeFaceType) -> LinearGradient {
        let baseColor = type.baseColor
        return LinearGradient(
            colors: [
                baseColor,
                baseColor.opacity(0.7)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    /**
     * 闪光效果
     */
    private var flashOverlay: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.white)
            .frame(width: 100, height: 100)
            .opacity(showFlash ? 0.9 : 0.0)
            .scaleEffect(showFlash ? 1.5 : 0.8)
            .position(x: explosionCenter.x, y: explosionCenter.y)
            .animation(.easeOut(duration: 0.15), value: showFlash)
    }
    
    /**
     * 冲击波效果
     */
    private var shockwaveOverlay: some View {
        ZStack {
            // 主冲击波
            Circle()
                .stroke(Color.white.opacity(0.7), lineWidth: 3)
                .scaleEffect(explosionProgress * 4)
                .opacity(1 - explosionProgress)
            
            // 次冲击波
            Circle()
                .stroke(Color.yellow.opacity(0.5), lineWidth: 2)
                .scaleEffect(explosionProgress * 6)
                .opacity(max(0, 0.8 - explosionProgress))
        }
        .position(x: explosionCenter.x, y: explosionCenter.y)
        .animation(.easeOut(duration: explosionDuration), value: explosionProgress)
    }
    
    // MARK: - Animation Methods
    
    /**
     * 开始爆炸动画
     */
    private func startExplosionAnimation() {
        guard !animationCompleted else { return }
        
        // 初始化立方体面片
        generateCubeFaces()
        
        // 动画序列
        executeExplosionSequence()
    }
    
    /**
     * 生成立方体面片
     */
    private func generateCubeFaces() {
        faces = CubeFaceType.allCases.map { type in
            CubeFace(type: type)
        }
    }
    
    /**
     * 执行爆炸动画序列
     */
    private func executeExplosionSequence() {
        // 阶段1: 闪光效果 (0.1秒)
        withAnimation(.easeOut(duration: 0.1)) {
            showFlash = true
        }
        
        // 阶段2: 开始面片飞散 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeOut(duration: 0.3)) {
                self.scatterFaces()
                self.explosionProgress = 0.4
            }
            
            // 显示冲击波
            self.showShockwave = true
        }
        
        // 阶段3: 继续扩散 (0.4秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeInOut(duration: 0.4)) {
                self.continueFaceScattering()
                self.explosionProgress = 0.8
            }
        }
        
        // 阶段4: 消失 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation(.easeIn(duration: 0.3)) {
                self.explosionProgress = 1.0
                self.hideFaces()
            }
        }
        
        // 阶段5: 完成动画
        DispatchQueue.main.asyncAfter(deadline: .now() + explosionDuration) {
            self.completeAnimation()
        }
    }
    
    /**
     * 初始面片飞散
     */
    private func scatterFaces() {
        for i in 0..<faces.count {
            let angle = Double(i) * (360.0 / Double(faces.count)) * .pi / 180.0
            let distance: CGFloat = 60
            
            faces[i].offset = CGPoint(
                x: CGFloat(cos(angle)) * distance,
                y: CGFloat(sin(angle)) * distance
            )
            faces[i].rotation = Double.random(in: 0...360)
            faces[i].scale = 0.8
        }
    }
    
    /**
     * 继续面片飞散
     */
    private func continueFaceScattering() {
        for i in 0..<faces.count {
            let angle = Double(i) * (360.0 / Double(faces.count)) * .pi / 180.0
            let distance: CGFloat = 120
            
            faces[i].offset = CGPoint(
                x: CGFloat(cos(angle)) * distance,
                y: CGFloat(sin(angle)) * distance
            )
            faces[i].rotation += Double.random(in: 180...540)
            faces[i].scale = 0.4
        }
    }
    
    /**
     * 隐藏面片
     */
    private func hideFaces() {
        for i in 0..<faces.count {
            faces[i].scale = 0.0
        }
        showFlash = false
    }
    
    /**
     * 完成动画
     */
    private func completeAnimation() {
        animationCompleted = true
        showShockwave = false
        faces.removeAll()
    }
    
    /**
     * 重置动画
     */
    private func resetAnimation() {
        animationCompleted = false
        explosionProgress = 0.0
        showFlash = false
        showShockwave = false
        faces.removeAll()
    }
}

struct ExplosionAnimationView_Previews: PreviewProvider {
    static var previews: some View {
        ExplosionPreviewWrapper()
    }
}

struct ExplosionPreviewWrapper: View {
    @State private var isTriggered = false

    var body: some View {
        ZStack {
            Color.gray.opacity(0.2)
                .ignoresSafeArea()

            VStack {
                ExplosionAnimationView(
                    isTriggered: $isTriggered,
                    explosionCenter: CGPoint(x: 100, y: 100)
                )
                .frame(width: 200, height: 200)

                Button("触发爆炸") {
                    isTriggered.toggle()
                }
                .padding()
            }
        }
    }
}
