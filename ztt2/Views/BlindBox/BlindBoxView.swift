//
//  BlindBoxView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒游戏主视图
 * 提供完整的盲盒游戏体验，包括网格布局、动画效果、结果展示等
 * 基于ztt1项目的BlindBoxView适配到ztt2项目，兼容iOS15.6以上
 */
struct BlindBoxView: View {
    
    // MARK: - Properties
    
    @StateObject private var viewModel: BlindBoxViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    
    let config: BlindBoxConfig
    let onDismiss: () -> Void
    
    // MARK: - State Properties
    
    @State private var pageAppeared = false
    
    // MARK: - Initializer
    
    init(config: BlindBoxConfig, onDismiss: @escaping () -> Void) {
        self.config = config
        self.onDismiss = onDismiss
        self._viewModel = StateObject(wrappedValue: BlindBoxViewModel(dataManager: DataManager.shared))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 结果弹窗
                if viewModel.showResult {
                    BlindBoxResultAnimationView(
                        prizeName: viewModel.resultPrize,
                        isPresented: viewModel.showResult,
                        onDismiss: {
                            viewModel.confirmResult()
                        }
                    )
                    .zIndex(1000)
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
                
                // 粒子效果层
                ParticleSystemView(particles: viewModel.particles, isActive: true)
                    .zIndex(500)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("blind_box.page_title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadBlindBoxConfig(with: config)
            withAnimation(.easeInOut(duration: 0.8)) {
                pageAppeared = true
            }
        }
        .onDisappear {
            onDismiss()
        }
    }
    
    // MARK: - Background Gradient
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#667eea").opacity(0.1),
                Color(hex: "#764ba2").opacity(0.05),
                Color.white
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Main Content
    
    private func mainContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // 顶部统计信息
            if !viewModel.boxItems.isEmpty {
                BlindBoxStatsView(viewModel: viewModel)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : -30)
                    .animation(.easeInOut(duration: 0.8).delay(0.2), value: pageAppeared)
            }
            
            // 盲盒网格
            if !viewModel.boxItems.isEmpty {
                blindBoxGridView(geometry: geometry)
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : 50)
                    .animation(.easeInOut(duration: 0.8).delay(0.4), value: pageAppeared)
            } else {
                // 空状态视图
                emptyStateView
            }
        }
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image("宝箱未打开")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 120, height: 120)
                .opacity(0.6)
            
            Text("blind_box.empty.title".localized)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
            
            Text("blind_box.empty.message".localized)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button(action: {
                dismiss()
            }) {
                Text("blind_box.empty.back_button".localized)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(width: 120, height: 44)
                    .background(Color(hex: "#ff6b6b"))
                    .cornerRadius(22)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Back Button
    
    private var backButton: some View {
        Button(action: {
            dismiss()
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))
                Text("common.back".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(Color(hex: "#333333"))
        }
    }
    
    // MARK: - Insufficient Points Alert
    
    private var insufficientPointsAlert: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    viewModel.showInsufficientPoints = false
                }
            
            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.orange)
                
                Text("blind_box.insufficient_points.title".localized)
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text("blind_box.insufficient_points.message".localized
                    .replacingOccurrences(of: "%d", with: "\(viewModel.costPerOpen)")
                    .replacingOccurrences(of: "%d", with: "\(viewModel.currentPoints)"))
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Button(action: {
                    viewModel.showInsufficientPoints = false
                }) {
                    Text("common.confirm".localized)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color(hex: "#ff6b6b"))
                        .cornerRadius(25)
                }
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(UIColor.systemBackground))
                    .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
        }
        .zIndex(999)
    }
    
    // MARK: - Blind Box Grid View

    private func blindBoxGridView(geometry: GeometryProxy) -> some View {
        let columns = Array(repeating: GridItem(.flexible(), spacing: 16), count: 3)
        let availableWidth = geometry.size.width - 40 // 左右各20的padding
        let itemSize = (availableWidth - 32) / 3 // 减去间距

        return ScrollView {
            LazyVGrid(columns: columns, spacing: 16) {
                ForEach(Array(viewModel.boxItems.enumerated()), id: \.element.id) { index, item in
                    BlindBoxItemView(
                        item: item,
                        size: itemSize,
                        animationState: viewModel.animationStates[item.id] ?? .idle,
                        onTapped: {
                            handleBoxTapped(at: index)
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }

    // MARK: - Private Methods

    /**
     * 处理盲盒点击事件
     */
    private func handleBoxTapped(at index: Int) {
        let success = viewModel.openBlindBox(at: index)
        if success {
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
    }
}

/**
 * 盲盒统计信息视图
 */
struct BlindBoxStatsView: View {
    
    @ObservedObject var viewModel: BlindBoxViewModel
    
    var body: some View {
        HStack(spacing: 20) {
            // 当前积分
            StatItemView(
                title: "blind_box.stats.current_points".localized,
                value: "\(viewModel.currentPoints)",
                icon: "star.fill",
                color: Color(hex: "#feca57")
            )
            
            // 消耗积分
            StatItemView(
                title: "blind_box.stats.cost_per_open".localized,
                value: "\(viewModel.costPerOpen)",
                icon: "minus.circle.fill",
                color: Color(hex: "#ff6b6b")
            )
            
            // 剩余盲盒
            StatItemView(
                title: "blind_box.stats.remaining".localized,
                value: "\(viewModel.remainingBoxCount)",
                icon: "cube.fill",
                color: Color(hex: "#4ecdc4")
            )
        }
        .padding(.horizontal, 20)
    }
}

/**
 * 统计项视图
 */
struct StatItemView: View {
    
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(color)
                
                Text(value)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.primary)
            }
            
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
}

struct BlindBoxView_Previews: PreviewProvider {
    static var previews: some View {
        let dataManager = DataManager.shared
        let member = dataManager.members.first ?? {
            let newMember = dataManager.createMember(name: "测试成员", role: "son", birthDate: nil, initialPoints: 100)
            return newMember!
        }()

        return NavigationView {
            BlindBoxView(
                config: BlindBoxConfig(
                    boxCount: 6,
                    costPerOpen: 10,
                    prizes: ["奖品1", "奖品2", "奖品3", "奖品4", "奖品5", "奖品6"],
                    member: member
                ),
                onDismiss: {}
            )
        }
        .environmentObject(dataManager)
    }
}
