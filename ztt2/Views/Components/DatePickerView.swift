//
//  DatePickerView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 日期选择器弹窗组件
 * 用于选择出生日期
 */
struct DatePickerView: View {
    
    @Binding var selectedDate: Date
    @Binding var isPresented: Bool
    
    var body: some View {
        ZStack {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)

                VStack {
                    Spacer()

                    // 居中的日期选择器弹窗
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Button("取消") {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                            }
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.gray)

                            Spacer()

                            Text("选择出生日期")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)

                            Spacer()

                            Button("确定") {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    isPresented = false
                                }
                            }
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(hex: "#a9d051"))
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(
                            Color.white
                                .overlay(
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.2))
                                        .frame(height: 0.5),
                                    alignment: .bottom
                                )
                        )

                        // 日期选择器
                        DatePicker(
                            "",
                            selection: $selectedDate,
                            in: ...Date(),
                            displayedComponents: .date
                        )
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .environment(\.locale, Locale(identifier: "zh_CN"))
                        .background(Color.white)
                        .frame(height: 200)
                        .clipped()
                    }
                    .background(Color.white)
                    .cornerRadius(16)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 0)
                    .padding(.horizontal, 40)

                    Spacer()
                }
                .transition(.scale.combined(with: .opacity))
            }
        }
    }
}

// MARK: - View Extensions
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - Preview
#Preview {
    DatePickerView(
        selectedDate: .constant(Date()),
        isPresented: .constant(true)
    )
}
