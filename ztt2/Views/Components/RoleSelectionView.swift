//
//  RoleSelectionView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 角色选择弹窗组件
 * 用于选择家庭成员角色
 */
struct RoleSelectionView: View {
    
    @Binding var selectedRole: String
    @Binding var isPresented: Bool
    
    private let roles = [
        ("son", "儿子", "男生头像"),
        ("daughter", "女儿", "女生头像"),
        ("father", "爸爸", "爸爸头像"),
        ("mother", "妈妈", "妈妈头像"),
        ("other", "其他", "其他头像")
    ]
    
    var body: some View {
        ZStack {
            // 半透明背景
            if isPresented {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isPresented = false
                        }
                    }

                // 选择菜单
                VStack(spacing: 0) {
                    // 标题
                    HStack {
                        Text("选择家庭角色")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        Spacer()

                        But<PERSON>(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)

                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)

                    // 角色选项列表
                    ScrollView {
                        VStack(spacing: 0) {
                            ForEach(roles, id: \.0) { role in
                                RoleOptionRow(
                                    roleKey: role.0,
                                    roleName: role.1,
                                    roleIcon: role.2,
                                    isSelected: selectedRole == role.0,
                                    onTap: {
                                        selectedRole = role.0
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            isPresented = false
                                        }
                                    }
                                )
                            }
                        }
                    }
                    .frame(maxHeight: 300)
                }
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
                .padding(.horizontal, 40)
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isPresented)
    }
}

/**
 * 角色选项行组件
 */
struct RoleOptionRow: View {
    
    let roleKey: String
    let roleName: String
    let roleIcon: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 角色头像
                Image(roleIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                
                // 角色名称
                Text(roleName)
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 选中状态指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18))
                        .foregroundColor(Color(hex: "#a9d051"))
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(isSelected ? Color(hex: "#f8fff2") : Color.clear)
        }
        .buttonStyle(PlainButtonStyle())

        // 分隔线
        if roleKey != "other" {
            Rectangle()
                .fill(Color(hex: "#f0f0f0"))
                .frame(height: 0.5)
                .padding(.horizontal, 20)
        }
    }
}

// MARK: - Preview
#Preview {
    RoleSelectionView(
        selectedRole: .constant("son"),
        isPresented: .constant(true)
    )
}
