//
//  AddMemberFormView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 添加成员表单弹窗组件
 * 用于首页中点击"添加成员"按钮后弹出的表单弹窗
 */
struct AddMemberFormView: View {
    
    @Binding var isPresented: Bool
    @State private var memberName: String = ""
    @State private var initialPoints: String = "0"
    @State private var selectedRole: String = "son"
    @State private var birthDate: Date = Date()
    @State private var animationTrigger = false
    @State private var showValidationErrors = false
    @State private var isSubmitting = false
    @State private var showRoleSelection = false
    @State private var showDatePicker = false
    
    let onSubmit: (MemberFormData) -> Void
    let onCancel: () -> Void
    
    // MARK: - 表单验证
    private var validationResult: (isValid: Bool, errors: [String]) {
        var errors: [String] = []
        
        // 验证姓名
        let trimmedName = memberName.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedName.isEmpty {
            errors.append("请输入成员姓名")
        } else if trimmedName.count > 20 {
            errors.append("姓名长度不能超过20个字符")
        }
        
        // 验证初始积分
        if let points = Int(initialPoints), points < 0 {
            errors.append("初始积分不能为负数")
        } else if let points = Int(initialPoints), points > 1000 {
            errors.append("初始积分不能超过1000")
        } else if !initialPoints.isEmpty && Int(initialPoints) == nil {
            errors.append("请输入有效的积分数值")
        }
        
        return (errors.isEmpty, errors)
    }
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        if !isSubmitting {
                            closeForm()
                        }
                    }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text("添加成员")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            Button(action: {
                                if !isSubmitting {
                                    closeForm()
                                }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                            .disabled(isSubmitting)
                        }
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 表单内容
                        ScrollView(.vertical, showsIndicators: false) {
                            VStack(spacing: 20) {
                                // 姓名输入
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("成员姓名")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(DesignSystem.Colors.textPrimary)
                                    
                                    TextField("请输入成员姓名", text: $memberName)
                                        .textFieldStyle(CustomTextFieldStyle())
                                        .disabled(isSubmitting)
                                }
                                
                                // 初始积分输入
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("初始积分")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(DesignSystem.Colors.textPrimary)
                                    
                                    TextField("0", text: $initialPoints)
                                        .keyboardType(.numberPad)
                                        .textFieldStyle(CustomTextFieldStyle())
                                        .disabled(isSubmitting)
                                }
                                
                                // 角色选择
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("家庭角色")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(DesignSystem.Colors.textPrimary)
                                    
                                    Button(action: {
                                        showRoleSelection = true
                                    }) {
                                        HStack {
                                            Text(getRoleDisplayName(selectedRole))
                                                .font(.system(size: 14))
                                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                            
                                            Spacer()
                                            
                                            Image(systemName: "chevron.down")
                                                .font(.system(size: 12))
                                                .foregroundColor(.gray)
                                        }
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 10)
                                        .background(Color.white)
                                        .cornerRadius(8)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                        )
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                    .disabled(isSubmitting)
                                }
                                
                                // 出生日期选择
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("出生日期")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(DesignSystem.Colors.textPrimary)
                                    
                                    Button(action: {
                                        showDatePicker = true
                                    }) {
                                        HStack {
                                            Text(formatDate(birthDate))
                                                .font(.system(size: 14))
                                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                            
                                            Spacer()
                                            
                                            Image(systemName: "calendar")
                                                .font(.system(size: 12))
                                                .foregroundColor(.gray)
                                        }
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 10)
                                        .background(Color.white)
                                        .cornerRadius(8)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                        )
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                    .disabled(isSubmitting)
                                }
                                
                                // 验证错误显示
                                if showValidationErrors && !validationResult.isValid {
                                    VStack(alignment: .leading, spacing: 4) {
                                        ForEach(validationResult.errors, id: \.self) { error in
                                            HStack {
                                                Image(systemName: "exclamationmark.triangle.fill")
                                                    .foregroundColor(.red)
                                                    .font(.system(size: 12))
                                                Text(error)
                                                    .font(.system(size: 12))
                                                    .foregroundColor(.red)
                                                Spacer()
                                            }
                                        }
                                    }
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(Color.red.opacity(0.1))
                                    .cornerRadius(8)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                        }
                        .frame(maxHeight: geometry.size.height * 0.5)
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                            .padding(.horizontal, 24)
                        
                        // 底部按钮区域
                        HStack(spacing: 16) {
                            // 取消按钮
                            Button(action: {
                                if !isSubmitting {
                                    closeForm()
                                }
                            }) {
                                Text("取消")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(Color(hex: "#666666"))
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color(hex: "#f5f5f5"))
                                    )
                            }
                            .disabled(isSubmitting)
                            
                            // 提交按钮
                            Button(action: submitForm) {
                                HStack {
                                    if isSubmitting {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    }
                                    Text(isSubmitting ? "添加中..." : "确定添加")
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [
                                                    Color(hex: "#a9d051"),
                                                    Color(hex: "#8bc34a")
                                                ]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                )
                            }
                            .disabled(isSubmitting)
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 20)
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.white)
                            .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                    )
                    .frame(maxWidth: min(geometry.size.width - 40, 400))
                    .frame(maxHeight: geometry.size.height * 0.85)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                    .scaleEffect(animationTrigger ? 1.0 : 0.8)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: animationTrigger)
                }
            }
        }
        .overlay(
            // 角色选择弹窗
            RoleSelectionView(
                selectedRole: $selectedRole,
                isPresented: $showRoleSelection
            )
        )
        .overlay(
            // 日期选择器
            DatePickerView(
                selectedDate: $birthDate,
                isPresented: $showDatePicker
            )
        )
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                resetForm()
            } else {
                animationTrigger = false
            }
        }
    }

    // MARK: - Private Methods

    /**
     * 提交表单
     */
    private func submitForm() {
        showValidationErrors = true

        let validation = validationResult
        guard validation.isValid else { return }

        isSubmitting = true

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let memberData = MemberFormData(
                name: memberName.trimmingCharacters(in: .whitespacesAndNewlines),
                initialPoints: initialPoints,
                role: selectedRole,
                gender: getGenderFromRole(selectedRole),
                birthDate: birthDate
            )

            onSubmit(memberData)
            isSubmitting = false
        }
    }

    /**
     * 关闭表单
     */
    private func closeForm() {
        isPresented = false
        onCancel()
    }

    /**
     * 重置表单状态
     */
    private func resetForm() {
        memberName = ""
        initialPoints = "0"
        selectedRole = "son"
        birthDate = Date()
        showValidationErrors = false
        isSubmitting = false
    }

    /**
     * 获取角色显示名称
     */
    private func getRoleDisplayName(_ role: String) -> String {
        switch role {
        case "son": return "儿子"
        case "daughter": return "女儿"
        case "father": return "爸爸"
        case "mother": return "妈妈"
        case "other": return "其他"
        default: return "儿子"
        }
    }

    /**
     * 根据角色获取性别
     */
    private func getGenderFromRole(_ role: String) -> String {
        switch role {
        case "son", "father": return "male"
        case "daughter", "mother": return "female"
        default: return "male"
        }
    }

    /**
     * 格式化日期显示
     */
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale.current
        return formatter.string(from: date)
    }
}

// MARK: - 成员表单数据模型
struct MemberFormData {
    var name: String = ""
    var initialPoints: String = "0"
    var role: String = "son"
    var gender: String = "male"
    var birthDate: Date = Date()

    var formattedName: String {
        return name.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    var initialPointsValue: Int {
        return Int(initialPoints) ?? 0
    }

    var age: Int {
        let calendar = Calendar.current
        let now = Date()
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: now)
        return ageComponents.year ?? 0
    }
}

// MARK: - 自定义文本框样式
struct CustomTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color.white)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()

        AddMemberFormView(
            isPresented: .constant(true),
            onSubmit: { memberData in
                print("提交成员: \(memberData.name)")
            },
            onCancel: {
                print("取消添加")
            }
        )
    }
}
