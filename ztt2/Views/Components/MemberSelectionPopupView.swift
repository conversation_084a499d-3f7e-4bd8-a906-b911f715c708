//
//  MemberSelectionPopupView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 成员选择弹窗组件
 * 用于抽奖配置时选择要配置的成员
 */
struct MemberSelectionPopupView: View {
    
    @Binding var isPresented: Bool
    let members: [Member]
    let onMemberSelected: (Member) -> Void
    
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 弹窗内容
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("选择成员")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                        
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(Color.white)
                    
                    // 分割线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                    
                    // 成员列表
                    if members.isEmpty {
                        // 空状态
                        VStack(spacing: 12) {
                            Image(systemName: "person.2.slash")
                                .font(.system(size: 40))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("暂无成员")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("请先添加家庭成员")
                                .font(.system(size: 14))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.vertical, 40)
                        .background(Color.white)
                    } else {
                        ScrollView {
                            LazyVStack(spacing: 0) {
                                ForEach(members, id: \.id) { member in
                                    MemberSelectionRow(
                                        member: member,
                                        action: {
                                            onMemberSelected(member)
                                        }
                                    )
                                    
                                    if member.id != members.last?.id {
                                        Rectangle()
                                            .fill(Color(hex: "#edf5d9"))
                                            .frame(height: 1)
                                            .padding(.horizontal, 20)
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 300)
                        .background(Color.white)
                    }
                }
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
                .padding(.horizontal, 40)
                .scaleEffect(animationTrigger ? 1.0 : 0.8)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .onAppear {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                        animationTrigger = true
                    }
                }
                .onDisappear {
                    animationTrigger = false
                }
            }
        }
    }
}

/**
 * 成员选择行组件
 */
struct MemberSelectionRow: View {
    
    let member: Member
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 成员头像
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(hex: "#a9d051").opacity(0.8),
                                    Color(hex: "#7fb83d")
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 44, height: 44)
                    
                    Text(String(member.name?.prefix(1) ?? "?"))
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                // 成员信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(member.name ?? "未知成员")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    HStack(spacing: 8) {
                        Text(getRoleDisplayName(member.role ?? ""))
                            .font(.system(size: 14))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text("•")
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text("\(member.currentPoints)积分")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color(hex: "#a9d051"))
                    }
                }
                
                Spacer()
                
                // 箭头图标
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                Color.white
                    .overlay(
                        isPressed ? Color.black.opacity(0.05) : Color.clear
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
    
    /**
     * 获取角色显示名称
     */
    private func getRoleDisplayName(_ role: String) -> String {
        switch role {
        case "father":
            return "爸爸"
        case "mother":
            return "妈妈"
        case "son":
            return "儿子"
        case "daughter":
            return "女儿"
        case "grandfather":
            return "爷爷"
        case "grandmother":
            return "奶奶"
        case "other":
            return "其他"
        default:
            return "成员"
        }
    }
}

#Preview {
    MemberSelectionPopupView(
        isPresented: .constant(true),
        members: [],
        onMemberSelected: { _ in }
    )
}
