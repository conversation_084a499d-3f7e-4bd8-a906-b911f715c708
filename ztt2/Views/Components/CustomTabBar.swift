//
//  CustomTabBar.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//  液态融球效果TabBar
//

import SwiftUI

/**
 * 自定义底部导航栏 - 液态融球效果
 */
struct CustomTabBar: View {
    
    @Binding var selectedIndex: Int
    let onTabSelected: (Int) -> Void
    
    // Tab项数据
    private let tabItems: [TabItem] = [
        TabItem(index: 0, title: "tab.home".localized, iconName: "shouye1_1", selectedIconName: "shouye1_1"),
        TabItem(index: 1, title: "tab.diary".localized, iconName: "chengzhang", selectedIconName: "录音"),
        TabItem(index: 2, title: "tab.profile".localized, iconName: "wode13", selectedIconName: "wode13")
    ]
    
    // 动画状态
    @State private var transitionProgress: CGFloat = 1.0
    
    var body: some View {
        GeometryReader { geometry in
            let tabWidth = geometry.size.width / CGFloat(tabItems.count)
            let bubbleCenter = tabWidth * (CGFloat(selectedIndex) + 0.5)
            
            VStack(spacing: 0) {
                // 导航栏主体部分
                ZStack {
                    // 液态背景
                    LiquidTabBarBackground(
                        bubbleCenter: bubbleCenter,
                        transitionProgress: transitionProgress
                    )
                    
                    // 融球效果
                    LiquidTabBarBubble(
                        centerX: bubbleCenter,
                        selectedIcon: tabItems[selectedIndex].selectedIconName
                    )
                    
                    // Tab按钮（仅显示未选中的图标）
                    HStack(spacing: 0) {
                        ForEach(tabItems, id: \.index) { item in
                            TabBarButton(
                                item: item,
                                isSelected: selectedIndex == item.index,
                                tabWidth: tabWidth
                            ) {
                                selectTab(item.index)
                            }
                            .frame(maxWidth: .infinity)
                        }
                    }
                    .padding(.horizontal, DesignSystem.Spacing.md)
                }
                .frame(height: DesignSystem.LiquidTabBar.height)
                // 移除.clipped()以保留上拱部分的边框显示
                
                // 底部安全区域填充（与导航栏背景色一致）
                Rectangle()
                    .fill(DesignSystem.LiquidTabBar.backgroundColor)
                    .frame(height: max(0, geometry.size.height - DesignSystem.LiquidTabBar.height))
            }
        }
        .frame(maxWidth: .infinity) // 确保占满屏幕宽度
    }
    
    // MARK: - Private Methods
    
    private func selectTab(_ index: Int) {
        guard index != selectedIndex else { return }
        
        // 开始动画
        withAnimation(.spring(response: DesignSystem.LiquidTabBar.animationResponse, dampingFraction: DesignSystem.LiquidTabBar.animationDamping, blendDuration: 0)) {
            selectedIndex = index
        }
        
        // 调用回调
        onTabSelected(index)
    }
}

/**
 * Tab项数据结构
 */
private struct TabItem {
    let index: Int
    let title: String
    let iconName: String
    let selectedIconName: String
}

/**
 * Tab按钮组件 - 仅显示未选中的图标
 */
private struct TabBarButton: View {
    
    let item: TabItem
    let isSelected: Bool
    let tabWidth: CGFloat
    let onTapped: () -> Void
    
    var body: some View {
        Button(action: onTapped) {
            // 只显示未选中的图标
            if !isSelected {
                Image(item.iconName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: DesignSystem.LiquidTabBar.bubbleIconSize, height: DesignSystem.LiquidTabBar.bubbleIconSize)
                    .foregroundColor(DesignSystem.LiquidTabBar.inactiveIconColor)
                    .opacity(0.8)
                    // 为个人中心图标（index=2）添加向左偏移以实现对称效果
                    .offset(x: item.index == 2 ? DesignSystem.LiquidTabBar.rightIconOffsetX : 0)
            } else {
                // 选中时显示透明占位，避免布局跳动
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 24, height: 24)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(width: tabWidth, height: DesignSystem.LiquidTabBar.height)
    }
}

// MARK: - Preview Container
private struct CustomTabBarPreviewContainer: View {
    @State private var selectedIndex = 0
    
    var body: some View {
        VStack {
            Spacer()
            
            Text(String(format: "tab.current_selection_format".localized, selectedIndex))
                .font(.title)
                .padding()
            
            Spacer()
            
            CustomTabBar(selectedIndex: $selectedIndex) { index in
                print("选中Tab: \(index)")
            }
        }
        .background(DesignSystem.Colors.background)
    }
}

// MARK: - Preview
#Preview {
    CustomTabBarPreviewContainer()
}
