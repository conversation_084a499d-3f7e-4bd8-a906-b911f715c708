//
//  MemberDetailTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 成员详情页测试视图
 * 用于测试和预览成员详情页的各种状态
 */
struct MemberDetailTestView: View {
    
    @State private var showMemberDetail = false
    @State private var selectedMemberData: MemberTestData = .duoduo
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("成员详情页测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()
                
                VStack(spacing: 16) {
                    Text("选择测试成员:")
                        .font(.headline)
                    
                    ForEach(MemberTestData.allCases, id: \.self) { member in
                        Button(action: {
                            selectedMemberData = member
                            showMemberDetail = true
                        }) {
                            HStack {
                                Image(member.avatarImageName)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 40, height: 40)
                                    .clipShape(Circle())
                                
                                VStack(alignment: .leading) {
                                    Text(member.name)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.primary)
                                    
                                    Text("\(member.role) · \(member.age)岁 · \(member.points)积分")
                                        .font(.system(size: 14))
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("测试页面")
        }
        .fullScreenCover(isPresented: $showMemberDetail) {
            MemberDetailTestWrapper(memberData: selectedMemberData) {
                showMemberDetail = false
            }
        }
    }
}

/**
 * 成员详情页测试包装器
 * 使用测试数据创建成员详情页
 */
struct MemberDetailTestWrapper: View {
    let memberData: MemberTestData
    let onClose: () -> Void
    
    var body: some View {
        // 创建一个自定义的成员详情页，使用测试数据
        GeometryReader { geometry in
            ZStack {
                // 美化背景渐变
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "#fcfff4"), location: 0.0),
                        .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                        .init(color: Color.white, location: 0.7),
                        .init(color: Color(hex: "#fafffe"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(.all)
                
                VStack(spacing: 0) {
                    // 顶部关闭按钮
                    MemberDetailHeader {
                        onClose()
                    }
                    
                    // 主要内容区域
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // 成员信息卡片
                        MemberInfoCard(
                            memberName: memberData.name,
                            memberRole: memberData.role,
                            memberAge: memberData.age,
                            currentPoints: memberData.points,
                            onAddPointsTapped: {
                                print("加分按钮点击 - \(memberData.name)")
                            },
                            onDeductPointsTapped: {
                                print("扣分按钮点击 - \(memberData.name)")
                            },
                            onExchangeTapped: {
                                print("兑换按钮点击 - \(memberData.name)")
                            },
                            onLotteryTapped: {
                                print("抽奖按钮点击 - \(memberData.name)")
                            },
                            onAnalysisReportTapped: {
                                print("AI分析按钮点击 - \(memberData.name)")
                            }
                        )
                        .padding(.horizontal, 25)
                        
                        // 历史记录组件 - 暂时注释掉，因为需要真实的ViewModel
                        // MemberHistoryRecordsView(selectedRecordType: .constant(.points), viewModel: viewModel)
                            .padding(.horizontal, 25)
                        
                        Spacer()
                    }
                    .padding(.top, DesignSystem.Spacing.sm)
                }
            }
        }
        .navigationBarHidden(true)
    }
}

/**
 * 测试用成员数据
 */
enum MemberTestData: CaseIterable {
    case duoduo
    case xiaoming
    case mama
    case baba
    case other
    
    var name: String {
        switch self {
        case .duoduo: return "多多"
        case .xiaoming: return "小明"
        case .mama: return "妈妈"
        case .baba: return "爸爸"
        case .other: return "其他"
        }
    }
    
    var role: String {
        switch self {
        case .duoduo: return "儿子"
        case .xiaoming: return "儿子"
        case .mama: return "妈妈"
        case .baba: return "爸爸"
        case .other: return "其他"
        }
    }
    
    var age: Int {
        switch self {
        case .duoduo: return 9
        case .xiaoming: return 8
        case .mama: return 35
        case .baba: return 38
        case .other: return 25
        }
    }
    
    var points: Int {
        switch self {
        case .duoduo: return 10
        case .xiaoming: return 85
        case .mama: return 120
        case .baba: return 95
        case .other: return 50
        }
    }
    
    var avatarImageName: String {
        switch self {
        case .duoduo, .xiaoming: return "男生头像"
        case .mama: return "妈妈头像"
        case .baba: return "爸爸头像"
        case .other: return "其他头像"
        }
    }
}

// MARK: - Preview
#Preview {
    MemberDetailTestView()
}
