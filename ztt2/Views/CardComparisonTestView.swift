//
//  CardComparisonTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 卡片对比测试视图
 * 用于对比修改前后的成员卡片样式
 */
struct CardComparisonTestView: View {
    
    var body: some View {
        ScrollView {
            VStack(spacing: 30) {
                
                // 标题
                Text("成员卡片样式对比")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.top, 20)
                
                // 修改后的样式（当前项目）
                VStack(alignment: .leading, spacing: 16) {
                    Text("修改后的样式（当前项目）")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        FamilyMemberCardView(
                            memberName: "爸爸",
                            memberRole: "father",
                            currentPoints: 0,
                            memberIndex: 1,
                            isDeleteMode: false,
                            onLongPress: {},
                            onDeleteTapped: {},
                            onCardTapped: {}
                        )
                        
                        FamilyMemberCardView(
                            memberName: "妈妈",
                            memberRole: "mother",
                            currentPoints: 0,
                            memberIndex: 2,
                            isDeleteMode: false,
                            onLongPress: {},
                            onDeleteTapped: {},
                            onCardTapped: {}
                        )
                        
                        FamilyMemberCardView(
                            memberName: "多多",
                            memberRole: "son",
                            currentPoints: 10,
                            memberIndex: 3,
                            isDeleteMode: false,
                            onLongPress: {},
                            onDeleteTapped: {},
                            onCardTapped: {}
                        )
                        
                        FamilyMemberCardView(
                            memberName: "朵朵",
                            memberRole: "daughter",
                            currentPoints: 5,
                            memberIndex: 4,
                            isDeleteMode: false,
                            onLongPress: {},
                            onDeleteTapped: {},
                            onCardTapped: {}
                        )
                    }
                }
                .padding(.horizontal, 20)
                
                Divider()
                    .padding(.horizontal, 20)
                
                // 样式说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("修改内容说明")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("卡片高度：从140调整为100，与参考项目一致")
                                .font(.caption)
                        }
                        
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("头像大小：调整为更小的尺寸，适配新的卡片高度")
                                .font(.caption)
                        }
                        
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("字体大小：姓名和积分字体调整为更合适的大小")
                                .font(.caption)
                        }
                        
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("布局位置：优化各元素的相对位置")
                                .font(.caption)
                        }
                        
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("文字效果：添加阴影效果，提升视觉层次")
                                .font(.caption)
                        }
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer(minLength: 50)
            }
        }
        .background(DesignSystem.Colors.background)
        .navigationTitle("样式对比")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        CardComparisonTestView()
    }
}
