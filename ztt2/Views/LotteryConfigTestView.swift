//
//  LotteryConfigTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖配置测试页面
 * 用于测试成员选择和抽奖道具选择弹窗
 */
struct LotteryConfigTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var showMemberSelectionPopup = false
    @State private var showLotteryToolSelectionPopup = false
    @State private var selectedMemberForLottery: Member?
    @State private var selectedToolType: LotteryConfig.ToolType?
    @State private var resultMessage = ""

    // 抽奖道具配置弹窗状态
    @State private var showWheelConfigPopup = false
    @State private var showBlindBoxConfigPopup = false
    @State private var showScratchCardConfigPopup = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                
                // 标题
                Text("抽奖配置测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                // 当前状态显示
                VStack(spacing: 16) {
                    if let member = selectedMemberForLottery {
                        HStack {
                            Text("已选择成员:")
                                .font(.headline)
                            Text(member.name ?? "未知成员")
                                .font(.headline)
                                .foregroundColor(Color(hex: "#a9d051"))
                        }
                    }
                    
                    if let toolType = selectedToolType {
                        HStack {
                            Text("已选择道具:")
                                .font(.headline)
                            Text(toolType.displayName)
                                .font(.headline)
                                .foregroundColor(Color(hex: "#a9d051"))
                        }
                    }
                    
                    if !resultMessage.isEmpty {
                        Text(resultMessage)
                            .font(.body)
                            .foregroundColor(.blue)
                            .multilineTextAlignment(.center)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                // 测试按钮
                VStack(spacing: 16) {
                    Button("开始配置抽奖道具") {
                        selectedMemberForLottery = nil
                        selectedToolType = nil
                        resultMessage = ""
                        showMemberSelectionPopup = true
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(hex: "#a9d051"))
                    .cornerRadius(12)
                    
                    Button("重置") {
                        selectedMemberForLottery = nil
                        selectedToolType = nil
                        resultMessage = ""
                    }
                    .font(.headline)
                    .foregroundColor(Color(hex: "#a9d051"))
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(hex: "#a9d051").opacity(0.1))
                    .cornerRadius(12)
                }
                
                // 成员列表状态
                VStack(alignment: .leading, spacing: 8) {
                    Text("当前成员列表:")
                        .font(.headline)
                    
                    if dataManager.members.isEmpty {
                        Text("暂无成员，请先添加成员")
                            .foregroundColor(.gray)
                    } else {
                        ForEach(dataManager.members, id: \.id) { member in
                            HStack {
                                Text("• \(member.name ?? "未知成员")")
                                Spacer()
                                Text("\(member.currentPoints)积分")
                                    .foregroundColor(Color(hex: "#a9d051"))
                            }
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.05))
                .cornerRadius(8)
                
                Spacer()
            }
            .padding()
            .navigationTitle("抽奖配置测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        // 成员选择弹窗
        .overlay(
            MemberSelectionPopupView(
                isPresented: $showMemberSelectionPopup,
                members: dataManager.members,
                onMemberSelected: { member in
                    selectedMemberForLottery = member
                    showMemberSelectionPopup = false
                    showLotteryToolSelectionPopup = true
                    resultMessage = "已选择成员: \(member.name ?? "未知成员")"
                }
            )
        )
        // 抽奖道具选择弹窗
        .overlay(
            LotteryToolSelectionPopupView(
                isPresented: $showLotteryToolSelectionPopup,
                selectedMember: selectedMemberForLottery,
                onToolSelected: { member, toolType in
                    selectedToolType = toolType
                    showLotteryToolSelectionPopup = false
                    handleLotteryToolSelected(member: member, toolType: toolType)
                }
            )
        )
        // 大转盘配置弹窗
        .overlay(
            WheelConfigPopupView(
                isPresented: $showWheelConfigPopup,
                selectedMember: selectedMemberForLottery,
                onSave: { configData in
                    handleWheelConfigSave(configData)
                },
                onCancel: {
                    showWheelConfigPopup = false
                }
            )
        )
        // 盲盒配置弹窗
        .overlay(
            BlindBoxConfigPopupView(
                isPresented: $showBlindBoxConfigPopup,
                selectedMember: selectedMemberForLottery,
                onSave: { configData in
                    handleBlindBoxConfigSave(configData)
                },
                onCancel: {
                    showBlindBoxConfigPopup = false
                }
            )
        )
        // 刮刮卡配置弹窗
        .overlay(
            ScratchCardConfigPopupView(
                isPresented: $showScratchCardConfigPopup,
                selectedMember: selectedMemberForLottery,
                onSave: { configData in
                    handleScratchCardConfigSave(configData)
                },
                onCancel: {
                    showScratchCardConfigPopup = false
                }
            )
        )
    }

    // MARK: - 抽奖配置处理方法

    /**
     * 处理抽奖道具选择
     */
    private func handleLotteryToolSelected(member: Member, toolType: LotteryConfig.ToolType) {
        selectedMemberForLottery = member

        switch toolType {
        case .wheel:
            showWheelConfigPopup = true
        case .blindbox:
            showBlindBoxConfigPopup = true
        case .scratchcard:
            showScratchCardConfigPopup = true
        }

        resultMessage = "为成员 \(member.name ?? "未知成员") 选择了 \(toolType.displayName)"
    }

    /**
     * 处理大转盘配置保存
     */
    private func handleWheelConfigSave(_ configData: WheelConfigData) {
        showWheelConfigPopup = false
        resultMessage = """
        大转盘配置已保存：
        分区数量：\(configData.sectorCount)
        消耗积分：\(configData.costPerPlay)
        奖品：\(configData.sectorPrizes.joined(separator: "、"))
        """
    }

    /**
     * 处理盲盒配置保存
     */
    private func handleBlindBoxConfigSave(_ configData: BlindBoxConfigData) {
        showBlindBoxConfigPopup = false
        resultMessage = """
        盲盒配置已保存：
        盲盒数量：\(configData.boxCount)
        消耗积分：\(configData.costPerPlay)
        奖品：\(configData.boxPrizes.joined(separator: "、"))
        """
    }

    /**
     * 处理刮刮卡配置保存
     */
    private func handleScratchCardConfigSave(_ configData: ScratchCardConfigData) {
        showScratchCardConfigPopup = false
        resultMessage = """
        刮刮卡配置已保存：
        刮刮卡数量：\(configData.cardCount)
        消耗积分：\(configData.costPerPlay)
        奖品：\(configData.cardPrizes.joined(separator: "、"))
        """
    }
}

#Preview {
    LotteryConfigTestView()
        .environmentObject(DataManager.shared)
}
