//
//  LotteryWheelTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//  大转盘功能测试页面
//

import SwiftUI

/**
 * 大转盘测试页面
 * 用于测试大转盘功能的完整性和用户体验
 */
struct LotteryWheelTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var showLotteryWheel = false
    @State private var testMember: Member?
    @State private var resultMessage = ""
    @State private var showResult = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                Text("大转盘功能测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 40)
                
                // 说明文字
                VStack(spacing: 12) {
                    Text("测试说明:")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text("1. 点击下方按钮创建测试成员和大转盘配置")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text("2. 测试成员将获得100积分用于抽奖")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text("3. 大转盘包含8个奖品分区")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .multilineTextAlignment(.leading)
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 测试按钮
                VStack(spacing: 20) {
                    // 创建测试数据按钮
                    Button(action: createTestData) {
                        HStack(spacing: 12) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 20))
                            
                            Text("创建测试数据")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(width: 200, height: 50)
                        .background(
                            LinearGradient(
                                colors: [DesignSystem.Colors.primary, DesignSystem.Colors.primary.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(25)
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                    }
                    
                    // 开始测试按钮
                    Button(action: startTest) {
                        HStack(spacing: 12) {
                            Image(systemName: "play.circle.fill")
                                .font(.system(size: 20))
                            
                            Text("开始大转盘测试")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(width: 200, height: 50)
                        .background(
                            LinearGradient(
                                colors: [Color(hex: "#FF6B6B"), Color(hex: "#FF8E8E")],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(25)
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                    }
                    .disabled(testMember == nil)
                    .opacity(testMember == nil ? 0.6 : 1.0)
                }
                
                // 结果显示
                if showResult {
                    VStack(spacing: 8) {
                        Text("测试结果:")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text(resultMessage)
                            .font(.system(size: 14))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                    .padding(.vertical, 16)
                    .background(Color(hex: "#f8f8f8"))
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                }
                
                Spacer()
            }
            .navigationTitle("大转盘测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        // 大转盘页面
        .fullScreenCover(isPresented: $showLotteryWheel) {
            if let member = testMember {
                NavigationView {
                    LotteryWheelView(
                        isPresented: $showLotteryWheel,
                        member: member,
                        onLotteryComplete: handleLotteryComplete,
                        onDismiss: {
                            showLotteryWheel = false
                        },
                        onNavigateToSettings: {
                            print("导航到设置页面")
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建测试数据
     */
    private func createTestData() {
        let context = dataManager.persistenceController.container.viewContext

        // 创建测试成员
        let member = Member(context: context)
        member.id = UUID()
        member.name = "测试成员"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -10, to: Date())
        member.memberNumber = 1
        member.currentPoints = 100  // 给予100积分用于测试
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = dataManager.currentUser

        // 创建大转盘配置
        let config = member.createOrUpdateLotteryConfig(
            toolType: LotteryConfig.ToolType.wheel,
            itemCount: 8,
            costPerPlay: 10,
            prizeNames: [
                "小红花", "贴纸", "糖果", "玩具车",
                "图书", "文具盒", "积木", "彩笔"
            ],
            in: context
        )

        // 保存数据
        dataManager.save()

        testMember = member
        resultMessage = "测试数据创建成功！\n成员：\(member.displayName)\n积分：\(member.currentPoints)\n大转盘奖品：\(config.allItems.count)个"
        showResult = true

        print("测试数据创建成功 - 成员: \(member.displayName), 积分: \(member.currentPoints)")
    }
    
    /**
     * 开始测试
     */
    private func startTest() {
        guard testMember != nil else { return }
        showLotteryWheel = true
    }
    
    /**
     * 处理抽奖完成
     */
    private func handleLotteryComplete(prizeName: String, cost: Int) {
        print("抽奖完成 - 奖品: \(prizeName), 消耗积分: \(cost)")
        
        // 更新结果消息
        if let member = testMember {
            resultMessage = "抽奖成功！\n获得奖品：\(prizeName)\n消耗积分：\(cost)\n剩余积分：\(member.currentPoints)"
            showResult = true
        }
        
        // 成功触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
}

// MARK: - Preview
#Preview {
    LotteryWheelTestView()
        .environmentObject(DataManager.shared)
}
