//
//  WheelIntegrationTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 大转盘配置与动画集成测试视图
 * 用于测试配置数据与大转盘动画的完整集成效果
 */
struct WheelIntegrationTestView: View {
    
    @EnvironmentObject var dataManager: DataManager
    
    @State private var selectedMember: Member?
    @State private var showWheelConfig = false
    @State private var showWheelLottery = false
    @State private var testResults: [String] = []
    @State private var currentStep = 0
    
    private let testSteps = [
        "选择测试成员",
        "配置大转盘",
        "验证配置加载",
        "进行抽奖测试",
        "检查抽奖记录"
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("大转盘集成测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 测试步骤指示器
                testStepsIndicator
                
                // 成员选择
                memberSelectionSection
                
                // 测试操作按钮
                testActionsSection
                
                // 测试结果显示
                testResultsSection
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationBarHidden(true)
        }
        .overlay(
            // 大转盘配置弹窗
            WheelConfigPopupView(
                isPresented: $showWheelConfig,
                selectedMember: selectedMember,
                onSave: { configData in
                    handleConfigSave(configData)
                },
                onCancel: {
                    showWheelConfig = false
                    addTestResult("❌ 用户取消了配置")
                }
            )
        )
        .fullScreenCover(isPresented: $showWheelLottery) {
            if let member = selectedMember {
                LotteryWheelView(
                    isPresented: $showWheelLottery,
                    member: member,
                    onLotteryComplete: { prize, cost in
                        handleLotteryComplete(prize: prize, cost: cost)
                    },
                    onDismiss: {
                        showWheelLottery = false
                    },
                    onNavigateToSettings: {
                        showWheelLottery = false
                        showWheelConfig = true
                    }
                )
            }
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 测试步骤指示器
     */
    private var testStepsIndicator: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("测试步骤")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(spacing: 4) {
                ForEach(Array(testSteps.enumerated()), id: \.offset) { index, step in
                    HStack {
                        Circle()
                            .fill(index <= currentStep ? Color(hex: "#a9d051") : Color.gray.opacity(0.3))
                            .frame(width: 12, height: 12)
                        
                        Text("\(index + 1). \(step)")
                            .font(.system(size: 14))
                            .foregroundColor(index <= currentStep ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                        
                        Spacer()
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(hex: "#f8ffe5"))
            .cornerRadius(8)
        }
    }
    
    /**
     * 成员选择区域
     */
    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("选择测试成员")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            if dataManager.members.isEmpty {
                Text("请先在首页添加成员")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding()
                    .background(Color(hex: "#f8ffe5"))
                    .cornerRadius(8)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(dataManager.members, id: \.objectID) { member in
                            memberCard(member: member)
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
        }
    }
    
    /**
     * 成员卡片
     */
    private func memberCard(member: Member) -> some View {
        Button(action: {
            selectedMember = member
            currentStep = max(currentStep, 0)
            addTestResult("✅ 选择成员: \(member.displayName) (积分: \(member.currentPoints))")
        }) {
            VStack(spacing: 8) {
                Image(member.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                
                Text(member.displayName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                
                Text("\(member.currentPoints)分")
                    .font(.system(size: 10))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                selectedMember?.objectID == member.objectID ? 
                Color(hex: "#a9d051").opacity(0.2) : Color(hex: "#f8ffe5")
            )
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        selectedMember?.objectID == member.objectID ? 
                        Color(hex: "#a9d051") : Color.clear, 
                        lineWidth: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /**
     * 测试操作区域
     */
    private var testActionsSection: some View {
        VStack(spacing: 12) {
            Text("测试操作")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(spacing: 8) {
                testButton(
                    title: "配置大转盘",
                    icon: "gear",
                    enabled: selectedMember != nil,
                    action: openWheelConfig
                )
                
                testButton(
                    title: "检查配置",
                    icon: "doc.text.magnifyingglass",
                    enabled: selectedMember != nil,
                    action: checkConfiguration
                )
                
                testButton(
                    title: "开始抽奖",
                    icon: "play.circle",
                    enabled: selectedMember != nil && hasValidConfig(),
                    action: startLottery
                )
                
                testButton(
                    title: "查看抽奖记录",
                    icon: "list.bullet",
                    enabled: selectedMember != nil,
                    action: checkLotteryRecords
                )
                
                testButton(
                    title: "清空测试结果",
                    icon: "clear",
                    enabled: true,
                    action: clearResults
                )
            }
        }
    }
    
    /**
     * 测试按钮
     */
    private func testButton(title: String, icon: String, enabled: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(enabled ? .white : .gray)
                
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(enabled ? .white : .gray)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(enabled ? Color(hex: "#a9d051") : Color.gray.opacity(0.3))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!enabled)
    }
    
    /**
     * 测试结果区域
     */
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        testResultRow(index: index, result: result)
                    }
                }
            }
            .frame(maxHeight: 200)
            .background(Color(hex: "#f5f5f5"))
            .cornerRadius(8)
        }
    }
    
    /**
     * 测试结果行
     */
    private func testResultRow(index: Int, result: String) -> some View {
        let textColor = getResultColor(for: result)
        
        return Text("\(index + 1). \(result)")
            .font(.system(size: 12, design: .monospaced))
            .foregroundColor(textColor)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
    }
    
    /**
     * 获取结果文本颜色
     */
    private func getResultColor(for result: String) -> Color {
        if result.hasPrefix("✅") {
            return .green
        } else if result.hasPrefix("❌") {
            return .red
        } else if result.hasPrefix("📥") {
            return .blue
        } else if result.hasPrefix("🎉") {
            return .orange
        } else {
            return DesignSystem.Colors.textSecondary
        }
    }
    
    // MARK: - 测试方法
    
    /**
     * 打开大转盘配置
     */
    private func openWheelConfig() {
        showWheelConfig = true
        addTestResult("📱 打开大转盘配置界面")
    }
    
    /**
     * 检查配置
     */
    private func checkConfiguration() {
        guard let member = selectedMember else { return }
        
        if let config = DataManager.shared.getWheelConfig(for: member) {
            currentStep = max(currentStep, 2)
            addTestResult("📥 找到配置: 分区数=\(config.itemCount), 积分=\(config.costPerPlay)")
            
            let items = config.allItems
            for item in items {
                addTestResult("   - 分区\(item.itemIndex + 1): \(item.formattedPrizeName)")
            }
        } else {
            addTestResult("❌ 未找到配置，请先配置大转盘")
        }
    }
    
    /**
     * 开始抽奖
     */
    private func startLottery() {
        guard hasValidConfig() else {
            addTestResult("❌ 配置无效，无法开始抽奖")
            return
        }
        
        showWheelLottery = true
        addTestResult("🎮 启动大转盘抽奖界面")
    }
    
    /**
     * 检查抽奖记录
     */
    private func checkLotteryRecords() {
        guard let member = selectedMember else { return }
        
        let records = member.lotteryRecords?.allObjects as? [LotteryRecord] ?? []
        let wheelRecords = records.filter { $0.toolType == "wheel" }
        
        if wheelRecords.isEmpty {
            addTestResult("📝 暂无大转盘抽奖记录")
        } else {
            addTestResult("📋 找到 \(wheelRecords.count) 条大转盘抽奖记录:")
            for record in wheelRecords.sorted(by: { $0.timestamp ?? Date() > $1.timestamp ?? Date() }) {
                let timeStr = DateFormatter().apply {
                    $0.dateFormat = "MM-dd HH:mm"
                }.string(from: record.timestamp ?? Date())
                addTestResult("   - [\(timeStr)] \(record.prizeResult ?? "未知") (消耗\(record.cost)分)")
            }
        }
    }
    
    /**
     * 清空结果
     */
    private func clearResults() {
        testResults.removeAll()
        currentStep = 0
    }
    
    /**
     * 检查是否有有效配置
     */
    private func hasValidConfig() -> Bool {
        guard let member = selectedMember else { return false }
        return DataManager.shared.getWheelConfig(for: member) != nil
    }
    
    /**
     * 处理配置保存
     */
    private func handleConfigSave(_ configData: WheelConfigData) {
        showWheelConfig = false
        currentStep = max(currentStep, 1)
        
        addTestResult("✅ 配置保存成功")
        addTestResult("   - 分区数: \(configData.sectorCount)")
        addTestResult("   - 消耗积分: \(configData.costPerPlay)")
        addTestResult("   - 奖品: \(configData.sectorPrizes.joined(separator: ", "))")
    }
    
    /**
     * 处理抽奖完成
     */
    private func handleLotteryComplete(prize: String, cost: Int) {
        showWheelLottery = false
        currentStep = max(currentStep, 4)
        
        addTestResult("🎉 抽奖完成!")
        addTestResult("   - 中奖奖品: \(prize)")
        addTestResult("   - 消耗积分: \(cost)")
        
        // 刷新成员数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if let member = selectedMember {
                addTestResult("   - 剩余积分: \(member.currentPoints)")
            }
        }
    }
    
    /**
     * 添加测试结果
     */
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter().apply {
            $0.dateFormat = "HH:mm:ss"
        }.string(from: Date())
        
        testResults.append("[\(timestamp)] \(result)")
    }
}



#Preview {
    WheelIntegrationTestView()
        .environmentObject(DataManager.shared)
}
